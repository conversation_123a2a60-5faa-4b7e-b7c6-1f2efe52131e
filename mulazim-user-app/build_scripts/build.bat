@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM =============================================================================
REM 通用构建脚本 - 支持多环境、多平台 (Windows 版本)
REM 作者: Assistant
REM 版本: 1.0.0
REM 用法: build.bat [platform] [environment] [build_mode]
REM =============================================================================

REM 默认参数
set DEFAULT_PLATFORM=android
set DEFAULT_ENVIRONMENT=staging
set DEFAULT_BUILD_MODE=release

REM 应用信息
set APP_NAME=mulazim
for /f "tokens=2 delims=:" %%a in ('findstr "version:" pubspec.yaml') do (
    set VERSION_LINE=%%a
)
for /f "tokens=1 delims=+" %%a in ("!VERSION_LINE!") do (
    set APP_VERSION=%%a
)
for /f "tokens=2 delims=+" %%a in ("!VERSION_LINE!") do (
    set BUILD_NUMBER=%%a
)
set APP_VERSION=!APP_VERSION: =!
set BUILD_NUMBER=!BUILD_NUMBER: =!

REM 函数：显示帮助信息
:show_help
echo === Flutter 通用构建脚本 ===
echo 用法: build.bat [platform] [environment] [build_mode]
echo.
echo 参数说明:
echo   platform     - 构建平台 (android^|all)
echo   environment  - 环境 (development^|staging^|production)
echo   build_mode   - 构建模式 (debug^|profile^|release)
echo.
echo 示例:
echo   build.bat android staging release    # Android 测试环境 Release 版本
echo   build.bat all staging release        # 所有平台 测试环境 Release 版本
echo   build.bat                            # 使用默认参数
echo.
echo 支持的环境:
echo   development - 开发环境
echo   staging     - 测试环境
echo   production  - 生产环境
echo.
echo 输出文件:
echo   Android APK: build\outputs\
echo   iOS IPA:     build\outputs\ (需要 macOS 系统)
goto :eof

REM 函数：记录日志
:log
set level=%1
set message=%2
set timestamp=%date% %time%
echo [%level%] %timestamp% - %message%
goto :eof

REM 函数：检查参数
:validate_params
set platform=%1
set environment=%2
set build_mode=%3

if not "%platform%"=="android" if not "%platform%"=="all" (
    call :log "ERROR" "不支持的平台: %platform%"
    call :show_help
    exit /b 1
)

if not "%environment%"=="development" if not "%environment%"=="staging" if not "%environment%"=="production" (
    call :log "ERROR" "不支持的环境: %environment%"
    call :show_help
    exit /b 1
)

if not "%build_mode%"=="debug" if not "%build_mode%"=="profile" if not "%build_mode%"=="release" (
    call :log "ERROR" "不支持的构建模式: %build_mode%"
    call :show_help
    exit /b 1
)
goto :eof

REM 函数：准备构建环境
:prepare_build
set environment=%1

call :log "STEP" "准备构建环境..."

REM 确保在项目根目录
cd /d "%~dp0\.."

echo === 构建信息 ===
echo 应用名称: %APP_NAME%
echo 应用版本: %APP_VERSION%
echo 构建号: %BUILD_NUMBER%
echo 目标环境: %environment%
echo 构建时间: %date% %time%
echo.

REM 清理之前的构建
call :log "STEP" "清理之前的构建..."
flutter clean

REM 获取依赖
call :log "STEP" "获取项目依赖..."
flutter pub get

call :log "SUCCESS" "构建环境准备完成"
goto :eof

REM 函数：构建 Android APK
:build_android
set environment=%1
set build_mode=%2

call :log "STEP" "开始构建 Android APK..."

REM 构建 APK
set build_cmd=flutter build apk --%build_mode% --dart-define=ENV=%environment%
call :log "INFO" "执行命令: %build_cmd%"

%build_cmd%
if !errorlevel! equ 0 (
    call :log "SUCCESS" "Android APK 构建成功"
    call :handle_android_output "%environment%" "%build_mode%"
) else (
    call :log "ERROR" "Android APK 构建失败"
    exit /b 1
)
goto :eof

REM 函数：处理 Android 输出文件
:handle_android_output
set environment=%1
set build_mode=%2

REM 原始 APK 路径
set original_apk=build\app\outputs\flutter-apk\app-%build_mode%.apk

if exist "%original_apk%" (
    REM 创建输出目录
    if not exist "build\outputs" mkdir "build\outputs"
    
    REM 生成新文件名
    set new_apk_name=%APP_NAME%_%environment%_v%APP_VERSION%.apk
    set new_apk_path=build\outputs\!new_apk_name!
    
    REM 复制并重命名
    copy "%original_apk%" "!new_apk_path!"
    
    call :log "SUCCESS" "APK 文件已生成: !new_apk_path!"
    
    echo === 输出文件 ===
    echo 原始文件: %original_apk%
    echo 重命名文件: !new_apk_path!
    echo 绝对路径: %cd%\!new_apk_path!
    
    REM 打开文件所在目录
    explorer "build\outputs"
) else (
    call :log "ERROR" "未找到 APK 文件: %original_apk%"
)
goto :eof

REM 函数：显示构建摘要
:show_summary
set platform=%1
set environment=%2
set build_mode=%3

echo.
echo === 构建摘要 ===
echo 构建平台: %platform%
echo 目标环境: %environment%
echo 构建模式: %build_mode%
echo 完成时间: %date% %time%
echo.
echo ✅ 构建完成！
goto :eof

REM 主程序
:main
REM 解析参数
if "%1"=="" (
    set platform=%DEFAULT_PLATFORM%
) else (
    set platform=%1
)

if "%2"=="" (
    set environment=%DEFAULT_ENVIRONMENT%
) else (
    set environment=%2
)

if "%3"=="" (
    set build_mode=%DEFAULT_BUILD_MODE%
) else (
    set build_mode=%3
)

REM 处理帮助参数
if "%1"=="-h" call :show_help && exit /b 0
if "%1"=="--help" call :show_help && exit /b 0

REM 验证参数
call :validate_params "%platform%" "%environment%" "%build_mode%"
if !errorlevel! neq 0 exit /b 1

REM 显示开始信息
echo 🚀 开始构建 %platform% 平台的 %environment% 环境 %build_mode% 版本...
echo.

REM 准备构建环境
call :prepare_build "%environment%"

REM 执行构建
if "%platform%"=="android" (
    call :build_android "%environment%" "%build_mode%"
) else if "%platform%"=="all" (
    call :build_android "%environment%" "%build_mode%"
)

REM 显示构建摘要
call :show_summary "%platform%" "%environment%" "%build_mode%"

goto :eof

REM 检查是否在项目根目录
if not exist "pubspec.yaml" (
    call :log "ERROR" "请在 Flutter 项目根目录下运行此脚本"
    exit /b 1
)

REM 执行主函数
call :main %* 