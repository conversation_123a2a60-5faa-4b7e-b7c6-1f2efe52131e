#!/bin/bash

# =============================================================================
# 通用构建脚本 - 支持多环境、多平台
# 作者: Assistant
# 版本: 1.0.0
# 用法: ./build.sh [platform] [environment] [build_mode]
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认参数
DEFAULT_PLATFORM="android"
DEFAULT_ENVIRONMENT="staging"
DEFAULT_BUILD_MODE="release"

# 应用信息（从 pubspec.yaml 获取）
APP_NAME="mulazim"
VERSION_LINE=$(grep "version:" pubspec.yaml | head -1)
APP_VERSION=$(echo $VERSION_LINE | sed 's/version: //' | sed 's/+.*//')
BUILD_NUMBER=$(echo $VERSION_LINE | sed 's/.*+//')

# 函数：显示帮助信息
show_help() {
    echo -e "${CYAN}=== Flutter 通用构建脚本 ===${NC}"
    echo -e "${YELLOW}用法:${NC} ./build.sh [platform] [environment] [build_mode]"
    echo ""
    echo -e "${YELLOW}参数说明:${NC}"
    echo -e "  ${GREEN}platform${NC}     - 构建平台 (android|ios|all)"
    echo -e "  ${GREEN}environment${NC}  - 环境 (development|staging|production)"
    echo -e "  ${GREEN}build_mode${NC}   - 构建模式 (debug|profile|release)"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "  ${CYAN}./build.sh android staging release${NC}    # Android 测试环境 Release 版本"
    echo -e "  ${CYAN}./build.sh ios production release${NC}     # iOS 生产环境 Release 版本"
    echo -e "  ${CYAN}./build.sh all staging release${NC}        # 所有平台 测试环境 Release 版本"
    echo -e "  ${CYAN}./build.sh${NC}                            # 使用默认参数 (android staging release)"
    echo ""
    echo -e "${YELLOW}支持的环境:${NC}"
    echo -e "  ${GREEN}development${NC} - 开发环境"
    echo -e "  ${GREEN}staging${NC}     - 测试环境"
    echo -e "  ${GREEN}production${NC}  - 生产环境"
    echo ""
    echo -e "${YELLOW}输出文件:${NC}"
    echo -e "  ${GREEN}Android APK:${NC} build/outputs/"
    echo -e "  ${GREEN}iOS IPA:${NC}     build/outputs/"
}

# 函数：记录日志
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} ${timestamp} - $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} ${timestamp} - $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "STEP")
            echo -e "${PURPLE}[STEP]${NC} ${timestamp} - $message"
            ;;
    esac
}

# 函数：检查参数
validate_params() {
    local platform=$1
    local environment=$2
    local build_mode=$3
    
    # 验证平台
    if [[ ! "$platform" =~ ^(android|ios|all)$ ]]; then
        log "ERROR" "不支持的平台: $platform"
        show_help
        exit 1
    fi
    
    # 验证环境
    if [[ ! "$environment" =~ ^(development|staging|production)$ ]]; then
        log "ERROR" "不支持的环境: $environment"
        show_help
        exit 1
    fi
    
    # 验证构建模式
    if [[ ! "$build_mode" =~ ^(debug|profile|release)$ ]]; then
        log "ERROR" "不支持的构建模式: $build_mode"
        show_help
        exit 1
    fi
    
    # iOS 平台检查
    if [[ "$platform" == "ios" || "$platform" == "all" ]] && [[ "$OSTYPE" != "darwin"* ]]; then
        log "WARNING" "iOS 构建需要在 macOS 系统上进行"
        if [[ "$platform" == "ios" ]]; then
            exit 1
        fi
    fi
}

# 函数：准备构建环境
prepare_build() {
    local environment=$1
    
    log "STEP" "准备构建环境..."
    
    # 确保在项目根目录
    cd "$(dirname "$0")/.."
    
    # 显示构建信息
    echo -e "${CYAN}=== 构建信息 ===${NC}"
    echo -e "${YELLOW}应用名称:${NC} $APP_NAME"
    echo -e "${YELLOW}应用版本:${NC} $APP_VERSION"
    echo -e "${YELLOW}构建号:${NC} $BUILD_NUMBER"
    echo -e "${YELLOW}目标环境:${NC} $environment"
    echo -e "${YELLOW}构建时间:${NC} $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    # 清理之前的构建
    log "STEP" "清理之前的构建..."
    flutter clean
    
    # 获取依赖
    log "STEP" "获取项目依赖..."
    flutter pub get
    
    log "SUCCESS" "构建环境准备完成"
}

# 函数：构建 Android APK
build_android() {
    local environment=$1
    local build_mode=$2
    
    log "STEP" "开始构建 Android APK..."
    
    # 构建 APK
    local build_cmd="flutter build apk --$build_mode --dart-define=ENV=$environment"
    log "INFO" "执行命令: $build_cmd"
    
    if eval $build_cmd; then
        log "SUCCESS" "Android APK 构建成功"
        
        # 处理输出文件
        handle_android_output "$environment" "$build_mode"
    else
        log "ERROR" "Android APK 构建失败"
        exit 1
    fi
}

# 函数：构建 iOS IPA
build_ios() {
    local environment=$1
    local build_mode=$2
    
    log "STEP" "开始构建 iOS IPA..."
    
    # 使用 flutter build ipa 命令
    local build_cmd="flutter build ipa --$build_mode --dart-define=ENV=$environment"
    log "INFO" "执行命令: $build_cmd"
    
    if eval $build_cmd; then
        log "SUCCESS" "iOS IPA 构建成功"
        
        # 处理输出的 IPA 文件
        handle_ios_output "$environment" "$build_mode"
    else
        log "ERROR" "iOS IPA 构建失败"
        exit 1
    fi
}

# 函数：处理 iOS 输出文件
handle_ios_output() {
    local environment=$1
    local build_mode=$2
    
    log "STEP" "处理 iOS IPA 文件..."
    
    # 可能的 IPA 文件路径（按优先级排序）
    local possible_ipas=(
        "build/ios/ipa/user_app.ipa"
        "build/ios/ipa/Runner.ipa"
        "build/ios/ipa/mulazim.ipa"
        "build/ios/ipa/*.ipa"
    )
    
    local original_ipa=""
    
    # 查找第一个存在的 IPA 文件
    for ipa_path in "${possible_ipas[@]}"; do
        if [[ "$ipa_path" == *"*"* ]]; then
            # 通配符路径，查找匹配的文件
            for file in $ipa_path; do
                if [[ -f "$file" ]]; then
                    original_ipa="$file"
                    break 2
                fi
            done
        elif [[ -f "$ipa_path" ]]; then
            original_ipa="$ipa_path"
            break
        fi
    done
    
    if [[ -n "$original_ipa" ]]; then
        # 创建输出目录
        mkdir -p "build/outputs"
        
        # 生成新文件名
        local new_ipa_name="${APP_NAME}_${environment}_v${APP_VERSION}.ipa"
        local new_ipa_path="build/outputs/$new_ipa_name"
        
        # 复制并重命名
        cp "$original_ipa" "$new_ipa_path"
        
        log "SUCCESS" "IPA 文件已生成: $new_ipa_path"
        
        # 显示文件信息
        local file_size=$(du -h "$new_ipa_path" | cut -f1)
        log "INFO" "IPA 文件大小: $file_size"
        
        # 显示文件路径
        echo -e "${CYAN}=== iOS 输出文件 ===${NC}"
        echo -e "${GREEN}原始文件:${NC} $original_ipa"
        echo -e "${GREEN}重命名文件:${NC} $new_ipa_path"
        echo -e "${GREEN}绝对路径:${NC} $(realpath "$new_ipa_path")"
        
        # 打开文件所在目录
        open_directory "build/outputs"
    else
        log "ERROR" "未找到 Flutter 构建的 IPA 文件"
        log "INFO" "查找的路径包括:"
        for ipa_path in "${possible_ipas[@]}"; do
            log "INFO" "  - $ipa_path"
        done
        log "INFO" "请检查构建是否成功，或者手动查看 build/ios/ipa/ 目录"
        
        # 显示实际的 IPA 目录内容
        if [[ -d "build/ios/ipa" ]]; then
            log "INFO" "build/ios/ipa/ 目录内容:"
            ls -la "build/ios/ipa/" | while read line; do
                log "INFO" "  $line"
            done
        fi
    fi
}

# 函数：处理 Android 输出文件
handle_android_output() {
    local environment=$1
    local build_mode=$2
    
    # 原始 APK 路径
    local original_apk="build/app/outputs/flutter-apk/app-$build_mode.apk"
    
    if [[ -f "$original_apk" ]]; then
        # 创建输出目录
        mkdir -p "build/outputs"
        
        # 生成新文件名
        local new_apk_name="${APP_NAME}_${environment}_v${APP_VERSION}.apk"
        local new_apk_path="build/outputs/$new_apk_name"
        
        # 复制并重命名
        cp "$original_apk" "$new_apk_path"
        
        log "SUCCESS" "APK 文件已生成: $new_apk_path"
        
        # 显示文件信息
        local file_size=$(du -h "$new_apk_path" | cut -f1)
        log "INFO" "APK 文件大小: $file_size"
        
        # 显示文件路径
        echo -e "${CYAN}=== 输出文件 ===${NC}"
        echo -e "${GREEN}原始文件:${NC} $original_apk"
        echo -e "${GREEN}重命名文件:${NC} $new_apk_path"
        echo -e "${GREEN}绝对路径:${NC} $(realpath "$new_apk_path")"
        
        # 打开文件所在目录
        open_directory "build/outputs"
    else
        log "ERROR" "未找到 APK 文件: $original_apk"
    fi
}

# 函数：打开文件所在目录
open_directory() {
    local dir_path=$1
    
    log "INFO" "正在打开文件所在目录..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$dir_path"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v xdg-open &> /dev/null; then
            xdg-open "$dir_path"
        elif command -v nautilus &> /dev/null; then
            nautilus "$dir_path"
        else
            log "WARNING" "无法自动打开目录，请手动打开: $(realpath "$dir_path")"
        fi
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
        # Windows
        explorer "$dir_path"
    else
        log "WARNING" "无法自动打开目录，请手动打开: $(realpath "$dir_path")"
    fi
}

# 函数：显示构建摘要
show_summary() {
    local platform=$1
    local environment=$2
    local build_mode=$3
    local start_time=$4
    local end_time=$5
    
    local duration=$((end_time - start_time))
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))
    
    echo ""
    echo -e "${CYAN}=== 构建摘要 ===${NC}"
    echo -e "${YELLOW}构建平台:${NC} $platform"
    echo -e "${YELLOW}目标环境:${NC} $environment"
    echo -e "${YELLOW}构建模式:${NC} $build_mode"
    echo -e "${YELLOW}构建时间:${NC} ${minutes}分${seconds}秒"
    echo -e "${YELLOW}完成时间:${NC} $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    echo -e "${GREEN}✅ 构建完成！${NC}"
}

# 主函数
main() {
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 解析参数
    local platform=${1:-$DEFAULT_PLATFORM}
    local environment=${2:-$DEFAULT_ENVIRONMENT}
    local build_mode=${3:-$DEFAULT_BUILD_MODE}
    
    # 处理帮助参数
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    # 验证参数
    validate_params "$platform" "$environment" "$build_mode"
    
    # 显示开始信息
    echo -e "${CYAN}🚀 开始构建 $platform 平台的 $environment 环境 $build_mode 版本...${NC}"
    echo ""
    
    # 准备构建环境
    prepare_build "$environment"
    
    # 执行构建
    case $platform in
        "android")
            build_android "$environment" "$build_mode"
            ;;
        "ios")
            build_ios "$environment" "$build_mode"
            ;;
        "all")
            build_android "$environment" "$build_mode"
            echo ""
            if [[ "$OSTYPE" == "darwin"* ]]; then
                build_ios "$environment" "$build_mode"
            else
                log "WARNING" "跳过 iOS 构建（需要 macOS 系统）"
            fi
            ;;
    esac
    
    # 记录结束时间
    local end_time=$(date +%s)
    
    # 显示构建摘要
    show_summary "$platform" "$environment" "$build_mode" "$start_time" "$end_time"
}

# 检查是否在项目根目录
if [[ ! -f "pubspec.yaml" ]]; then
    log "ERROR" "请在 Flutter 项目根目录下运行此脚本"
    exit 1
fi

# 执行主函数
main "$@" 