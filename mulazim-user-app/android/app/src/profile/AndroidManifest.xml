<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    
    <application>
        <!-- 高德地图API密钥 -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="3c2d8bc0add227e536a43db754f23db1" />
        
        <!-- 高德地图服务 -->
        <service android:name="com.amap.api.location.APSService" />
    </application>
</manifest>
