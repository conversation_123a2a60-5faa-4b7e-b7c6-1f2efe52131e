package com.almas.dinner.map_line_view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;

import com.almas.dinner.R;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.LatLngBounds;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.PolylineOptions;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.route.BusRouteResult;
import com.amap.api.services.route.DriveRouteResult;
import com.amap.api.services.route.RidePath;
import com.amap.api.services.route.RideRouteResult;
import com.amap.api.services.route.RideStep;
import com.amap.api.services.route.RouteSearch;
import com.amap.api.services.route.WalkRouteResult;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

class MapLineView implements PlatformView, RouteSearch.OnRouteSearchListener{
    private MapView mapView;
    private Context context;
    private AMap aMap;
    private LatLng startPoint; // 起点经纬度
    private LatLng endPoint; // 终点经纬度
    private Marker startMarker; // 起点标记
    private Marker endMarker; // 终点标记
    private List<LatLng> routePoints; // 路线坐标列表
    private View view;
    RouteSearch routeSearch;


    public MapLineView(Context context, Map<String, Object> creationParams) {
        this.context = context;
        MapsInitializer.updatePrivacyShow(context,true,true);
        MapsInitializer.updatePrivacyAgree(context,true);
        
        double startLatPoint = 0.0;
        if (creationParams.get("user_latitude") != null) {
            try {
                startLatPoint = Double.parseDouble(creationParams.get("user_latitude").toString());
            } catch (NumberFormatException e) {
                // 处理解析异常，例如给startLatPoint赋默认值或者显示错误信息
                startLatPoint = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }


        double startLonPoint = 0.0;
        if(creationParams.get("user_longitude") != null){
            try {
                startLonPoint =  Double.parseDouble(creationParams.get("user_longitude").toString());
            }catch (NumberFormatException e){
                startLonPoint = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }


        double endLatPoint = 0.0;
        if(creationParams.get("store_latitude") != null){
            try{
                endLatPoint =  Double.parseDouble(creationParams.get("store_latitude").toString());
            }catch(NumberFormatException e){
                endLatPoint = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }


        double endLonPoint = 0.0;
        if(creationParams.get("store_longitude") != null){
            try{
                endLonPoint =  Double.parseDouble(creationParams.get("store_longitude").toString());
            }catch (NumberFormatException e){
                endLonPoint = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        String restaurantLogo = "";
        if(creationParams.get("restaurant_logo") != null){
            try{
                restaurantLogo =  creationParams.get("restaurant_logo").toString();
            }catch (NumberFormatException e){
                restaurantLogo = ""; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        view = LayoutInflater.from(context).inflate(R.layout.amap_motor_route_view, null);

        mapView = view.findViewById(R.id.map_view);
        mapView.onCreate(null);

        aMap = mapView.getMap();
        // 设置起点和终点经纬度
//        startPoint = new LatLng(43.768732, 87.625937); // 起点坐标
//        endPoint = new LatLng(43.715001, 87.663937); // 终点坐标
        startPoint = new LatLng(startLatPoint, startLonPoint);
        endPoint = new LatLng(endLatPoint, endLonPoint);

        // 创建一个 LatLngBounds.Builder 对象
        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        // 将两个点的经纬度添加到 builder 中，用于创建矩形区域
        builder.include(startPoint);
        builder.include(endPoint);
        // 创建包含两个点的矩形区域
        LatLngBounds bounds = builder.build();

        aMap.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 112));

        Log.e("restaurantLogo:",restaurantLogo);

        // 添加起点和终点标记
        startMarker = aMap.addMarker(new MarkerOptions().position(startPoint).icon(BitmapDescriptorFactory.fromResource(R.drawable.maporder)));
//        endMarker = aMap.addMarker(loadDynamicImageAndAddMarker(restaurantLogo));
        loadDynamicImageAndAddMarker(restaurantLogo, new MarkerLoadCallback() {
            @Override
            public void onMarkerLoaded(MarkerOptions markerOptions) {
                endMarker = aMap.addMarker(markerOptions);
            }
        });



        UiSettings uiSettings = aMap.getUiSettings();
        // 设置是否启用缩放控件
        uiSettings.setZoomControlsEnabled(false);
        uiSettings.setCompassEnabled(true);
        uiSettings.setScaleControlsEnabled(true);
        // 初始化路线坐标列表
        routePoints = new ArrayList<>();
        try{
            routeSearch = new RouteSearch(context);
        }catch (Exception e){
            Log.e("routeSearch exception:",e.toString()+"");
        }
        if(routeSearch != null){
            routeSearch.setRouteSearchListener(this); // 设置路径规划监听器
            RouteSearch.FromAndTo fromAndTo = new RouteSearch.FromAndTo(
                    new LatLonPoint(startPoint.latitude, startPoint.longitude),
                    new LatLonPoint(endPoint.latitude, endPoint.longitude)
            );
            RouteSearch.RideRouteQuery query = new RouteSearch.RideRouteQuery(fromAndTo);
            routeSearch.calculateRideRouteAsyn(query); // 异步计算骑行路径
        }

    }

//    MarkerOptions loadDynamicImageAndAddMarker(String imageUrl) {
//        MarkerOptions markerOptions = new MarkerOptions();
//        Glide.with(context)
//                .asBitmap()
//                .load(imageUrl)
//                .into(new SimpleTarget<Bitmap>() {
//                    @Override
//                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
//
//                        Bitmap scaledBitmap = Bitmap.createScaledBitmap(resource, 200, 200, false);
//                        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(scaledBitmap);
//                        markerOptions.position(endPoint).icon(bitmapDescriptor);
//                    }
//                });
//        return markerOptions;
//    }


    private void loadDynamicImageAndAddMarker(String imageUrl, MarkerLoadCallback callback) {
        Glide.with(context)
                .asBitmap()
                .load(imageUrl)
                .into(new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        Bitmap scaledBitmap = Bitmap.createScaledBitmap(resource, 80, 80, false);
                        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(scaledBitmap);
                        MarkerOptions markerOptions = new MarkerOptions()
                                .position(endPoint)
                                .icon(bitmapDescriptor);
                        callback.onMarkerLoaded(markerOptions);
                    }
                });
    }

    public interface MarkerLoadCallback {
        void onMarkerLoaded(MarkerOptions markerOptions);
    }


    @Nullable
    @org.jetbrains.annotations.Nullable
    @Override
    public View getView() {
        return view;
    }

    @Override
    public void onBusRouteSearched(BusRouteResult busRouteResult, int i) {}

    @Override
    public void onDriveRouteSearched(DriveRouteResult driveRouteResult, int i) {}

    @Override
    public void onWalkRouteSearched(WalkRouteResult walkRouteResult, int i) {}

    @Override
    public void onRideRouteSearched(RideRouteResult rideRouteResult, int i) {
        if (i == 1000 && rideRouteResult != null && rideRouteResult.getPaths() != null
                && rideRouteResult.getPaths().size() > 0) {
            // 获取第一条路径
            RidePath ridePath = rideRouteResult.getPaths().get(0);
            // 遍历路径中的所有步骤，将坐标添加到路线坐标列表中
            List<RideStep> steps = ridePath.getSteps();
            for(RideStep myStep : steps){
                List<LatLonPoint> pathPoints = myStep.getPolyline();
                for (LatLonPoint point : pathPoints) {
                    routePoints.add(new LatLng(point.getLatitude(), point.getLongitude()));
                }
            }
            Log.e("routePoints length:",routePoints.size()+"");
            aMap.addPolyline(new PolylineOptions().addAll(routePoints).color(Color.rgb(45, 122, 255))
                    .width(15f).setDottedLine(false));
            mapView.invalidate();
        }
    }



    @Override
    public void dispose() {
        // 清除地图相关资源
        mapView.onDestroy();

        // 清除标记物相关资源
        startMarker.remove();
        endMarker.remove();

        // 清除路线相关资源
        routePoints.clear();

        // 移除路径规划监听器
        routeSearch.setRouteSearchListener(null);
    }
}