package com.almas.dinner.choice_map_view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.almas.dinner.R;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.LatLngBounds;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiSearch;
import com.amap.api.services.route.BusRouteResult;
import com.amap.api.services.route.DriveRouteResult;
import com.amap.api.services.route.RideRouteResult;
import com.amap.api.services.route.RouteSearch;
import com.amap.api.services.route.WalkRouteResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.Log;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

class ChoicePositionView implements PlatformView, RouteSearch.OnRouteSearchListener, MethodChannel.MethodCallHandler, AMap.OnCameraChangeListener, PoiSearch.OnPoiSearchListener {
    private MapView mapView;
    private Context context;
    private AMap aMap;
    private View view;
    private final MethodChannel methodChannel;
    private LatLng initialLatLng;
    private ProgressBar progressBar; // 圆形加载指示器
    private Marker shipperMarker;

    private LatLng centerLatLng; // 地图中心点的经纬度

    private PoiSearch poiSearch; // POI 搜索对象

    public ChoicePositionView(Context context, Map<String, Object> creationParams, BinaryMessenger messenger) throws AMapException {
        this.context = context;
        methodChannel = new MethodChannel(messenger, "choice_position_channel");
        methodChannel.setMethodCallHandler(this);
        MapsInitializer.updatePrivacyShow(context,true,true);
        MapsInitializer.updatePrivacyAgree(context,true);
        // 从 Flutter 端接收传递的初始定位数据
        double initialLatitude = (double) creationParams.get("initial_latitude");
        double initialLongitude = (double) creationParams.get("initial_longitude");
        initialLatLng = new LatLng(initialLatitude, initialLongitude);

        view = LayoutInflater.from(context).inflate(R.layout.amap_motor_route_view, null);
        progressBar = view.findViewById(R.id.progress_bar); // 获取 ProgressBar 实例
        mapView = view.findViewById(R.id.map_view);
        mapView.onCreate(null);
        mapView.onResume();      // MapView 恢复
        if (mapView != null) {
            mapView.onResume();
        }
        if (aMap == null) {
            aMap = mapView.getMap();
            aMap.getUiSettings().setZoomControlsEnabled(true);
            aMap.setOnCameraChangeListener(this); // 设置相机变化监听
            // 初次打开页面时将相机移动到传入的初始定位数据
            moveCameraToInitialPosition();
        }
        UiSettings uiSettings = aMap.getUiSettings();
        // 设置是否启用缩放控件
        uiSettings.setZoomControlsEnabled(false);
        uiSettings.setCompassEnabled(true);
        uiSettings.setScaleControlsEnabled(true);

        shipperMarker = aMap.addMarker(new MarkerOptions().position(initialLatLng).icon(getScaledMarkerIcon(context, R.drawable.maporder, 66, 80)));

        // 初次设置中心点为传递的初始定位数据
        moveCameraToInitialPosition();
    }

    private BitmapDescriptor getScaledMarkerIcon(Context context, int drawableResId, int width, int height) {
        // 加载原始Bitmap
        Bitmap originalBitmap = BitmapFactory.decodeResource(context.getResources(), drawableResId);

        // 缩放Bitmap
        Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, width, height, false);

        // 将缩放后的Bitmap转换为BitmapDescriptor
        return BitmapDescriptorFactory.fromBitmap(scaledBitmap);
    }
    private void moveCameraToInitialPosition() {
        if (initialLatLng != null) {
            // 慢慢移动相机到初始位置
            aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(initialLatLng, 15f), 2000, null);
        }
    }
    @Nullable
    @org.jetbrains.annotations.Nullable
    @Override
    public View getView() {
        return view;
    }
    @Override
    public void onBusRouteSearched(BusRouteResult busRouteResult, int i) {}
    @Override
    public void onDriveRouteSearched(DriveRouteResult driveRouteResult, int i) {}
    public void onWalkRouteSearched(WalkRouteResult walkRouteResult, int i) {}
    @Override
    public void onRideRouteSearched(RideRouteResult rideRouteResult, int i) {}

    @Override
    public void dispose() {
        shipperMarker.remove();
        // 清除地图相关资源
        mapView.onDestroy();
    }
    @Override
    public void onMethodCall(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {
        if (methodCall.method.equals("changePositionItem")) {
            Map<String, Object> positionParam = methodCall.arguments();
            // 从 Flutter 端接收传递的初始定位数据
            double changedLatitude = (double) positionParam.get("latitude");
            double changedLongitude = (double) positionParam.get("longitude");

            LatLng newPosition = new LatLng(changedLatitude, changedLongitude);
            
            // 在主线程上移动摄像机，使用简单的缩放而不是边界计算
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.post(() -> {
                try {
                    // 使用 newLatLngZoom 而不是 newLatLngBounds，避免边界计算问题
                    aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(newPosition, 15f), 1000, null);
                    
                    // 更新标记位置
                    if (shipperMarker != null) {
                        shipperMarker.setPosition(newPosition);
                    }
                    
                    hideLoading(); // 移动完成后隐藏加载指示器
                } catch (Exception e) {
                    Log.e("ChoicePositionView", "Error updating map position: " + e.getMessage());
                }
            });
            result.success(null);
        } else if (methodCall.method.equals("searchPOIs")){
            // 接收从 Flutter 传递的搜索关键字
            String keyword = methodCall.arguments();
            try {
                searchPOIsByKeyword(keyword); // 根据关键字进行 POI 搜索
            } catch (AMapException e) {
                throw new RuntimeException(e);
            }
            result.success(null);
        } else if (methodCall.method.equals("myPosition")){
            Map<String, Object> positionParam = methodCall.arguments();
            double initialLatitude = (double) positionParam.get("initial_latitude");
            double initialLongitude = (double) positionParam.get("initial_longitude");
            LatLngBounds.Builder boundsBuilder = new LatLngBounds.Builder();
            boundsBuilder.include(new LatLng(initialLatitude, initialLongitude));
            LatLngBounds bounds = boundsBuilder.build();
            // 在主线程上移动摄像机
            Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.post(() -> {
                aMap.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 190));
                hideLoading(); // 路径绘制完成后隐藏加载指示器
            });
            result.success(null);
        }


        else {
            result.notImplemented();
        }
    }
    // 显示加载指示器
    private void showLoading() {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (progressBar != null && progressBar.getVisibility() == View.GONE) {
                progressBar.setVisibility(View.VISIBLE);
            }
        });
    }

    // 隐藏加载指示器
    private void hideLoading() {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (progressBar != null && progressBar.getVisibility() == View.VISIBLE) {
                progressBar.setVisibility(View.GONE);
            }
        });
    }


    @Override
    public void onCameraChange(CameraPosition cameraPosition) {
        // 当相机位置发生变化时，更新中心点位置
        centerLatLng = cameraPosition.target;
    }

    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        // 相机移动结束时，更新中心点位置
        centerLatLng = cameraPosition.target;
        // 开始进行逆地理编码查询，获取当前位置的详细地址
        try {
            searchNearbyPOIs(centerLatLng, 3000);
        } catch (AMapException e) {
            throw new RuntimeException(e);
        }
        // 将当前中心点的坐标通过MethodChannel传递给Flutter层
        Map<String, Double> position = new HashMap<>();
        position.put("latitude", centerLatLng.latitude);
        position.put("longitude", centerLatLng.longitude);
        methodChannel.invokeMethod("updateCenterPosition", position);
//        Handler mainHandler = new Handler(Looper.getMainLooper());
//        mainHandler.post(() -> {
//            //打印定位信息----------------------
//
//        });
    }

    @Override
    public void onPoiSearched(PoiResult poiResult, int i) {
        if (i == AMapException.CODE_AMAP_SUCCESS) {
            if (poiResult != null && poiResult.getPois() != null) {
                List<PoiItem> poiItems = poiResult.getPois();
                List<Map<String, Object>> poiList = new ArrayList<>();

                // 遍历 POI 项目并构建要传递回 Flutter 的数据
                for (PoiItem poi : poiItems) {
                    Map<String, Object> poiMap = new HashMap<>();
                    poiMap.put("name", poi.getTitle()); // POI 名称
                    poiMap.put("address", poi.getSnippet()); // POI 地址
                    poiMap.put("latitude", poi.getLatLonPoint().getLatitude());
                    poiMap.put("longitude", poi.getLatLonPoint().getLongitude());
                    // 计算 POI 到地图中心的距离，并将其存储到 POI 数据中
                    double distanceToCenter = calculateDistance(centerLatLng, poi.getLatLonPoint()) / 1000.0;// 转换为公里
                    poiMap.put("distance", distanceToCenter); // 存储距离信息（单位：公里）
                    poiList.add(poiMap);
                }

                // 通过 MethodChannel 将 POI 数据传递回 Flutter 层
                methodChannel.invokeMethod("updatePOIs", poiList);
                Log.d("PositionView", "成功获取 POI 信息: " + poiList.size() + " 个项目");
            }
        } else {
            Log.e("PositionView", "POI 搜索失败，错误码: " + i);
        }
    }


    // 计算 POI 到地图中心点或原位置的距离（单位：米）
    private double calculateDistance(LatLng startLatLng, LatLonPoint endLatLonPoint) {
        float[] results = new float[1];
        android.location.Location.distanceBetween(startLatLng.latitude, startLatLng.longitude,
                endLatLonPoint.getLatitude(), endLatLonPoint.getLongitude(), results);
        return results[0];
    }

    @Override
    public void onPoiItemSearched(PoiItem poiItem, int i) {}

    // 根据指定范围进行 POI 搜索，按距离排序
    private void searchNearbyPOIs(LatLng latLng, int radius) throws AMapException {
        LatLonPoint latLonPoint = new LatLonPoint(latLng.latitude, latLng.longitude);

        // 创建 POI 查询条件，第一个参数是搜索关键字，这里使用空字符串表示搜索所有类型
        PoiSearch.Query query = new PoiSearch.Query("", "", "");
        query.setPageSize(100); // 设置每次最大获取数量为100条（可以根据需要调整）
        query.setPageNum(0); // 当前页码，0 表示第一页

        // 创建 POI 搜索范围，第一个参数是中心点，第二个参数是搜索半径
        PoiSearch.SearchBound searchBound = new PoiSearch.SearchBound(latLonPoint, radius);

        // 创建 PoiSearch 对象，并设置查询条件和搜索范围
        poiSearch = new PoiSearch(context, query);
        poiSearch.setBound(searchBound); // 设置搜索范围
        poiSearch.setOnPoiSearchListener(this); // 设置搜索监听器
        poiSearch.searchPOIAsyn(); // 异步搜索
    }


    // 根据关键字进行 POI 搜索
    private void searchPOIsByKeyword(String keyword) throws AMapException {
        Log.d("PositionView", "开始根据关键字搜索 POI: " + keyword);

        // 创建 POI 查询条件，第一个参数是搜索关键字，第二个参数是搜索类型（"" 表示全部），第三个参数是城市（"" 表示全国范围）
        PoiSearch.Query query = new PoiSearch.Query(keyword, "", "");
        query.setPageSize(50); // 每页 50 个结果
        query.setPageNum(0); // 当前页码，0 表示第一页

        // 创建 PoiSearch 对象，并设置查询条件
        poiSearch = new PoiSearch(context, query);
        poiSearch.setOnPoiSearchListener(this); // 设置 POI 搜索监听器
        poiSearch.searchPOIAsyn(); // 异步搜索
    }
}