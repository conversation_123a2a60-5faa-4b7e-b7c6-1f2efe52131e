package com.almas.dinner.plugins;

import android.content.Context;
import android.media.MediaScannerConnection;

import androidx.annotation.NonNull;

import java.io.File;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

/**
 * 媒体扫描插件
 * 提供扫描文件添加到系统媒体库的能力
 */
public class MediaPlugin implements FlutterPlugin, MethodCallHandler {
    private static final String CHANNEL = "app.channel.mulazim.media";
    
    private MethodChannel channel;
    private Context context;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding binding) {
        // 创建MethodChannel并设置调用处理器
        channel = new MethodChannel(binding.getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(this);
        context = binding.getApplicationContext();
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        // 解绑通道
        channel.setMethodCallHandler(null);
        channel = null;
        context = null;
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        if (call.method.equals("scanFile")) {
            // 处理扫描文件的方法调用
            String path = call.argument("path");
            if (path != null) {
                scanFile(path);
                result.success(true);
            } else {
                result.error("INVALID_PATH", "Path cannot be null", null);
            }
        } else {
            // 未实现的方法
            result.notImplemented();
        }
    }

    /**
     * 扫描文件添加到媒体库
     * @param path 文件路径
     */
    private void scanFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return;
        }
        
        // 使用MediaScannerConnection扫描文件
        MediaScannerConnection.scanFile(
            context,
            new String[]{file.getAbsolutePath()},
            null,
            (path1, uri) -> {
                // 扫描完成回调
                System.out.println("Media scan completed for file: " + path);
            }
        );
    }
} 