package com.almas.dinner;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;

import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import com.almas.dinner.choice_map_view.ChoicePositionPlugin;
import com.almas.dinner.jpush.PushMessageService;
import com.almas.dinner.map_line_view.MapViewPlugin;
import com.almas.dinner.map_view.PositionPlugin;
import com.almas.dinner.order_line_view.OrderViewPlugin;
import com.almas.dinner.plugins.MediaPlugin;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

import cn.jiguang.api.utils.JCollectionAuth;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.data.JPushCollectControl;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

import androidx.core.app.NotificationManagerCompat;

public class MainActivity extends FlutterActivity implements MyEventBus.Message{
    private static final String CHANNEL = "com.almas.dinner";

    MethodChannel channel;

    private static boolean isShow;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        MyEventBus.register(this);
        
        // 设置竖屏模式
        setRequestedOrientation(SCREEN_ORIENTATION_PORTRAIT);
    }
    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        //注册插件
        flutterEngine.getPlugins().add(new PositionPlugin());
        flutterEngine.getPlugins().add(new ChoicePositionPlugin());
        flutterEngine.getPlugins().add(new MapViewPlugin());
        flutterEngine.getPlugins().add(new OrderViewPlugin());
        flutterEngine.getPlugins().add(new MediaPlugin());
        channel = new MethodChannel(flutterEngine.getDartExecutor(), CHANNEL);
        channel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(MethodCall call, MethodChannel.Result result) {

                Map<String, Object> parameter = (Map<String, Object>)call.arguments;
                try{
                    if (call.method.equals("isAppInstalled")) {
                        String packageName = parameter.get("packageName").toString();
                        result.success(isAppInstalled(packageName));
                    } else if (call.method.equals("openLocationSettings")) {
                        openLocationSettings();
                        result.success(true);
                    }else if(call.method.equals("registerJPush")){
                        JPushInterface.setDebugMode(true);
                        JCollectionAuth.setAuth(getContext(), true);
                        JPushInterface.init(getContext());
                        jPushPrivacy(false);
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                // 在这里编写你想要延迟执行的代码
                                Log.e("jpush_reg_id",JPushInterface.getRegistrationID(getContext()));
                                result.success(JPushInterface.getRegistrationID(getContext()));
                            }
                        }, 3000);
                    }else if(call.method.equals("openSettingsForNotification")){
                        JPushInterface.goToAppNotificationSettings(getContext());
                        result.success("ok");
                    }else if(call.method.equals("checkNotify")){
                        if(!hasNotificationPermission(getContext())){
                            result.success("no");
                        }else{
                            result.success("yes");
                        }
                    }
                }catch (Exception e){
                    // 获取异常的简短描述信息
                    String message = e.getMessage();
                    // 获取异常的详细堆栈跟踪信息
                    StringWriter sw = new StringWriter();
                    PrintWriter pw = new PrintWriter(sw);
                    e.printStackTrace(pw);
                    String stackTrace = sw.toString();
                    // 拼接 message 和 stackTrace，并换行
                    StringBuilder sb = new StringBuilder();
                    sb.append("Error message: ").append(message).append("\n");
                    sb.append("Stack trace: ").append(stackTrace);
//                    AliyunLogEvent.send("android异常捕获",sb.toString(),"一般");
                    if (call.method.equals("openLocationSettings")) {
                        result.error("LOCATION_SETTINGS_ERROR", message, null);
                    }
                }
            }
        });

    }

    //通知开关是否开启
    public static boolean hasNotificationPermission(Context context) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 在Android 8.0及以上版本
            return notificationManager.areNotificationsEnabled();
        } else {
            // 在Android 7.0及以下版本
            return NotificationManagerCompat.from(context).areNotificationsEnabled();
        }
    }

    //是否已安装此应用
    private boolean isAppInstalled(String packageName) {
        PackageManager packageManager = getContext().getPackageManager();
        try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    //打开系统位置设置
    private void openLocationSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            // 如果打开位置设置失败，尝试打开通用设置
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        isShow = true;
        runPushTask();
    }

    private void runPushTask() {
        if (PushMessageService.task!=null){
            for (PushMessageService.PushCall pushCall : PushMessageService.task) {
                String str = pushCall.getStr();
                channel.invokeMethod("callFlutterFunction",str, null);
            }
            PushMessageService.task.clear();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        MyEventBus.unRegister(this);
    }

    @Override
    public void call(String content) {
        Log.e("PushMessageService", "[jPushInfo.getInfo()] " + content);
        if (isShow) {
            runPushTask();
        }
    }

    private void jPushPrivacy(boolean isOpen) {
        try {
            JPushCollectControl.Builder builder=new JPushCollectControl.Builder();
            builder.imei(isOpen);
            builder.mac(isOpen);
            builder.imsi(isOpen);
            builder.ssid(isOpen);
            builder.wifi(isOpen);
            builder.bssid(isOpen);
            builder.cell(isOpen);
            JPushInterface.setCollectControl(this, builder.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
