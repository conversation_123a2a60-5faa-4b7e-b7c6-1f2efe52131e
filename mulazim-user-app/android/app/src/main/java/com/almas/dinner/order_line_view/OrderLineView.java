package com.almas.dinner.order_line_view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.almas.dinner.R;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.LatLngBounds;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.PolylineOptions;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.route.BusRouteResult;
import com.amap.api.services.route.DriveRouteResult;
import com.amap.api.services.route.RidePath;
import com.amap.api.services.route.RideRouteResult;
import com.amap.api.services.route.RideStep;
import com.amap.api.services.route.RouteSearch;
import com.amap.api.services.route.WalkRouteResult;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

class OrderLineView implements PlatformView, RouteSearch.OnRouteSearchListener, MethodChannel.MethodCallHandler{
    private MapView mapView;
    private Context context;
    private AMap aMap;
    private LatLng shipperPoint; // 配送员经纬度
    private LatLng restaurantPoint; // 餐厅经纬度
    private LatLng userPoint; // 用户经纬度
    private Marker restaurantMarker; // 起点标记
    private Marker userMarker; // 终点标记
    private Marker shipperMarker; // 终点标记
    private List<LatLng> routePoints; // 路线坐标列表
    private View view;
    RouteSearch routeSearch;
    RouteSearch shipperDistanceSearch;
    private final MethodChannel methodChannel;


    public OrderLineView(Context context, Map<String, Object> creationParams, BinaryMessenger messenger) {
        this.context = context;
        methodChannel = new MethodChannel(messenger, "order_line_channel");
        methodChannel.setMethodCallHandler(this);
        MapsInitializer.updatePrivacyShow(context,true,true);
        MapsInitializer.updatePrivacyAgree(context,true);
        
        double shipperLat = 0.0;
        if (creationParams.get("shipper_lat") != null) {
            try {
                shipperLat = Double.parseDouble(creationParams.get("shipper_lat").toString());
            } catch (NumberFormatException e) {
                // 处理解析异常，例如给startLatPoint赋默认值或者显示错误信息
                shipperLat = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        double shipperLng = 0.0;
        if(creationParams.get("shipper_lng") != null){
            try {
                shipperLng =  Double.parseDouble(creationParams.get("shipper_lng").toString());
            }catch (NumberFormatException e){
                shipperLng = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }


        double resLat = 0.0;
        if(creationParams.get("res_lat") != null){
            try{
                resLat =  Double.parseDouble(creationParams.get("res_lat").toString());
            }catch(NumberFormatException e){
                resLat = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        double resLng = 0.0;
        if(creationParams.get("res_lng") != null){
            try{
                resLng =  Double.parseDouble(creationParams.get("res_lng").toString());
            }catch(NumberFormatException e){
                resLng = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        double buildingLat = 0.0;
        if(creationParams.get("building_lat") != null){
            try{
                buildingLat =  Double.parseDouble(creationParams.get("building_lat").toString());
            }catch(NumberFormatException e){
                buildingLat = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        double buildingLng = 0.0;
        if(creationParams.get("building_lng") != null){
            try{
                buildingLng =  Double.parseDouble(creationParams.get("building_lng").toString());
            }catch(NumberFormatException e){
                buildingLng = 0.0; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        String resLogo = "";
        if(creationParams.get("res_logo") != null){
            try{
                resLogo =  creationParams.get("res_logo").toString();
            }catch (NumberFormatException e){
                resLogo = ""; // 默认值
                e.printStackTrace(); // 打印异常信息
            }
        }

        view = LayoutInflater.from(context).inflate(R.layout.amap_motor_route_view, null);

        mapView = view.findViewById(R.id.map_view);
        mapView.onCreate(null);

        aMap = mapView.getMap();
        // 设置起点和终点经纬度
        shipperPoint = new LatLng(shipperLat, shipperLng);
        restaurantPoint = new LatLng(resLat, resLng);
        userPoint = new LatLng(buildingLat, buildingLng);

        // 创建一个 LatLngBounds.Builder 对象
        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        // 将两个点的经纬度添加到 builder 中，用于创建矩形区域
//        builder.include(shipperPoint);
        builder.include(restaurantPoint);
        builder.include(userPoint);
        // 创建包含两个点的矩形区域
        LatLngBounds bounds = builder.build();

        aMap.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 112));


        loadDynamicImageAndAddMarker(resLogo, new MarkerLoadCallback() {
            @Override
            public void onMarkerLoaded(MarkerOptions markerOptions) {
                restaurantMarker = aMap.addMarker(markerOptions);
            }
        });
        // 添加起点和终点标记
        userMarker = aMap.addMarker(new MarkerOptions().position(userPoint).icon(BitmapDescriptorFactory.fromResource(R.drawable.maporder)));
        shipperMarker = aMap.addMarker(new MarkerOptions().position(shipperPoint).icon(BitmapDescriptorFactory.fromResource(R.drawable.moto)));




        UiSettings uiSettings = aMap.getUiSettings();
        // 设置是否启用缩放控件
        uiSettings.setZoomControlsEnabled(false);
        uiSettings.setCompassEnabled(true);
        uiSettings.setScaleControlsEnabled(true);
        // 初始化路线坐标列表
        routePoints = new ArrayList<>();
        try{
            routeSearch = new RouteSearch(context);
        }catch (Exception e){
            Log.e("routeSearch exception:",e.toString()+"");
        }
        if(routeSearch != null){
            routeSearch.setRouteSearchListener(this); // 设置路径规划监听器
            RouteSearch.FromAndTo fromAndTo = new RouteSearch.FromAndTo(
                    new LatLonPoint(restaurantPoint.latitude, restaurantPoint.longitude),
                    new LatLonPoint(userPoint.latitude, userPoint.longitude)
            );
            RouteSearch.RideRouteQuery query = new RouteSearch.RideRouteQuery(fromAndTo);
            routeSearch.calculateRideRouteAsyn(query); // 异步计算骑行路径
        }

    }


    private void loadDynamicImageAndAddMarker(String imageUrl, MarkerLoadCallback callback) {
        Glide.with(context)
                .asBitmap()
                .load(imageUrl)
                .into(new SimpleTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(Bitmap resource, Transition<? super Bitmap> transition) {
                        Bitmap scaledBitmap = Bitmap.createScaledBitmap(resource, 80, 80, false);
                        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(scaledBitmap);
                        MarkerOptions markerOptions = new MarkerOptions()
                                .position(restaurantPoint)
                                .icon(bitmapDescriptor);
                        callback.onMarkerLoaded(markerOptions);
                    }
                });
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        if (call.method.equals("updateShipperPosition")) {
            Map<String, Object> newParams = call.arguments();
            if(newParams == null) return;
            double shipperLat = 0.0;
            if (newParams.get("shipper_lat") != null) {
                try {
                    shipperLat = Double.parseDouble(newParams.get("shipper_lat").toString());
                } catch (NumberFormatException e) {
                    // 处理解析异常，例如给startLatPoint赋默认值或者显示错误信息
                    shipperLat = 0.0; // 默认值
                    e.printStackTrace(); // 打印异常信息
                }
            }
            double shipperLng = 0.0;
            if(newParams.get("shipper_lng") != null){
                try {
                    shipperLng =  Double.parseDouble(newParams.get("shipper_lng").toString());
                }catch (NumberFormatException e){
                    shipperLng = 0.0; // 默认值
                    e.printStackTrace(); // 打印异常信息
                }
            }
            if(shipperLng == 0 || shipperLat == 0){
                return;
            }
            shipperPoint = new LatLng(shipperLat, shipperLng);
            shipperMarker.remove();
            shipperMarker = aMap.addMarker(new MarkerOptions().position(shipperPoint).icon(BitmapDescriptorFactory.fromResource(R.drawable.moto)));

            try{
                shipperDistanceSearch = new RouteSearch(context);
            }catch (Exception e){
                Log.e("routeSearch exception:",e.toString()+"");
            }
            if(shipperDistanceSearch != null){

                RouteSearch.FromAndTo fromAndTo = new RouteSearch.FromAndTo(
                        new LatLonPoint(shipperPoint.latitude, shipperPoint.longitude),
                        new LatLonPoint(userPoint.latitude, userPoint.longitude)
                );
                RouteSearch.RideRouteQuery query = new RouteSearch.RideRouteQuery(fromAndTo);
                shipperDistanceSearch.calculateRideRouteAsyn(query); // 异步计算骑行路径
                shipperDistanceSearch.setRouteSearchListener(new RouteSearch.OnRouteSearchListener() {
                    @Override
                    public void onBusRouteSearched(BusRouteResult busRouteResult, int i) {}
                    @Override
                    public void onDriveRouteSearched(DriveRouteResult driveRouteResult, int i) {}
                    @Override
                    public void onWalkRouteSearched(WalkRouteResult walkRouteResult, int i) {}
                    @Override
                    public void onRideRouteSearched(RideRouteResult rideRouteResult, int i) {
                        if (rideRouteResult != null && rideRouteResult.getPaths() != null
                                && rideRouteResult.getPaths().size() > 0) {
                            // 获取第一条路径
                            RidePath ridePath = rideRouteResult.getPaths().get(0);
                            // 遍历路径中的所有步骤，将坐标添加到路线坐标列表中
                            result.success(ridePath.getDistance());
                        }
                    }
                }); // 设置路径规划监听器
            }
        }
    }

    public interface MarkerLoadCallback {
        void onMarkerLoaded(MarkerOptions markerOptions);
    }


    @Nullable
    @org.jetbrains.annotations.Nullable
    @Override
    public View getView() {
        return view;
    }

    @Override
    public void onBusRouteSearched(BusRouteResult busRouteResult, int i) {}

    @Override
    public void onDriveRouteSearched(DriveRouteResult driveRouteResult, int i) {}

    @Override
    public void onWalkRouteSearched(WalkRouteResult walkRouteResult, int i) {}

    @Override
    public void onRideRouteSearched(RideRouteResult rideRouteResult, int i) {
        if (i == 1000 && rideRouteResult != null && rideRouteResult.getPaths() != null
                && rideRouteResult.getPaths().size() > 0) {
            // 获取第一条路径
            RidePath ridePath = rideRouteResult.getPaths().get(0);
            // 遍历路径中的所有步骤，将坐标添加到路线坐标列表中
            List<RideStep> steps = ridePath.getSteps();
            for(RideStep myStep : steps){
                List<LatLonPoint> pathPoints = myStep.getPolyline();
                for (LatLonPoint point : pathPoints) {
                    routePoints.add(new LatLng(point.getLatitude(), point.getLongitude()));
                }
            }
            Log.e("routePoints length:",routePoints.size()+"");
            aMap.addPolyline(new PolylineOptions().addAll(routePoints).color(Color.rgb(255, 0, 0))
                    .width(15f).setDottedLine(false));
            mapView.invalidate();
        }
    }



    @Override
    public void dispose() {
        // 清除地图相关资源
        mapView.onDestroy();

        // 清除标记物相关资源
        restaurantMarker.remove();
        userMarker.remove();
        shipperMarker.remove();

        // 清除路线相关资源
        routePoints.clear();

        // 移除路径规划监听器
        routeSearch.setRouteSearchListener(null);
    }
}