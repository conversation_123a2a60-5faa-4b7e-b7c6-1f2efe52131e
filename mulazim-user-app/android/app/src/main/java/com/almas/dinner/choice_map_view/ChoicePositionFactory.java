package com.almas.dinner.choice_map_view;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.amap.api.services.core.AMapException;

import java.util.Map;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.platform.PlatformViewFactory;

class ChoicePositionFactory extends PlatformViewFactory {
    private final BinaryMessenger messenger;
    public ChoicePositionFactory(BinaryMessenger messenger) {
        super(StandardMessageCodec.INSTANCE);
        this.messenger = messenger;
    }

    @NonNull
    @Override
    public ChoicePositionView create(@NonNull Context context, int id, @Nullable Object args) {
        final Map<String, Object> creationParams = (Map<String, Object>) args;
        try {
            return new ChoicePositionView(context, creationParams, messenger);
        } catch (AMapException e) {
            throw new RuntimeException(e);
        }
    }
}
