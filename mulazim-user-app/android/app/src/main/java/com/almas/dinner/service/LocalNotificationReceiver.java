package com.almas.dinner.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.almas.dinner.jpush.PushMessageService;
import com.almas.dinner.model.JPushInfo;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class LocalNotificationReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        // 获取从通知传递过来的额外数据
        String orderId = intent.getStringExtra("orderId");
        Map<String, String> info = new HashMap<>();
        info.put("page", "chat_page");
        info.put("order_id", orderId);
        // 在这里可以根据订单ID执行相应的操作，例如发布一个 EventBus 事件
        if (orderId != null) {
            // 将Map转换为字符串
            String infoString = mapToString(info);
            if (PushMessageService.task==null){
                PushMessageService.task=new ArrayList<>();
            }
            PushMessageService.task.add(()->infoString);
            PushMessageService.launchAPK(context);
            try{
//                EventBus.getDefault().post(new JPushInfo(infoString));
            }catch (Throwable throwable){

            }
        }
    }

    // 将Map转换为字符串
    private static String mapToString(Map<String, String> map) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("{");
        for (Map.Entry<String, String> entry : map.entrySet()) {
            stringBuilder.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\", ");
        }
        stringBuilder.delete(stringBuilder.length() - 2, stringBuilder.length()); // 删除末尾多余的逗号和空格
        stringBuilder.append("}");
        return stringBuilder.toString();
    }
}
