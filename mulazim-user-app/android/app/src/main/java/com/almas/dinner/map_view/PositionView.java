package com.almas.dinner.map_view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.almas.dinner.R;
import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptor;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiSearch;
import com.amap.api.services.route.BusRouteResult;
import com.amap.api.services.route.DriveRouteResult;
import com.amap.api.services.route.RideRouteResult;
import com.amap.api.services.route.RouteSearch;
import com.amap.api.services.route.WalkRouteResult;

import java.util.HashMap;
import java.util.Map;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

class PositionView implements PlatformView, RouteSearch.OnRouteSearchListener, MethodChannel.MethodCallHandler, AMap.OnCameraChangeListener, PoiSearch.OnPoiSearchListener {
    private MapView mapView;
    private AMap aMap;
    private View view;
    private final MethodChannel methodChannel;
    private LatLng initialLatLng;
    private ProgressBar progressBar; // 圆形加载指示器
    private Marker shipperMarker;

    private LatLng centerLatLng; // 地图中心点的经纬度

    private PoiSearch poiSearch; // POI 搜索对象

    public PositionView(Context context, Map<String, Object> creationParams, BinaryMessenger messenger) throws AMapException {
        methodChannel = new MethodChannel(messenger, "position_channel");
        methodChannel.setMethodCallHandler(this);
        MapsInitializer.updatePrivacyShow(context,true,true);
        MapsInitializer.updatePrivacyAgree(context,true);
        // 从 Flutter 端接收传递的初始定位数据
        double initialLatitude = (double) creationParams.get("latitude");
        double initialLongitude = (double) creationParams.get("longitude");
        initialLatLng = new LatLng(initialLatitude, initialLongitude);

        view = LayoutInflater.from(context).inflate(R.layout.amap_motor_route_view, null);
        progressBar = view.findViewById(R.id.progress_bar); // 获取 ProgressBar 实例
        mapView = view.findViewById(R.id.map_view);
        mapView.onCreate(null);
        mapView.onResume();      // MapView 恢复
        if (mapView != null) {
            mapView.onResume();
        }
        if (aMap == null) {
            aMap = mapView.getMap();
            aMap.getUiSettings().setZoomControlsEnabled(true);
            aMap.setOnCameraChangeListener(this); // 设置相机变化监听
            // 初次打开页面时将相机移动到传入的初始定位数据
            moveCameraToInitialPosition();
        }
        UiSettings uiSettings = aMap.getUiSettings();
        // 设置是否启用缩放控件
        uiSettings.setZoomControlsEnabled(false);
        uiSettings.setCompassEnabled(true);
        uiSettings.setScaleControlsEnabled(true);

        shipperMarker = aMap.addMarker(new MarkerOptions().position(initialLatLng).icon(getScaledMarkerIcon(context, R.drawable.maporder, 66, 80)));

        // 初次设置中心点为传递的初始定位数据
        moveCameraToInitialPosition();
    }

    private BitmapDescriptor getScaledMarkerIcon(Context context, int drawableResId, int width, int height) {
        // 加载原始Bitmap
        Bitmap originalBitmap = BitmapFactory.decodeResource(context.getResources(), drawableResId);

        // 缩放Bitmap
        Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, width, height, false);

        // 将缩放后的Bitmap转换为BitmapDescriptor
        return BitmapDescriptorFactory.fromBitmap(scaledBitmap);
    }
    private void moveCameraToInitialPosition() {
        if (initialLatLng != null) {
            // 慢慢移动相机到初始位置
            aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(initialLatLng, 15f), 2000, null);
        }
    }
    @Nullable
    @org.jetbrains.annotations.Nullable
    @Override
    public View getView() {
        return view;
    }
    @Override
    public void onBusRouteSearched(BusRouteResult busRouteResult, int i) {}
    @Override
    public void onDriveRouteSearched(DriveRouteResult driveRouteResult, int i) {}
    public void onWalkRouteSearched(WalkRouteResult walkRouteResult, int i) {}
    @Override
    public void onRideRouteSearched(RideRouteResult rideRouteResult, int i) {}

    @Override
    public void dispose() {
        try {
            // 在UI线程中安全移除标记和销毁地图
            if (Looper.myLooper() == Looper.getMainLooper()) {
                disposeMapResources();
            } else {
                new Handler(Looper.getMainLooper()).post(this::disposeMapResources);
            }
        } catch (Exception e) {
            // 记录异常但不抛出
            e.printStackTrace();
        }
    }
    
    // 将资源释放逻辑分离到单独的方法中
    private void disposeMapResources() {
        try {
            if (shipperMarker != null) {
                shipperMarker.remove();
                shipperMarker = null;
            }
            
            if (aMap != null) {
                aMap.clear();
                aMap = null;
            }
            
            if (mapView != null) {
                mapView.onPause();
                mapView.onDestroy();
                mapView = null;
            }
        } catch (Exception e) {
            // 记录异常但不中断程序
            e.printStackTrace();
        }
    }

    @Override
    public void onMethodCall(@NonNull MethodCall methodCall, @NonNull MethodChannel.Result result) {

    }
    // 显示加载指示器
    private void showLoading() {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (progressBar != null && progressBar.getVisibility() == View.GONE) {
                progressBar.setVisibility(View.VISIBLE);
            }
        });
    }

    // 隐藏加载指示器
    private void hideLoading() {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (progressBar != null && progressBar.getVisibility() == View.VISIBLE) {
                progressBar.setVisibility(View.GONE);
            }
        });
    }


    @Override
    public void onCameraChange(CameraPosition cameraPosition) {
        // 当相机位置发生变化时，更新中心点位置
        centerLatLng = cameraPosition.target;
    }

    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        // 相机移动结束时，更新中心点位置
        centerLatLng = cameraPosition.target;
        // 将当前中心点的坐标通过MethodChannel传递给Flutter层
        Map<String, Double> position = new HashMap<>();
        position.put("latitude", centerLatLng.latitude);
        position.put("longitude", centerLatLng.longitude);
        methodChannel.invokeMethod("updateCenterPosition", position);
    }

    @Override
    public void onPoiSearched(PoiResult poiResult, int i) {
    }

    @Override
    public void onPoiItemSearched(PoiItem poiItem, int i) {}

}