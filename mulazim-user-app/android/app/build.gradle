plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
//    id "com.huawei.agconnect"
}

android {
    namespace = "com.almas.dinner"
    compileSdk = 34
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.almas.dinner"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled = true

        ndk {
            abiFilters 'arm64-v8a'
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME : "com.almas.dinner",
                JPUSH_APPKEY : "3326fe4b331a003902154e24", //JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.
                XIAOMI_APPID : "MI-2882303761518024968",//JPush5.5.3开始，可以不添加MI-前缀.
                XIAOMI_APPKEY : "MI-5421802435968",//JPush5.5.3开始，可以不添加MI-前缀.
                OPPO_APPKEY : "OP-1ecyC9WgkO9W4K4wOsWscgSC8",
                OPPO_APPID : "**********",
                OPPO_APPSECRET : "OP-0dA5cD3Ee753edbdee4D7F048e568b37",
                VIVO_APPKEY : "aa547c1951369be7c93b1deab6d698ce",
                VIVO_APPID : "101188174"
        ]

    }

    signingConfigs {
        config {
            keyAlias 'almas'
            keyPassword 'AlmasBiz2602B'
            storeFile file('DinnerAndroidKey.jks')
            storePassword 'AlmasBiz2602B'
        }
    }

    buildTypes {
        debug {
            minifyEnabled true
            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        profile {
            minifyEnabled true
            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        release {
            minifyEnabled true
            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    buildToolsVersion '30.0.3'
}

flutter {
    source = "../.."
}


//dependencies {
//    implementation 'com.aliyun.openservices:aliyun-log-android-sdk:2.6.12'
//}

dependencies {
//    implementation fileTree(include: ['*.jar','*.aar'], dir: 'libs' )

    // JPush核心依赖
    implementation 'cn.jiguang.sdk:jpush:5.3.1'
    implementation 'cn.jiguang.sdk:jcore:4.6.1'
    
    // JPush第三方推送依赖
    implementation 'cn.jiguang.sdk.plugin:xiaomi:5.3.1'
    implementation 'cn.jiguang.sdk.plugin:huawei:5.3.1'
    implementation 'cn.jiguang.sdk.plugin:oppo:5.3.1'
    implementation 'cn.jiguang.sdk.plugin:vivo:5.3.1'

    implementation 'com.amap.api:location:6.0.0' // 尝试最新版本
    implementation 'com.amap.api:navi-3dmap:8.1.0_3dmap8.1.0'
    implementation 'com.amap.api:search:9.2.0'
    implementation 'com.amap.api:location:6.1.0'
    implementation 'com.github.bumptech.glide:glide:4.13.2'
    implementation 'androidx.core:core:1.7.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.13.2'

    //以下为 OPPO 3.1.0 aar 需要依赖
    implementation 'com.google.code.gson:gson:2.6.2'
    implementation 'commons-codec:commons-codec:1.6'
    implementation 'androidx.annotation:annotation:1.1.0'



    //以下为 华为
    implementation 'com.huawei.hms:push:6.9.0.300'

}
