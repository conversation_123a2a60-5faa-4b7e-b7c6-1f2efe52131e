//
//  ShipperPointViewController.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/13.
//

import UIKit
import Flutter
import AMapSearchKit
import AMapNaviKit

public class ShipperPointViewController: NSObject, FlutterPlatformView, MAMapViewDelegate, AMapSearchDelegate {
    // 声明变量
    private let containerView: UIView
    var mapView: MAMapView!
    var searchAPI: AMapSearchAPI!  // 搜索服务对象
    private var methodChannel: FlutterMethodChannel?
    
    // 用于存储上次搜索位置的经纬度
    private var lastSearchCoordinate: CLLocationCoordinate2D?

    // 初始化方法
    public init(frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) {
        // 创建容器视图
        containerView = UIView(frame: frame)
        super.init()

        // 初始化高德地图的SDK，并设置APIKey
        AMapServices.shared()?.apiKey = "b246804aa0216430d4e64979bd0bb1e0"

        // 初始化地图
        initMapView()

        // 初始化搜索服务
        initSearchService()
        
        // 检查 window 和 rootViewController 是否存在
        guard let window = UIApplication.shared.windows.first,
              let rootViewController = window.rootViewController else {
            print("Window or rootViewController not found.")
            return
        }
        
        // 设置 Flutter 方法通道
        let controller = rootViewController as! FlutterViewController
        methodChannel = FlutterMethodChannel(name: "position_channel", binaryMessenger: controller.binaryMessenger)
        methodChannel?.setMethodCallHandler(handleMethodCall)
    }
    
    // 初始化搜索服务
    func initSearchService() {
        searchAPI = AMapSearchAPI()
        searchAPI.delegate = self
    }
    
    // 发起POI搜索请求
    func searchNearbyPOI(at coordinate: CLLocationCoordinate2D) {
        let request = AMapPOIAroundSearchRequest()
        request.location = AMapGeoPoint.location(withLatitude: CGFloat(coordinate.latitude), longitude: CGFloat(coordinate.longitude))
        request.types = ""  // 可根据需要设置更多类别
        request.radius = 10000000  // 设置搜索半径，单位是米
        request.sortrule = 0  // 按距离排序
        request.offset = 25

        // 发起POI搜索请求
        searchAPI.aMapPOIAroundSearch(request)
    }
    
    // 新增处理搜索POI的方法
    private func searchPOI(with keyword: String) {
        // 确保使用最后一次搜索的坐标
        guard let coordinate = lastSearchCoordinate else {
            print("Last search coordinate is nil")
            return
        }

        let request = AMapPOIAroundSearchRequest()
        request.keywords = keyword
        request.radius = 10000000  // 设置搜索半径，单位是米
        request.location = AMapGeoPoint.location(withLatitude: CGFloat(coordinate.latitude), longitude: CGFloat(coordinate.longitude))  // 使用 lastSearchCoordinate
        request.offset = 25

        // 发起POI搜索请求
        searchAPI.aMapPOIAroundSearch(request)
    }
    
    // 搜索成功回调
    public func onPOISearchDone(_ request: AMapPOISearchBaseRequest!, response: AMapPOISearchResponse!) {
        print("POI搜索成功")

        if response.pois.count > 0 {
            var poiList: [[String: Any]] = []
            
            // 遍历POI结果并处理
            for poi in response.pois {
                let name = poi.name  // POI名称
                let address = poi.address ?? ""  // POI地址
                let location = poi.location  // POI经纬度
                
                // 在地图上添加标注
                let annotation = MAPointAnnotation()
                annotation.coordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(poi.location.latitude), longitude: CLLocationDegrees(poi.location.longitude))
                annotation.title = name
                annotation.subtitle = address
                mapView.addAnnotation(annotation)

                // 将 POI 信息存储为字典格式以便传递给 Flutter
                poiList.append([
                    "name": name as Any,
                    "address": address,
                    "latitude": poi.location.latitude,
                    "longitude": poi.location.longitude,
                    "distance": poi.distance
                ])
            }
            
            print("poiList ===", poiList)
            
            // 通过 Flutter 方法通道返回 POI 搜索结果
            methodChannel?.invokeMethod("updatePOIs", arguments: poiList)
        } else {
            // 没有找到 POI 的情况，返回一个空列表
            methodChannel?.invokeMethod("onNearbyBuildingsFetched", arguments: [])
        }
    }

    // 搜索失败回调
    public func aMapSearchRequest(_ request: Any!, didFailWithError error: Error!) {
        print("POI搜索失败: \(error.localizedDescription)")
        methodChannel?.invokeMethod("onSearchError", arguments: error.localizedDescription)
    }

    // 实现FlutterPlatformView协议中的方法
    public func view() -> UIView {
        return containerView
    }

    // 初始化地图方法
    func initMapView() {
        mapView = MAMapView(frame: containerView.bounds)
        mapView.delegate = self
        // 显示用户当前位置
        mapView.showsUserLocation = true
        // 设置地图精度为500米
        mapView.desiredAccuracy = kCLLocationAccuracyHundredMeters
        mapView.zoomLevel = 15
        // 设置用户跟踪模式
        mapView.userTrackingMode = .followWithHeading
        containerView.addSubview(mapView)
        
        // 添加用户位置标注
        if let userLocation = mapView.userLocation {
            let annotation = MAPointAnnotation()
            annotation.coordinate = userLocation.coordinate
            annotation.title = "当前位置"
            mapView.addAnnotation(annotation)
        }
    }

    // 实现MAMapViewDelegate协议中的方法，用于自定义标注点
    public func mapView(_ mapView: MAMapView!, viewFor annotation: MAAnnotation!) -> MAAnnotationView! {
        if annotation is MAUserLocation {
            let identifier = "UserLocationIdentifier"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
            if annotationView == nil {
                annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            }
            
            // 创建定位图标
            let locationView = UIView(frame: CGRect(x: 0, y: 0, width: 30, height: 30))
            locationView.backgroundColor = .systemBlue
            
            // 创建外圈
            let outerCircle = UIView(frame: CGRect(x: -10, y: -10, width: 50, height: 50))
            outerCircle.backgroundColor = .systemBlue.withAlphaComponent(0.3)
            outerCircle.layer.cornerRadius = 25
            outerCircle.clipsToBounds = true
            
            // 创建内圈
            let innerCircle = UIView(frame: CGRect(x: 10, y: 10, width: 10, height: 10))
            innerCircle.backgroundColor = .white
            innerCircle.layer.cornerRadius = 5
            innerCircle.clipsToBounds = true
            
            // 将视图组合
            locationView.layer.cornerRadius = 15
            locationView.clipsToBounds = true
            locationView.addSubview(outerCircle)
            locationView.addSubview(innerCircle)
            
            // 将组合视图转换为图片
            let renderer = UIGraphicsImageRenderer(size: locationView.bounds.size)
            let image = renderer.image { context in
                locationView.drawHierarchy(in: locationView.bounds, afterScreenUpdates: true)
            }
            
            annotationView?.image = image
            return annotationView
        }
        return nil
    }

    // 处理 Flutter 调用
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "changePositionItem":
            if let args = call.arguments as? [String: Any],
               let latitude = args["latitude"] as? CLLocationDegrees,
               let longitude = args["longitude"] as? CLLocationDegrees {
                // 更新地图中心点为指定的建筑坐标
                let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
                mapView.setCenter(coordinate, animated: true)
                // 搜索该建筑周边的建筑
                searchNearbyPOI(at: coordinate)  // 进行建筑周边搜索
                lastSearchCoordinate = coordinate  // 更新最后的搜索坐标
                result(nil)
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid coordinates", details: nil))
            }
        case "searchPOIs":
            if let keyword = call.arguments as? String {
                searchPOI(with: keyword)  // 调用搜索方法
                result(nil)
            } else {
                result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid keyword", details: nil))
            }
        case "myPosition":
            // 获取配送员当前的位置
            let userLocation = mapView.userLocation.coordinate
            // 返回当前坐标给 Flutter
            
            mapView.setCenter(userLocation, animated: true)
            lastSearchCoordinate = userLocation
            
            result([
                "latitude": userLocation.latitude,
                "longitude": userLocation.longitude
            ])
        case "getCurrentPosition":
            let currentPosition = mapView.centerCoordinate
            result(["latitude": currentPosition.latitude, "longitude": currentPosition.longitude])
        default:
            result(FlutterMethodNotImplemented)
        }
    }

    // 实现用户位置更新
    public func mapView(_ mapView: MAMapView!, didUpdate userLocation: MAUserLocation!, updatingLocation: Bool) {
        if updatingLocation && lastSearchCoordinate == nil {
            // 如果是首次加载地图，发送用户位置
            lastSearchCoordinate = userLocation.coordinate
            
            // 向 Flutter 通知地图中心更新
            methodChannel?.invokeMethod("updateCenterPosition", arguments: [
                "latitude": userLocation.coordinate.latitude,
                "longitude": userLocation.coordinate.longitude
            ])
        }
    }
    
    // 当地图区域改变时调用，发送中心点坐标
    public func mapView(_ mapView: MAMapView!, regionDidChangeAnimated animated: Bool) {
        let centerCoordinate = mapView.centerCoordinate
        
        // 向 Flutter 发送地图中心点坐标
        methodChannel?.invokeMethod("updateCenterPosition", arguments: [
            "latitude": centerCoordinate.latitude,
            "longitude": centerCoordinate.longitude
        ])
    }

    // 缩小并裁剪图像为圆形
    func resizeAndRoundImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let roundedImage = renderer.image { context in
            let rect = CGRect(origin: .zero, size: targetSize)
            let roundedPath = UIBezierPath(ovalIn: rect)
            context.cgContext.addPath(roundedPath.cgPath)
            context.cgContext.clip()
            image.draw(in: rect)
        }
        return roundedImage
    }
}

// 实现FlutterPlatformViewFactory协议
public class ShipperPointViewFactory: NSObject, FlutterPlatformViewFactory {
    // 创建MapViewController对象
    public func create(withFrame frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) -> FlutterPlatformView {
        return ShipperPointViewController(frame: frame, viewIdentifier: viewId, arguments: args)
    }

    // 创建消息编解码器
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

// 实现FlutterPlugin协议
public class ShipperPointViewPlugin: NSObject, FlutterPlugin {
    // 注册插件
    public static func register(with registrar: FlutterPluginRegistrar) {
        let factory = ShipperPointViewFactory()
        registrar.register(factory, withId: "plugin/shipper-location")
    }
}
