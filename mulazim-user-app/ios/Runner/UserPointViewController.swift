//
//  UserPointViewController.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import UIKit
import Flutter
import AMapSearchKit
import AMapNaviKit

public class UserPointViewController: NSObject, FlutterPlatformView, MAMapViewDelegate, AMapSearchDelegate {
    // 核心组件
    private let containerView: UIView
    private var mapView: MAMapView!
    private var searchAPI: AMapSearchAPI!
    private var methodChannel: FlutterMethodChannel?
    
    // 状态管理
    private var isUserInteracting = false  // 用户是否正在交互
    private var currentSearchLocation: CLLocationCoordinate2D?  // 当前搜索位置
    private var hasInitializedLocation = false  // 是否已初始化位置

    // 初始化
    public init(frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) {
        containerView = UIView(frame: frame)
        super.init()
        
        // 设置高德地图API Key
        AMapServices.shared()?.apiKey = "b246804aa0216430d4e64979bd0bb1e0"
        
        // 初始化组件
        setupMapView()
        setupSearchAPI()
        setupMethodChannel()
    }
    
    // 设置地图视图
    private func setupMapView() {
        mapView = MAMapView(frame: containerView.bounds)
        mapView.delegate = self
        mapView.showsUserLocation = true
        mapView.desiredAccuracy = kCLLocationAccuracyHundredMeters
        mapView.zoomLevel = 15
        
        // 关键：不设置自动跟踪模式，完全手动控制
        mapView.userTrackingMode = .none
        
        containerView.addSubview(mapView)
        
        // 确保地图立即显示
        DispatchQueue.main.async { [weak self] in
            self?.mapView.setNeedsDisplay()
        }
    }
    
    // 设置搜索API
    private func setupSearchAPI() {
        searchAPI = AMapSearchAPI()
        searchAPI.delegate = self
    }
    
    // 设置方法通道
    private func setupMethodChannel() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if let flutterVC = self.findFlutterViewController() {
                self.methodChannel = FlutterMethodChannel(
                    name: "choice_position_channel", 
                    binaryMessenger: flutterVC.binaryMessenger
                )
                self.methodChannel?.setMethodCallHandler(self.handleMethodCall)
            }
        }
    }
    
    // 查找FlutterViewController
    private func findFlutterViewController() -> FlutterViewController? {
        var rootVC: UIViewController?
        
        if #available(iOS 13.0, *) {
            rootVC = UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .flatMap { $0.windows }
                .first { $0.isKeyWindow }?.rootViewController
        } else {
            rootVC = UIApplication.shared.keyWindow?.rootViewController
        }
        
        return findFlutterVC(in: rootVC)
    }
    
    private func findFlutterVC(in vc: UIViewController?) -> FlutterViewController? {
        if let flutterVC = vc as? FlutterViewController {
            return flutterVC
        }
        
        for child in vc?.children ?? [] {
            if let found = findFlutterVC(in: child) {
                return found
            }
        }
        
        if let presented = vc?.presentedViewController {
            return findFlutterVC(in: presented)
        }
        
        return nil
    }
    
    // MARK: - FlutterPlatformView
    public func view() -> UIView {
        return containerView
    }
    
    // MARK: - 方法调用处理
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "changePositionItem":
            handleChangePosition(call: call, result: result)
        case "searchPOIs":
            handleSearchPOIs(call: call, result: result)
        case "myPosition":
            handleMyPosition(result: result)
        case "goToMyPosition":
            handleGoToMyPosition(result: result)
        case "getCurrentPosition":
            handleGetCurrentPosition(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    // 处理位置变更
    private func handleChangePosition(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let latitude = args["latitude"] as? Double,
              let longitude = args["longitude"] as? Double else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid coordinates", details: nil))
            return
        }
        
        let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
        
        // 只有在已经初始化后才标记为用户交互
        // 初始化时不标记，避免影响首次显示
        if hasInitializedLocation {
            isUserInteracting = true
        }
        
        currentSearchLocation = coordinate
        
        // 移动地图到指定位置
        mapView.setCenter(coordinate, animated: true)
        
        // 搜索周边POI
        searchPOIAround(coordinate: coordinate)
        
        result(nil)
    }
    
    // 处理POI搜索
    private func handleSearchPOIs(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let keyword = call.arguments as? String,
              let searchLocation = currentSearchLocation else {
            result(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid search parameters", details: nil))
            return
        }
        
        searchPOIWithKeyword(keyword: keyword, around: searchLocation)
        result(nil)
    }
    
    // 处理我的位置 - 只返回坐标，不移动地图
    private func handleMyPosition(result: @escaping FlutterResult) {
        let userLocation = mapView.userLocation.coordinate
        
        // 只返回用户位置坐标，不移动地图
        // 避免自动跳转到用户位置
        result([
            "latitude": userLocation.latitude,
            "longitude": userLocation.longitude
        ])
    }
    
    // 处理跳转到我的位置 - 用户手动点击时调用
    private func handleGoToMyPosition(result: @escaping FlutterResult) {
        let userLocation = mapView.userLocation.coordinate
        
        // 重置状态，允许回到用户位置
        isUserInteracting = false
        currentSearchLocation = userLocation
        
        // 移动到用户位置
        mapView.setCenter(userLocation, animated: true)
        
        // 搜索用户位置周边POI
        searchPOIAround(coordinate: userLocation)
        
        result([
            "latitude": userLocation.latitude,
            "longitude": userLocation.longitude
        ])
    }
    
    // 处理获取当前位置
    private func handleGetCurrentPosition(result: @escaping FlutterResult) {
        let currentPosition = mapView.centerCoordinate
        result([
            "latitude": currentPosition.latitude, 
            "longitude": currentPosition.longitude
        ])
    }
    
    // MARK: - POI搜索
    private func searchPOIAround(coordinate: CLLocationCoordinate2D) {
        let request = AMapPOIAroundSearchRequest()
        request.location = AMapGeoPoint.location(
            withLatitude: CGFloat(coordinate.latitude), 
            longitude: CGFloat(coordinate.longitude)
        )
        request.radius = 5000  // 5公里范围
        request.offset = 25
        request.sortrule = 0  // 按距离排序
        
        searchAPI.aMapPOIAroundSearch(request)
    }
    
    private func searchPOIWithKeyword(keyword: String, around coordinate: CLLocationCoordinate2D) {
        let request = AMapPOIAroundSearchRequest()
        request.keywords = keyword
        request.location = AMapGeoPoint.location(
            withLatitude: CGFloat(coordinate.latitude), 
            longitude: CGFloat(coordinate.longitude)
        )
        request.radius = 5000
        request.offset = 25
        
        searchAPI.aMapPOIAroundSearch(request)
    }
    
    // MARK: - AMapSearchDelegate
    public func onPOISearchDone(_ request: AMapPOISearchBaseRequest!, response: AMapPOISearchResponse!) {
        guard let pois = response?.pois, !pois.isEmpty else {
            DispatchQueue.main.async { [weak self] in
                self?.methodChannel?.invokeMethod("updatePOIs", arguments: [])
            }
            return
        }
        
        // 清除旧的标注
        mapView.removeAnnotations(mapView.annotations.filter { !($0 is MAUserLocation) })
        
        var poiList: [[String: Any]] = []
        
        for poi in pois {
            // 添加地图标注
            let annotation = MAPointAnnotation()
            annotation.coordinate = CLLocationCoordinate2D(
                latitude: CLLocationDegrees(poi.location.latitude), 
                longitude: CLLocationDegrees(poi.location.longitude)
            )
            annotation.title = poi.name
            annotation.subtitle = poi.address
            mapView.addAnnotation(annotation)
            
            // 构建POI数据
            poiList.append([
                "name": poi.name ?? "",
                "address": poi.address ?? "",
                "latitude": poi.location.latitude,
                "longitude": poi.location.longitude,
                "distance": poi.distance
            ])
        }
        
        // 返回POI列表给Flutter
        DispatchQueue.main.async { [weak self] in
            self?.methodChannel?.invokeMethod("updatePOIs", arguments: poiList)
        }
    }
    
    public func aMapSearchRequest(_ request: Any!, didFailWithError error: Error!) {
        print("POI搜索失败: \(error?.localizedDescription ?? "未知错误")")
        DispatchQueue.main.async { [weak self] in
            self?.methodChannel?.invokeMethod("updatePOIs", arguments: [])
        }
    }
    
    // MARK: - MAMapViewDelegate
    public func mapView(_ mapView: MAMapView!, didUpdate userLocation: MAUserLocation!, updatingLocation: Bool) {
        // 只在首次获取位置时处理
        if updatingLocation && !hasInitializedLocation {
            hasInitializedLocation = true
            currentSearchLocation = userLocation.coordinate
            
            // 第一次获取位置时，移动地图到用户位置
            mapView.setCenter(userLocation.coordinate, animated: true)
            
            // 搜索当前位置周边POI
            searchPOIAround(coordinate: userLocation.coordinate)
            
            // 通知Flutter位置更新（仅首次）
            DispatchQueue.main.async { [weak self] in
                self?.methodChannel?.invokeMethod("updateCenterPosition", arguments: [
                    "latitude": userLocation.coordinate.latitude,
                    "longitude": userLocation.coordinate.longitude
                ])
            }
        }
    }
    
    public func mapView(_ mapView: MAMapView!, regionDidChangeAnimated animated: Bool) {
        let centerCoordinate = mapView.centerCoordinate
        
        // 更新当前搜索位置
        currentSearchLocation = centerCoordinate
        
        // 搜索新位置的周边POI
        searchPOIAround(coordinate: centerCoordinate)
        
        // 通知Flutter地图中心位置变化
        DispatchQueue.main.async { [weak self] in
            self?.methodChannel?.invokeMethod("updateCenterPosition", arguments: [
                "latitude": centerCoordinate.latitude,
                "longitude": centerCoordinate.longitude
            ])
        }
    }
    
    // 自定义标注视图
    public func mapView(_ mapView: MAMapView!, viewFor annotation: MAAnnotation!) -> MAAnnotationView! {
        if annotation is MAUserLocation {
            return createUserLocationView(for: annotation)
        } else if annotation is MAPointAnnotation {
            return createPOIAnnotationView(for: annotation)
        }
        return nil
    }
    
    private func createUserLocationView(for annotation: MAAnnotation) -> MAAnnotationView {
        let identifier = "UserLocationView"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
        
        if annotationView == nil {
            annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            
            // 创建用户位置图标
            let size = CGSize(width: 20, height: 20)
            let renderer = UIGraphicsImageRenderer(size: size)
            let image = renderer.image { context in
                // 外圈
                context.cgContext.setFillColor(UIColor.systemBlue.withAlphaComponent(0.3).cgColor)
                context.cgContext.fillEllipse(in: CGRect(origin: .zero, size: size))
                
                // 内圈
                let innerSize = CGSize(width: 12, height: 12)
                let innerOrigin = CGPoint(x: (size.width - innerSize.width) / 2, y: (size.height - innerSize.height) / 2)
                context.cgContext.setFillColor(UIColor.systemBlue.cgColor)
                context.cgContext.fillEllipse(in: CGRect(origin: innerOrigin, size: innerSize))
                
                // 中心点
                let centerSize = CGSize(width: 4, height: 4)
                let centerOrigin = CGPoint(x: (size.width - centerSize.width) / 2, y: (size.height - centerSize.height) / 2)
                context.cgContext.setFillColor(UIColor.white.cgColor)
                context.cgContext.fillEllipse(in: CGRect(origin: centerOrigin, size: centerSize))
            }
            
            annotationView?.image = image
        }
        
        return annotationView!
    }
    
    private func createPOIAnnotationView(for annotation: MAAnnotation) -> MAAnnotationView {
        let identifier = "POIAnnotationView"
        var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier) as? MAPinAnnotationView
        
        if annotationView == nil {
            annotationView = MAPinAnnotationView(annotation: annotation, reuseIdentifier: identifier)
            annotationView?.pinColor = .red
            annotationView?.canShowCallout = true
        }
        
        return annotationView!
    }
}

// MARK: - Factory和Plugin
public class UserPointViewFactory: NSObject, FlutterPlatformViewFactory {
    public func create(withFrame frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) -> FlutterPlatformView {
        return UserPointViewController(frame: frame, viewIdentifier: viewId, arguments: args)
    }
    
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

public class UserPointViewPlugin: NSObject, FlutterPlugin {
    public static func register(with registrar: FlutterPluginRegistrar) {
        let factory = UserPointViewFactory()
        registrar.register(factory, withId: "plugin/choice-position-view")
    }
}
