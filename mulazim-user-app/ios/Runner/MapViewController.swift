//
//  MapViewController.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/12.
//
import UIKit
import Flutter
import AMapSearchKit
import AMapNaviKit

public class MapViewController: NSObject, FlutterPlatformView, AMapSearchDelegate, MAMapViewDelegate {
    // 声明变量
    private let containerView: UIView
    var search: AMapSearchAPI!
    var mapView: MAMapView!
    var startCoordinate: CLLocationCoordinate2D!
    var destinationCoordinate: CLLocationCoordinate2D!
    var restaurantLogoUrl: String?

    // 初始化方法
    public init(frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) {
        // 创建容器视图
        containerView = UIView(frame: frame)
        super.init()
        // 解析传递过来的参数
           if let params = args as? [String: Any] {
               if let startLatitudeString = params["user_latitude"] as? String,
                  let startLongitudeString = params["user_longitude"] as? String,
                  let destLatitudeString = params["store_latitude"] as? String,
                  let destLongitudeString = params["store_longitude"] as? String {
                                  if let startLatitude = Double(startLatitudeString),
                  let startLongitude = Double(startLongitudeString),
                  let destLatitude = Double(destLatitudeString),
                  let destLongitude = Double(destLongitudeString) {
                   startCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(startLatitude), longitude: CLLocationDegrees(startLongitude))
                   destinationCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(destLatitude), longitude: CLLocationDegrees(destLongitude))
                   
                   // 保存餐厅logo URL
                   restaurantLogoUrl = params["restaurant_logo"] as? String

                   } else {
                       print("无法解析坐标数据")
                       // 使用默认坐标数据
                       startCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(39.910267), longitude: CLLocationDegrees(116.370888))
                       destinationCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(39.989872), longitude: CLLocationDegrees(116.481956))
                   }
               } else {
                   print("缺少坐标参数")
                   // 使用默认坐标数据
                   startCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(39.910267), longitude: CLLocationDegrees(116.370888))
                   destinationCoordinate = CLLocationCoordinate2D(latitude: CLLocationDegrees(39.989872), longitude: CLLocationDegrees(116.481956))
               }
           } else {
               print("Flutter传过来的参数有误")
           }
        
        // 初始化高德地图的SDK，并设置APIKey
        let apiKey = "b246804aa0216430d4e64979bd0bb1e0"
        AMapServices.shared()?.apiKey = apiKey
        
        // 验证API Key设置是否成功
        if let currentKey = AMapServices.shared()?.apiKey {
            print("✅ API Key设置成功: \(currentKey == apiKey ? "匹配" : "不匹配")")
        } else {
            print("❌ API Key设置失败")
        }
        
        // 初始化地图
        initMapView()
        
        // 初始化路径规划对象，并设置代理
        search = AMapSearchAPI()
        search.delegate = self
        
        // 构造步行路径规划请求（按照官方文档规范）
        let request = AMapWalkingRouteSearchRequest()
        
        // 设置起点和终点 - 使用正确的坐标格式
        request.origin = AMapGeoPoint.location(withLatitude: CGFloat(startCoordinate!.latitude), longitude: CGFloat(startCoordinate!.longitude))
        request.destination = AMapGeoPoint.location(withLatitude: CGFloat(destinationCoordinate!.latitude), longitude: CGFloat(destinationCoordinate!.longitude))
        
        // 根据官方文档设置请求参数 - 尝试获取详细路径信息
        // 注意：某些属性可能在不同版本的SDK中不存在
        
        // 尝试设置扩展信息（如果支持）
        if request.responds(to: NSSelectorFromString("setRequireExtension:")) {
            request.setValue(true, forKey: "requireExtension")
            print("🔧 设置requireExtension为true")
        } else {
            print("⚠️ 当前SDK版本不支持requireExtension属性")
        }
        
        // 尝试设置显示字段类型（如果支持）
        if request.responds(to: NSSelectorFromString("setShowFieldsType:")) {
            // 尝试设置为返回所有字段
            request.setValue(NSNumber(value: 0xFFFFFFFF), forKey: "showFieldsType")
        } else {
            print("⚠️ 当前SDK版本不支持showFieldsType属性")
        }
        
        // 尝试设置多路径搜索（如果支持）
        if request.responds(to: NSSelectorFromString("setMultipath:")) {
            request.setValue(NSNumber(value: 1), forKey: "multipath")
        } else {
            print("⚠️ 当前SDK版本不支持multipath属性")
        }
        
        // 验证坐标有效性
        if !CLLocationCoordinate2DIsValid(startCoordinate!) {
            print("❌ 起点坐标无效: \(startCoordinate!)")
            showFallbackRoute()
            return
        }
        
        if !CLLocationCoordinate2DIsValid(destinationCoordinate!) {
            print("❌ 终点坐标无效: \(destinationCoordinate!)")
            showFallbackRoute()
            return
        }

        // 检查起点和终点的距离
        let distance = calculateDistance(from: startCoordinate!, to: destinationCoordinate!)
        
        if distance < 5 {
            showFallbackRoute()
            return
        } else if distance > 100000 {
            print("⚠️ 起终点距离太远(\(String(format: "%.2f", distance))米)，可能超出服务范围")
        }
        
        // 发送路径规划请求
        print("🌐 正在向高德服务器发送请求...")
        search.aMapWalkingRouteSearch(request)
        
        // 添加起点和终点标注点
        addDefaultAnnotations()
        
        // 添加捏合手势识别器
        let pinchGesture = UIPinchGestureRecognizer(target: self, action: #selector(handlePinchGesture(_:)))
        containerView.addGestureRecognizer(pinchGesture)
    }

    // 实现FlutterPlatformView协议中的方法
    public func view() -> UIView {
        return containerView
    }
    
    // 处理捏合手势
    @objc func handlePinchGesture(_ gesture: UIPinchGestureRecognizer) {
        if gesture.state == .changed {
            let scaleChange = gesture.scale - 1.0
            let newZoom = mapView.zoomLevel + scaleChange * 2.0
            // 限制缩放级别范围
            let clampedZoom = max(3.0, min(20.0, newZoom))
            mapView.setZoomLevel(clampedZoom, animated: false)
            gesture.scale = 1.0
        }
    }

    // 初始化地图方法
    func initMapView() {
        mapView = MAMapView(frame: containerView.bounds)
        mapView.delegate = self
        // 设置地图的初始缩放级别（稍后会根据路线自动调整）
        mapView.zoomLevel = 15
        // 设置地图中心点为起点和终点的中点
        let centerLat = (startCoordinate!.latitude + destinationCoordinate!.latitude) / 2
        let centerLng = (startCoordinate!.longitude + destinationCoordinate!.longitude) / 2
        mapView.centerCoordinate = CLLocationCoordinate2D(latitude: centerLat, longitude: centerLng)
        // 启用用户交互
        mapView.isUserInteractionEnabled = true
        mapView.isZoomEnabled = true
        mapView.isScrollEnabled = true
        containerView.addSubview(mapView)
    }

    // 实现AMapSearchDelegate协议中的方法
    public func onRouteSearchDone(_ request: AMapRouteSearchBaseRequest!, response: AMapRouteSearchResponse!) {
        // 先清除之前添加的覆盖物
        mapView.removeOverlays(mapView.overlays)

        // 详细检查响应数据
        guard let response = response else {
            print("❌ 响应为空")
            showFallbackRoute()
            return
        }
        
        // 检查路线数据
        guard let route = response.route else {
            showFallbackRoute()
            return
        }

        guard let paths = route.paths, !paths.isEmpty else {
            print("❌ 路线中没有路径数组或路径数组为空")
            showFallbackRoute()
            return
        }
        
        let path = paths[0] // 使用第一条路径
        var coordinates: [CLLocationCoordinate2D] = []
        
        // 检查是否有步骤数据
        guard let steps = path.steps, !steps.isEmpty else {
            print("❌ 路径中没有步骤数据")
            showFallbackRoute()
            return
        }
        
        // 解析每个步骤中的坐标点
        for (index, step) in steps.enumerated() {
            if let polyline = step.polyline, !polyline.isEmpty {
                let coords = polyline.components(separatedBy: ";")
                for coordString in coords {
                    let trimmedCoord = coordString.trimmingCharacters(in: .whitespacesAndNewlines)
                    if trimmedCoord.isEmpty { continue }
                    
                    let latLng = trimmedCoord.components(separatedBy: ",")
                    if latLng.count >= 2,
                       let longitude = Double(latLng[0].trimmingCharacters(in: .whitespacesAndNewlines)),
                       let latitude = Double(latLng[1].trimmingCharacters(in: .whitespacesAndNewlines)) {
                        
                        if latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180 {
                            let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
                            coordinates.append(coordinate)
                        }
                    }
                }
            }
        }
        
        // 确保路径至少包含起点和终点
        if coordinates.isEmpty {
            coordinates = [startCoordinate!, destinationCoordinate!]
        } else if coordinates.count == 1 {
            coordinates.append(destinationCoordinate!)
        }
        
        // 确保路径的第一个点精确匹配起点标记
        if !coordinates.isEmpty {
            coordinates[0] = startCoordinate!
        }
        
        // 确保路径的最后一个点精确匹配终点标记
        if coordinates.count > 1 {
            coordinates[coordinates.count - 1] = destinationCoordinate!
        }

        // 创建多段线
        if !coordinates.isEmpty {
            // 将坐标点分成多个小段
            let segmentSize = 3 // 每3个点创建一个箭头
            var segments: [[CLLocationCoordinate2D]] = []
            
            for i in stride(from: 0, to: coordinates.count - 1, by: segmentSize) {
                let endIndex = min(i + segmentSize, coordinates.count - 1)
                let segment = Array(coordinates[i...endIndex])
                segments.append(segment)
            }
            
            // 为每个段创建多段线
            for segment in segments {
                var segmentCoordinates = segment
                let polyline = MAMultiPolyline(coordinates: &segmentCoordinates, count: UInt(segment.count))
                if let polyline = polyline {
                    mapView.add(polyline)
                }
            }
            
            // 调整地图视野以显示路线和标注点
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
                guard let self = self else { return }
                let edgePadding = UIEdgeInsets(top: 60, left: 40, bottom: 60, right: 40)
                
                // 显示所有覆盖物
                if let overlays = self.mapView.overlays, !overlays.isEmpty {
                    self.mapView.showOverlays(overlays, edgePadding: edgePadding, animated: true)
                }
                
                // 确保标注点也在视野中
                if let annotations = self.mapView.annotations, !annotations.isEmpty {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.mapView.showAnnotations(annotations, edgePadding: edgePadding, animated: true)
                    }
                }
            }
        }
    }
    
    // 备用方案：显示直线路径
    private func showFallbackRoute() {

        // 创建直线路径（起点到终点），确保与标记点坐标完全一致
        var coordinates = [startCoordinate!, destinationCoordinate!]
        let polyline = MAPolyline(coordinates: &coordinates, count: UInt(coordinates.count))
        
        if let polyline = polyline {
            print("✅ 创建直线路径覆盖物")
            mapView.add(polyline)
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                guard let self = self else { return }
                let edgePadding = UIEdgeInsets(top: 60, left: 40, bottom: 60, right: 40)
                
                // 显示路径和标注点
                self.mapView.showOverlays([polyline], edgePadding: edgePadding, animated: true)
                
                if let annotations = self.mapView.annotations, !annotations.isEmpty {
                    self.mapView.showAnnotations(annotations, edgePadding: edgePadding, animated: true)
                }
            }
        } else {
            print("❌ 连直线路径都创建失败")
        }
    }

    // 处理路径搜索失败的情况
    public func aMapSearchRequest(_ request: Any!, didFailWithError error: Error!) {
        print("❌ 路径规划失败: \(String(describing: error))")
        
        // 详细错误信息
        if let nsError = error as NSError? {
            print("🔍 错误码: \(nsError.code)")
            print("🔍 错误域: \(nsError.domain)")
            print("🔍 错误描述: \(nsError.localizedDescription)")
            print("🔍 用户信息: \(nsError.userInfo)")
            
            // 常见错误码分析
            switch nsError.code {
            case 1800:
                print("⚠️ 可能是API Key无效或权限不足")
            case 1801:
                print("⚠️ 可能是网络连接问题")
            case 1802:
                print("⚠️ 可能是参数错误")
            case 1803:
                print("⚠️ 可能是查询超时")
            default:
                print("⚠️ 其他错误，错误码: \(nsError.code)")
            }
        }
        
        print("🔄 启用备用路径显示方案")
        
        // 路径规划失败时，显示直线路径
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.showFallbackRoute()
        }
    }

    // 实现MAMapViewDelegate协议中的方法，用于自定义覆盖物
    public func mapView(_ mapView: MAMapView!, rendererFor overlay: MAOverlay!) -> MAOverlayRenderer! {
        if overlay is MAPolyline {
            let renderer = MAPolylineRenderer(overlay: overlay)
            
            // 根据坐标点数量判断是否为真实路径
            if let polyline = overlay as? MAPolyline {
                let pointCount = polyline.pointCount
                print("🎨 绘制路线，包含\(pointCount)个点")
                
                if pointCount <= 2 {
                    // 直线路径 - 使用红色表示简化路径
                    renderer?.lineWidth = 6.0
                    renderer?.strokeColor = UIColor.systemBlue

                    renderer?.alpha = 0.8
                    // 设置箭头样式
//                    renderer?.lineCapType = kMALineCapArrow
                    renderer?.lineJoinType = kMALineJoinRound
                    print("📏 渲染简化路径（红色线条，\(pointCount)个点）")
                } else if pointCount <= 5 {
                    // 少量点的路径 - 使用橙色表示估算路径
                    renderer?.lineWidth = 8.0
                    renderer?.strokeColor = UIColor.systemBlue

                    renderer?.alpha = 0.8
                    // 设置箭头样式
//                    renderer?.lineCapType = kMALineCapArrow
                    renderer?.lineJoinType = kMALineJoinRound
                    print("📏 渲染估算路径（橙色线条，\(pointCount)个点）")
                } else {
                    // 真实详细路径 - 使用蓝色实线
                    renderer?.lineWidth = 10.0
                    renderer?.strokeColor = UIColor.systemBlue

                    renderer?.alpha = 0.9
                    // 设置箭头样式
//                    renderer?.lineCapType = kMALineCapArrow
                    renderer?.lineJoinType = kMALineJoinRound
                    print("📏 渲染详细步行路径（蓝色实线，\(pointCount)个点）")
                }
            }
            
            return renderer
        }
        return nil
    }
    
    // 自定义标注点样式
    public func mapView(_ mapView: MAMapView!, viewFor annotation: MAAnnotation!) -> MAAnnotationView! {
        if annotation is MAPointAnnotation {
            let pointAnnotation = annotation as! MAPointAnnotation
            
            if pointAnnotation.title == "起点" {
                // 起点使用自定义的用户头像图标
                let identifier = "UserPin"
                var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
                
                if annotationView == nil {
                    annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    annotationView?.canShowCallout = true
                    annotationView?.centerOffset = CGPoint(x: 0, y: -20) // 调整位置使图标底部对准坐标点
                }
                
                // 设置用户头像图标
                let userIcon = createModernUserLocationIcon()
                annotationView?.image = userIcon
                
                print("✅ 用户位置图标设置成功")
                return annotationView
                
            } else if pointAnnotation.title == "终点" {
                // 终点使用自定义餐厅logo
                let identifier = "RestaurantPin"
                var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
                
                if annotationView == nil {
                    annotationView = MAAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    annotationView?.canShowCallout = true
                    annotationView?.centerOffset = CGPoint(x: 0, y: -20) // 调整位置使图标底部对准坐标点
                }
                
                // 设置餐厅logo图片
                loadRestaurantLogo { [weak annotationView] image in
                    DispatchQueue.main.async {
                        if let logoImage = image {
                            // 调整图片大小
                            let targetSize = CGSize(width: 40, height: 40)
                            let resizedImage = self.resizeImage(image: logoImage, targetSize: targetSize)
                            
                            // 创建圆形图片
                            let circularImage = self.makeCircularImage(image: resizedImage)
                            annotationView?.image = circularImage
                            
                            print("✅ 餐厅logo设置成功")
                        } else {
                            // 如果logo加载失败，使用默认的红色大头针
                            print("⚠️ 餐厅logo加载失败，使用默认标记")
                            if let pinView = annotationView as? MAPinAnnotationView {
                                pinView.pinColor = .red
                            }
                        }
                    }
                }
                
                return annotationView
            }
        }
        return nil
    }

    // 添加起点和终点标注点的方法
    func addDefaultAnnotations() {
        let startAnnotation = MAPointAnnotation()
        startAnnotation.coordinate = startCoordinate!
        startAnnotation.title = "起点"
        startAnnotation.subtitle = "用户位置"
        mapView.addAnnotation(startAnnotation)
        
        let destinationAnnotation = MAPointAnnotation()
        destinationAnnotation.coordinate = destinationCoordinate!
        destinationAnnotation.title = "终点"
        destinationAnnotation.subtitle = "餐厅位置"
        mapView.addAnnotation(destinationAnnotation)
        
        // 初始显示所有标注点，稍后会在路径规划完成后重新调整
        mapView.showAnnotations([startAnnotation, destinationAnnotation], animated: false)
    }
    
    // 计算两点之间的距离（米）
    private func calculateDistance(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D) -> Double {
        let fromLocation = CLLocation(latitude: from.latitude, longitude: from.longitude)
        let toLocation = CLLocation(latitude: to.latitude, longitude: to.longitude)
        return fromLocation.distance(from: toLocation)
    }
    
    // 根据指令估算中间点坐标
    private func calculateIntermediatePoint(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D, distance: Int, instruction: String) -> CLLocationCoordinate2D {
        // 计算总距离
        let totalDistance = calculateDistance(from: from, to: to)
        
        // 如果步骤距离大于总距离，使用比例
        let ratio = min(Double(distance) / totalDistance, 1.0)
        
        // 根据方向调整比例
        var adjustedRatio = ratio
        
        // 简单的方向解析（可以根据需要扩展）
        if instruction.contains("右转") || instruction.contains("右拐") {
            // 右转可能需要在路径中间偏右一点
            adjustedRatio = ratio * 0.6 // 提前一些转向
        } else if instruction.contains("左转") || instruction.contains("左拐") {
            // 左转可能需要在路径中间偏左一点
            adjustedRatio = ratio * 0.6 // 提前一些转向
        }
        
        // 线性插值计算中间点
        let lat = from.latitude + (to.latitude - from.latitude) * adjustedRatio
        let lng = from.longitude + (to.longitude - from.longitude) * adjustedRatio
        
        // 根据方向微调坐标（模拟真实道路的转弯）
        var adjustedLat = lat
        var adjustedLng = lng
        
        if instruction.contains("东北") {
            adjustedLat += 0.0001 * adjustedRatio
            adjustedLng += 0.0001 * adjustedRatio
        } else if instruction.contains("东南") {
            adjustedLat -= 0.0001 * adjustedRatio
            adjustedLng += 0.0001 * adjustedRatio
        } else if instruction.contains("西北") {
            adjustedLat += 0.0001 * adjustedRatio
            adjustedLng -= 0.0001 * adjustedRatio
        } else if instruction.contains("西南") {
            adjustedLat -= 0.0001 * adjustedRatio
            adjustedLng -= 0.0001 * adjustedRatio
        }
        
        return CLLocationCoordinate2D(latitude: adjustedLat, longitude: adjustedLng)
    }
    
    // 根据进度计算路径上的点（用于中间步骤）
    private func calculateProgressivePoint(from: CLLocationCoordinate2D, to: CLLocationCoordinate2D, progress: Double, instruction: String) -> CLLocationCoordinate2D {
        // 基础线性插值
        let baseLat = from.latitude + (to.latitude - from.latitude) * progress
        let baseLng = from.longitude + (to.longitude - from.longitude) * progress
        
        // 根据指令添加一些随机性，模拟真实道路的弯曲
        var adjustedLat = baseLat
        var adjustedLng = baseLng
        
        // 根据指令内容调整路径，使其看起来更真实
        let offset = 0.0002 * progress * (1 - progress) // 在中间部分产生最大偏移
        
        if instruction.contains("右转") || instruction.contains("右拐") {
            adjustedLng += offset // 向右偏移
        } else if instruction.contains("左转") || instruction.contains("左拐") {
            adjustedLng -= offset // 向左偏移
        }
        
        if instruction.contains("东") {
            adjustedLng += offset * 0.5
        } else if instruction.contains("西") {
            adjustedLng -= offset * 0.5
        }
        
        if instruction.contains("北") {
            adjustedLat += offset * 0.5
        } else if instruction.contains("南") {
            adjustedLat -= offset * 0.5
        }
        
        return CLLocationCoordinate2D(latitude: adjustedLat, longitude: adjustedLng)
    }
    
    // 加载餐厅logo图片
    private func loadRestaurantLogo(completion: @escaping (UIImage?) -> Void) {
        guard let logoUrlString = restaurantLogoUrl,
              let logoUrl = URL(string: logoUrlString) else {
            print("❌ 餐厅logo URL无效")
            completion(nil)
            return
        }
        
        print("🖼️ 开始加载餐厅logo: \(logoUrlString)")
        
        // 异步下载图片
        URLSession.shared.dataTask(with: logoUrl) { data, response, error in
            if let error = error {
                print("❌ 餐厅logo下载失败: \(error.localizedDescription)")
                completion(nil)
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                print("❌ 餐厅logo数据无效")
                completion(nil)
                return
            }
            
            print("✅ 餐厅logo下载成功")
            completion(image)
        }.resume()
    }
    
    // 调整图片大小
    private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
    }
    
    // 创建圆形图片
    private func makeCircularImage(image: UIImage) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: image.size)
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: image.size)
            
            // 创建圆形裁剪路径
            context.cgContext.addEllipse(in: rect)
            context.cgContext.clip()
            
            // 绘制图片
            image.draw(in: rect)
            
            // 添加边框
            context.cgContext.setStrokeColor(UIColor.white.cgColor)
            context.cgContext.setLineWidth(2.0)
            context.cgContext.addEllipse(in: rect.insetBy(dx: 1, dy: 1))
            context.cgContext.strokePath()
        }
    }
    
    // 创建用户位置图标
    private func createUserLocationIcon() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let ctx = context.cgContext
            
            // 绘制外圆（蓝色背景）
            ctx.setFillColor(UIColor.systemBlue.cgColor)
            ctx.fillEllipse(in: rect)
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(3.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 1.5, dy: 1.5))
            
            // 绘制用户图标（简化的人形图标）
            ctx.setFillColor(UIColor.white.cgColor)
            
            // 头部 - 圆形
            let headRadius: CGFloat = 6
            let headCenter = CGPoint(x: size.width / 2, y: size.height / 2 - 4)
            ctx.fillEllipse(in: CGRect(
                x: headCenter.x - headRadius,
                y: headCenter.y - headRadius,
                width: headRadius * 2,
                height: headRadius * 2
            ))
            
            // 身体 - 梯形
            let bodyPath = CGMutablePath()
            let bodyTop = headCenter.y + headRadius + 2
            let bodyBottom = size.height - 8
            let bodyWidth: CGFloat = 12
            let shoulderWidth: CGFloat = 8
            
            bodyPath.move(to: CGPoint(x: headCenter.x - shoulderWidth/2, y: bodyTop))
            bodyPath.addLine(to: CGPoint(x: headCenter.x + shoulderWidth/2, y: bodyTop))
            bodyPath.addLine(to: CGPoint(x: headCenter.x + bodyWidth/2, y: bodyBottom))
            bodyPath.addLine(to: CGPoint(x: headCenter.x - bodyWidth/2, y: bodyBottom))
            bodyPath.closeSubpath()
            
            ctx.addPath(bodyPath)
            ctx.fillPath()
        }
    }
    
    // 创建现代化的用户位置图标（使用SF Symbols风格）
    private func createModernUserLocationIcon() -> UIImage {
        let size = CGSize(width: 40, height: 40)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)
            let ctx = context.cgContext
            
            // 绘制外圆阴影
            ctx.setShadow(offset: CGSize(width: 0, height: 2), blur: 4, color: UIColor.black.withAlphaComponent(0.3).cgColor)
            ctx.setFillColor(UIColor.systemBlue.cgColor)
            ctx.fillEllipse(in: rect.insetBy(dx: 2, dy: 2))
            
            // 重置阴影
            ctx.setShadow(offset: .zero, blur: 0, color: nil)
            
            // 绘制主背景圆
            ctx.setFillColor(UIColor.systemBlue.cgColor)
            ctx.fillEllipse(in: rect.insetBy(dx: 2, dy: 2))
            
            // 绘制白色边框
            ctx.setStrokeColor(UIColor.white.cgColor)
            ctx.setLineWidth(2.0)
            ctx.strokeEllipse(in: rect.insetBy(dx: 3, dy: 3))
            
                         // 尝试使用SF Symbols图标，如果失败则使用自定义绘制
             if #available(iOS 13.0, *) {
                 // 使用SF Symbols的person.fill图标
                 if let personSymbol = UIImage(systemName: "person.fill") {
                     let symbolSize = CGSize(width: 20, height: 20)
                     let symbolRect = CGRect(
                         x: (size.width - symbolSize.width) / 2,
                         y: (size.height - symbolSize.height) / 2,
                         width: symbolSize.width,
                         height: symbolSize.height
                     )
                     
                     // 使用UIImage的draw方法，自动处理坐标系
                     let whiteSymbol = personSymbol.withTintColor(.white, renderingMode: .alwaysOriginal)
                     whiteSymbol.draw(in: symbolRect)
                 } else {
                     // 如果SF Symbol不可用，绘制自定义用户图标
                     drawCustomUserIcon(in: ctx, rect: rect)
                 }
             } else {
                 // iOS 13以下版本，绘制自定义用户图标
                 drawCustomUserIcon(in: ctx, rect: rect)
             }
        }
    }
    
    // 绘制自定义用户图标的辅助方法
    private func drawCustomUserIcon(in ctx: CGContext, rect: CGRect) {
        ctx.setFillColor(UIColor.white.cgColor)
        
        let size = rect.size
        
        // 头部 - 圆形
        let headRadius: CGFloat = 7
        let headCenter = CGPoint(x: size.width / 2, y: size.height / 2 - 3)
        ctx.fillEllipse(in: CGRect(
            x: headCenter.x - headRadius,
            y: headCenter.y - headRadius,
            width: headRadius * 2,
            height: headRadius * 2
        ))
        
        // 身体 - 更圆润的形状
        let bodyPath = CGMutablePath()
        let bodyTop = headCenter.y + headRadius + 1
        let bodyBottom = size.height - 6
        let bodyWidth: CGFloat = 14
        let shoulderWidth: CGFloat = 10
        
        // 创建更平滑的身体轮廓
        bodyPath.move(to: CGPoint(x: headCenter.x - shoulderWidth/2, y: bodyTop))
        bodyPath.addCurve(
            to: CGPoint(x: headCenter.x + shoulderWidth/2, y: bodyTop),
            control1: CGPoint(x: headCenter.x - shoulderWidth/4, y: bodyTop - 1),
            control2: CGPoint(x: headCenter.x + shoulderWidth/4, y: bodyTop - 1)
        )
        bodyPath.addLine(to: CGPoint(x: headCenter.x + bodyWidth/2, y: bodyBottom))
        bodyPath.addCurve(
            to: CGPoint(x: headCenter.x - bodyWidth/2, y: bodyBottom),
            control1: CGPoint(x: headCenter.x + bodyWidth/4, y: bodyBottom + 1),
            control2: CGPoint(x: headCenter.x - bodyWidth/4, y: bodyBottom + 1)
        )
        bodyPath.closeSubpath()
        
        ctx.addPath(bodyPath)
        ctx.fillPath()
    }
}

// 实现FlutterPlatformViewFactory协议
public class MapViewFactory: NSObject, FlutterPlatformViewFactory {
    // 创建MapViewController对象
    public func create(withFrame frame: CGRect, viewIdentifier viewId: Int64, arguments args: Any?) -> FlutterPlatformView {
        return MapViewController(frame: frame, viewIdentifier: viewId, arguments: args)
    }

    // 创建消息编解码器
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

// 实现FlutterPlugin协议（未实现具体功能）
public class MapViewPlugin: NSObject, FlutterPlugin {
    // 注册插件
    public static func register(with registrar: FlutterPluginRegistrar) {
        let factory = MapViewFactory()
        registrar.register(factory, withId: "plugin/mapview")
    }

    // 处理Flutter调用插件的方法（未实现具体功能）
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(FlutterMethodNotImplemented)
    }
}
