<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Mulazim 美滋来</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>user_app</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>weixin</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>wxedfd69db270adcf6</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>weixin</string>
			<string>weixinULAPI</string>
			<string>weixinURLParamsAPI</string>
			<string>baidumap</string>
			<string>iosamap</string>
			<string>qqmap</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
			<key>NSAllowsArbitraryLoadsInWebContent</key>
			<true/>
			<key>NSAllowsLocalNetworking</key>
			<true/>
		</dict>
		<key>NSBonjourServices</key>
		<array>
			<string>_smart._tcp</string>
			<string>_smart._udp</string>
		</array>
		<key>NSCameraUsageDescription</key>
		<string>需要使用您的相机来拍摄头像照片</string>
		<key>NSLocalNetworkUsageDescription</key>
		<string>App需要访问本地网络</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>需要使用您的位置信息以显示地图和获取位置信息</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>需要使用您的位置信息以显示地图和获取位置信息</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>需要使用您的位置信息以显示地图和获取位置信息</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>需要访问您的相册来保存海报图片</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>需要访问您的相册来选择头像照片</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleLightContent</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
	</dict>
</plist>
