import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    
    // 注册地图视图插件
      MapViewPlugin.register(with: registrar(forPlugin: "plugin/mapview")!)
      ShipperPointViewPlugin.register(with: registrar(forPlugin: "plugin/shipper-location")!)
      UserPointViewPlugin.register(with: registrar(forPlugin: "plugin/choice-position-view")!)
      OrderMapViewPlugin.register(with: registrar(forPlugin: "plugin/order-map")!)
    
    // 设置方法通道
    setupMethodChannel()
    
    // 配置JPush
    if #available(iOS 13.0.0, *) {
        guard let controller = window?.rootViewController as? FlutterViewController else {
            return super.application(application, didFinishLaunchingWithOptions: launchOptions)
        }
        JPushManager.shared.configure(with: controller, launchOptions: launchOptions)
    }
    
    // 注册前台任务插件（如果需要的话）
    // SwiftFlutterForegroundTaskPlugin.setPluginRegistrantCallback(registerPlugins)
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  /// 设置方法通道
  private func setupMethodChannel() {
    guard let controller = window?.rootViewController as? FlutterViewController else { return }
    
    let methodChannel = FlutterMethodChannel(
      name: "com.almas.dinner",
      binaryMessenger: controller.binaryMessenger
    )
    
    methodChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      self?.handleMethodCall(call, result: result)
    }
  }
  
  /// 处理方法调用
  private func handleMethodCall(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "isAppInstalled":
      if let arguments = call.arguments as? [String: Any],
         let packageName = arguments["packageName"] as? String {
        let isInstalled = isAppInstalled(urlScheme: packageName)
        result(isInstalled)
      } else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
      }
      
    case "openLocationSettings":
      openLocationSettings()
      result(true)

    default:
      result(FlutterMethodNotImplemented)
    }
  }
  
  /// 检查应用是否已安装
  /// - Parameter urlScheme: URL Scheme (如: "iosamap://", "baidumap://", "qqmap://")
  /// - Returns: 是否已安装
  private func isAppInstalled(urlScheme: String) -> Bool {
    guard let url = URL(string: urlScheme) else { return false }
    return UIApplication.shared.canOpenURL(url)
  }
  
  /// 打开位置设置
  private func openLocationSettings() {
    guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else { return }
    
    if UIApplication.shared.canOpenURL(settingsUrl) {
      UIApplication.shared.open(settingsUrl, options: [:], completionHandler: nil)
    }
  }
  
  // MARK: - JPush 相关代理方法
  override func applicationWillEnterForeground(_ application: UIApplication) {
    if #available(iOS 13.0.0, *) {
      JPushManager.shared.applicationWillEnterForeground()
    }
  }
  
  override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    if #available(iOS 13.0.0, *) {
      JPushManager.shared.didRegisterForRemoteNotificationsWithDeviceToken(deviceToken)
    }
  }
  
  override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
    if #available(iOS 13.0.0, *) {
      JPushManager.shared.didFailToRegisterForRemoteNotificationsWithError(error)
    }
  }
  
  override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    if #available(iOS 13.0.0, *) {
      JPushManager.shared.didReceiveRemoteNotification(userInfo, fetchCompletionHandler: completionHandler)
    } else {
      completionHandler(.newData)
    }
  }
}

// MARK: - 插件注册
func registerPlugins(registry: FlutterPluginRegistry) {
    GeneratedPluginRegistrant.register(with: registry)
}
