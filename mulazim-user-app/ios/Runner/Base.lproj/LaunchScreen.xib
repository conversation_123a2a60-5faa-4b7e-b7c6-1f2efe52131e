<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="System colors in document" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchImage" translatesAutoresizingMaskIntoConstraints="NO" id="kIdg3uBxKPQ">
                    <rect key="frame" x="146.66666666666666" y="369.66666666666669" width="100" height="113"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="Tc2-Cn-kHI"/>
                        <constraint firstAttribute="height" constant="113" id="eQL-73-9sJ"/>
                    </constraints>
                </imageView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="kIdg3uBxKPQ" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="1a2-6s-vTC"/>
                <constraint firstItem="kIdg3uBxKPQ" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="4X2-HB-R7a"/>
            </constraints>
            <point key="canvasLocation" x="548" y="285"/>
        </view>
    </objects>
    <resources>
        <image name="LaunchImage" width="108" height="213"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document> 