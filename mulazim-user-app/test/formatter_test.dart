import 'package:flutter_test/flutter_test.dart';
import 'package:user_app/core/utils/formatter.dart';
import 'package:user_app/data/models/address/street_list_model.dart';

void main() {
  group('Formatter Tests', () {
    test('Chinese pinyin grouping should work correctly', () {
      // 创建测试数据
      List<StreetListData> testData = [
        StreetListData(id: 1, name: '北京路'),
        StreetListData(id: 2, name: '上海路'),
        StreetListData(id: 3, name: '广州路'),
        StreetListData(id: 4, name: '深圳路'),
        StreetListData(id: 5, name: '杭州路'),
        StreetListData(id: 6, name: '南京路'),
        StreetListData(id: 7, name: '武汉路'),
        StreetListData(id: 8, name: '成都路'),
        StreetListData(id: 9, name: '西安路'),
        StreetListData(id: 10, name: '重庆路'),
        StreetListData(id: 11, name: '123路'),
        StreetListData(id: 12, name: 'ABC路'),
      ];

      // 测试中文拼音分组 (langId = 2)
      var formatter = Formatter(2);
      var result = formatter.formatList(testData);

      // 验证结果
      expect(result, isNotEmpty);
      
      // 检查是否按拼音首字母分组
      bool hasBGroup = false;
      bool hasSGroup = false;
      bool hasGGroup = false;
      bool hasHGroup = false;
      bool hasXGroup = false;
      bool hasCGroup = false;
      bool hasNumberGroup = false;
      bool hasLetterGroup = false;

      for (var group in result) {
        String char = group['char'];
        List items = group['items'];
        
        if (char == 'B') {
          hasBGroup = true;
          expect(items.any((item) => item.name == '北京路'), isTrue);
        } else if (char == 'S') {
          hasSGroup = true;
          expect(items.any((item) => item.name == '上海路'), isTrue);
          expect(items.any((item) => item.name == '深圳路'), isTrue);
        } else if (char == 'G') {
          hasGGroup = true;
          expect(items.any((item) => item.name == '广州路'), isTrue);
        } else if (char == 'H') {
          hasHGroup = true;
          expect(items.any((item) => item.name == '杭州路'), isTrue);
        } else if (char == 'X') {
          hasXGroup = true;
          expect(items.any((item) => item.name == '西安路'), isTrue);
        } else if (char == 'C') {
          hasCGroup = true;
          expect(items.any((item) => item.name == '成都路'), isTrue);
          expect(items.any((item) => item.name == '重庆路'), isTrue);
        } else if (char == '#') {
          hasNumberGroup = true;
          expect(items.any((item) => item.name == '123路'), isTrue);
        } else if (char == 'A') {
          hasLetterGroup = true;
          expect(items.any((item) => item.name == 'ABC路'), isTrue);
        }
      }

      // 验证分组是否正确
      expect(hasBGroup, isTrue);
      expect(hasSGroup, isTrue);
      expect(hasGGroup, isTrue);
      expect(hasHGroup, isTrue);
      expect(hasXGroup, isTrue);
      expect(hasCGroup, isTrue);
      expect(hasNumberGroup, isTrue);
      expect(hasLetterGroup, isTrue);
    });

    test('Uyghur grouping should work correctly', () {
      // 创建维吾尔文测试数据
      List<StreetListData> testData = [
        StreetListData(id: 1, name: 'ئاۋات'),
        StreetListData(id: 2, name: 'باغ'),
        StreetListData(id: 3, name: 'تاش'),
        StreetListData(id: 4, name: '123'),
      ];

      // 测试维吾尔文分组 (langId = 1)
      var formatter = Formatter(1);
      var result = formatter.formatList(testData);

      // 验证结果
      expect(result, isNotEmpty);
      
      // 检查维吾尔文分组
      bool hasAwatGroup = false;
      bool hasBagGroup = false;
      bool hasTashGroup = false;
      bool hasNumberGroup = false;

      for (var group in result) {
        String char = group['char'];
        List items = group['items'];
        
        if (char == 'ئا') {
          hasAwatGroup = true;
          expect(items.any((item) => item.name == 'ئاۋات'), isTrue);
        } else if (char == 'ب') {
          hasBagGroup = true;
          expect(items.any((item) => item.name == 'باغ'), isTrue);
        } else if (char == 'ت') {
          hasTashGroup = true;
          expect(items.any((item) => item.name == 'تاش'), isTrue);
        } else if (char == '#') {
          hasNumberGroup = true;
          expect(items.any((item) => item.name == '123'), isTrue);
        }
      }

      // 验证分组是否正确
      expect(hasAwatGroup, isTrue);
      expect(hasBagGroup, isTrue);
      expect(hasTashGroup, isTrue);
      expect(hasNumberGroup, isTrue);
    });
  });
} 