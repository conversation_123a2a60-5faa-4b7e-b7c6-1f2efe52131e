import 'package:user_app/data/models/home/<USER>';

class HomeNoticeModel {
  HomeNoticeData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  HomeNoticeModel({this.data, this.lang, this.msg, this.status, this.time});

  HomeNoticeModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new HomeNoticeData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class HomeNoticeData {
  List<PopupAdver>? popupAdver;
  LocationInfo? location;
  List<CouponListItem>? coupon;
  List<CouponListItem>? couponPin;
  List<Null>? lottery;
  int? lotteryChanceCount;
  List<Null>? lotteryChanceItems;
  int? springCouponActive;
  List<Null>? springCoupons;
  int? canTakeSpringCoupon;
  int? isLogIn;

  AppConfigData? appConfig;

  HomeNoticeData(
      {
        this.popupAdver,
        this.location,
        this.coupon,
        this.couponPin,
        this.lottery,
        this.lotteryChanceCount,
        this.lotteryChanceItems,
        this.springCouponActive,
        this.springCoupons,
        this.canTakeSpringCoupon,
        this.isLogIn,
        this.appConfig});

  HomeNoticeData.fromJson(Map<String, dynamic> json) {

    if (json['popup_adver'] != null) {
      popupAdver = <PopupAdver>[];
      json['popup_adver'].forEach((v) {
        popupAdver!.add(new PopupAdver.fromJson(v));
      });
    }

    if (json['coupon'] != null) {
      coupon = <CouponListItem>[];
      json['coupon'].forEach((v) {
        coupon!.add(new CouponListItem.fromJson(v));
      });
    }
    if (json['coupon_pin'] != null) {
      couponPin = <CouponListItem>[];
      json['coupon_pin'].forEach((v) {
        couponPin!.add(new CouponListItem.fromJson(v));
      });
    }

    location = json['location'] != null
        ? new LocationInfo.fromJson(json['location'])
        : null;
    lotteryChanceCount = json['lottery_chance_count'];
    springCouponActive = json['spring_coupon_active'];
    canTakeSpringCoupon = json['can_take_spring_coupon'];
    isLogIn = json['is_log_in'];
    appConfig = json['app_config'] != null
        ? AppConfigData.fromJson(json['app_config'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['lottery_chance_count'] = this.lotteryChanceCount;
    data['spring_coupon_active'] = this.springCouponActive;
    data['can_take_spring_coupon'] = this.canTakeSpringCoupon;
    data['is_log_in'] = this.isLogIn;
    data['app_config'] = this.appConfig?.toJson();
    return data;
  }
}

/// 应用配置
class AppConfigData {
  /// 是否显示企业微信二维码
  int? app_show_enterprise_wechat_qr_code;

  AppConfigData.fromJson(Map<String, dynamic> json) {
    app_show_enterprise_wechat_qr_code = json['app_show_enterprise_wechat_qr_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['app_show_enterprise_wechat_qr_code'] = this.app_show_enterprise_wechat_qr_code;
    return data;
  }
}



class CouponListItem {
  int? id;
  String? name;
  int? cityId;
  int? areaId;
  num? price;
  int? couponId;
  int? state;
  num? minPrice;
  int? count;
  int? takenCount;
  String? startTime;
  String? endTime;
  String? startUseTime;
  String? endUseTime;
  String? notice;
  int? modelId;
  int? customerType;

  CouponListItem({this.id,
    this.name,
    this.cityId,
    this.areaId,
    this.price,
    this.couponId,
    this.state,
    this.minPrice,
    this.count,
    this.takenCount,
    this.startTime,
    this.endTime,
    this.startUseTime,
    this.endUseTime,
    this.notice,
    this.modelId,
    this.customerType});

  CouponListItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    cityId = json['city_id'];
    areaId = json['area_id'];
    price = json['price'];
    couponId = json['coupon_id'];
    state = json['state'];
    minPrice = json['min_price'];
    count = json['count'];
    takenCount = json['taken_count'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    startUseTime = json['start_use_time'];
    endUseTime = json['end_use_time'];
    notice = json['notice'];
    modelId = json['model_id'];
    customerType = json['customer_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['city_id'] = this.cityId;
    data['area_id'] = this.areaId;
    data['price'] = this.price;
    data['coupon_id'] = this.couponId;
    data['state'] = this.state;
    data['min_price'] = this.minPrice;
    data['count'] = this.count;
    data['taken_count'] = this.takenCount;
    data['start_time'] = this.startTime;
    data['end_time'] = this.endTime;
    data['start_use_time'] = this.startUseTime;
    data['end_use_time'] = this.endUseTime;
    data['notice'] = this.notice;
    data['model_id'] = this.modelId;
    data['customer_type'] = this.customerType;
    return data;
  }
}


class LocationInfo {
  int? id;
  num? lat;
  num? lng;
  num? distance;
  String? buildingName;
  String? buildingNameZh;
  int? areaId;
  int? cityId;
  int? streetId;
  String? areaName;
  String? areaNameZh;
  String? cityName;
  String? servicePhone;
  String? areaRestingStartTime;
  String? areaRestingEndTime;
  String? areaRestingContentUg;
  String? areaRestingContentZh;
  int? rankState;
  int? workWechatState;
  String? workWechatQrcode;
  String? workWechatPoster;
  bool? canDeleteAccount;

  LocationInfo(
      {this.id,
        this.lat,
        this.lng,
        this.distance,
        this.buildingName,
        this.buildingNameZh,
        this.areaId,
        this.cityId,
        this.streetId,
        this.areaName,
        this.areaNameZh,
        this.cityName,
        this.servicePhone,
        this.areaRestingStartTime,
        this.areaRestingEndTime,
        this.areaRestingContentUg,
        this.areaRestingContentZh,
        this.rankState,
        this.workWechatState,
        this.workWechatQrcode,
        this.workWechatPoster,
        this.canDeleteAccount});

  LocationInfo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    lat = json['lat'];
    lng = json['lng'];
    distance = json['distance'];
    buildingName = json['building_name'];
    buildingNameZh = json['building_name_zh'];
    areaId = json['area_id'];
    cityId = json['city_id'];
    streetId = json['street_id'];
    areaName = json['area_name'];
    areaNameZh = json['area_name_zh'];
    cityName = json['city_name'];
    servicePhone = json['service_phone'];
    areaRestingStartTime = json['area_resting_start_time'];
    areaRestingEndTime = json['area_resting_end_time'];
    areaRestingContentUg = json['area_resting_content_ug'];
    areaRestingContentZh = json['area_resting_content_zh'];
    rankState = json['rank_state'];
    workWechatState = json['work_wechat_state'];
    workWechatQrcode = json['work_wechat_qrcode'];
    workWechatPoster = json['work_wechat_poster'];
    canDeleteAccount = json['can_delete_account'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['lat'] = this.lat;
    data['lng'] = this.lng;
    data['distance'] = this.distance;
    data['building_name'] = this.buildingName;
    data['building_name_zh'] = this.buildingNameZh;
    data['area_id'] = this.areaId;
    data['city_id'] = this.cityId;
    data['street_id'] = this.streetId;
    data['area_name'] = this.areaName;
    data['area_name_zh'] = this.areaNameZh;
    data['city_name'] = this.cityName;
    data['service_phone'] = this.servicePhone;
    data['area_resting_start_time'] = this.areaRestingStartTime;
    data['area_resting_end_time'] = this.areaRestingEndTime;
    data['area_resting_content_ug'] = this.areaRestingContentUg;
    data['area_resting_content_zh'] = this.areaRestingContentZh;
    data['rank_state'] = this.rankState;
    data['work_wechat_state'] = this.workWechatState;
    data['work_wechat_qrcode'] = this.workWechatQrcode;
    data['work_wechat_poster'] = this.workWechatPoster;
    data['can_delete_account'] = this.canDeleteAccount;
    return data;
  }
}

class Restaurant {
  int? id;
  String? tag;
  int? dealerId;
  int? cityId;
  int? areaId;
  int? streetId;
  String? nameUg;
  String? nameZh;
  String? address;
  String? addressUg;
  String? addressZh;
  String? name;
  String? description;
  String? descriptionUg;
  String? descriptionZh;
  Null? fullNameZh;
  Null? fullNameUg;
  int? famous;
  String? logo;
  int? lat;
  int? lng;
  String? adminTel;
  String? tel;
  String? tel2;
  String? tel3;
  String? tel4;
  String? tel5;
  String? openTime;
  String? closeTime;
  String? beginTime;
  String? endTime;
  int? hasStamp;
  Null? mark;
  int? shipperAvg;
  int? monthOrderCount;
  int? printLang;
  int? weight;
  Null? openId;
  int? type;
  int? starAvg;
  int? foodStarAvg;
  int? boxStarAvg;
  int? commentCount;
  int? state;
  String? startBusiness;
  String? lastCommentReaded;
  int? isCashouting;
  int? selfOfferLunchBox;
  Null? lastQueryTime;
  Null? deviceToken;
  int? rankState;
  Null? score;
  int? resDealerYourselfTakePercent;
  int? resDealerPercent;
  int? resMpYourselfTakePercent;
  int? resMpPercent;
  int? canCashOut;
  int? displayShipment;
  int? displayMerchantProfit;
  String? createdAt;
  String? updatedAt;
  Null? deletedAt;
  int? restaurantType;
  int? canSelfTake;
  int? canMulazimTake;
  int? cashPlatform;
  int? payPlatform;
  int? cashPerDay;
  int? lunchBoxRate;
  int? deliveryTime;

  Restaurant(
      {this.id,
        this.tag,
        this.dealerId,
        this.cityId,
        this.areaId,
        this.streetId,
        this.nameUg,
        this.nameZh,
        this.address,
        this.addressUg,
        this.addressZh,
        this.name,
        this.description,
        this.descriptionUg,
        this.descriptionZh,
        this.fullNameZh,
        this.fullNameUg,
        this.famous,
        this.logo,
        this.lat,
        this.lng,
        this.adminTel,
        this.tel,
        this.tel2,
        this.tel3,
        this.tel4,
        this.tel5,
        this.openTime,
        this.closeTime,
        this.beginTime,
        this.endTime,
        this.hasStamp,
        this.mark,
        this.shipperAvg,
        this.monthOrderCount,
        this.printLang,
        this.weight,
        this.openId,
        this.type,
        this.starAvg,
        this.foodStarAvg,
        this.boxStarAvg,
        this.commentCount,
        this.state,
        this.startBusiness,
        this.lastCommentReaded,
        this.isCashouting,
        this.selfOfferLunchBox,
        this.lastQueryTime,
        this.deviceToken,
        this.rankState,
        this.score,
        this.resDealerYourselfTakePercent,
        this.resDealerPercent,
        this.resMpYourselfTakePercent,
        this.resMpPercent,
        this.canCashOut,
        this.displayShipment,
        this.displayMerchantProfit,
        this.createdAt,
        this.updatedAt,
        this.deletedAt,
        this.restaurantType,
        this.canSelfTake,
        this.canMulazimTake,
        this.cashPlatform,
        this.payPlatform,
        this.cashPerDay,
        this.lunchBoxRate,
        this.deliveryTime});

  Restaurant.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tag = json['tag'];
    dealerId = json['dealer_id'];
    cityId = json['city_id'];
    areaId = json['area_id'];
    streetId = json['street_id'];
    nameUg = json['name_ug'];
    nameZh = json['name_zh'];
    address = json['address'];
    addressUg = json['address_ug'];
    addressZh = json['address_zh'];
    name = json['name'];
    description = json['description'];
    descriptionUg = json['description_ug'];
    descriptionZh = json['description_zh'];
    fullNameZh = json['full_name_zh'];
    fullNameUg = json['full_name_ug'];
    famous = json['famous'];
    logo = json['logo'];
    lat = json['lat'];
    lng = json['lng'];
    adminTel = json['admin_tel'];
    tel = json['tel'];
    tel2 = json['tel2'];
    tel3 = json['tel3'];
    tel4 = json['tel4'];
    tel5 = json['tel5'];
    openTime = json['open_time'];
    closeTime = json['close_time'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    hasStamp = json['has_stamp'];
    mark = json['mark'];
    shipperAvg = json['shipper_avg'];
    monthOrderCount = json['month_order_count'];
    printLang = json['print_lang'];
    weight = json['weight'];
    openId = json['open_id'];
    type = json['type'];
    starAvg = json['star_avg'];
    foodStarAvg = json['food_star_avg'];
    boxStarAvg = json['box_star_avg'];
    commentCount = json['comment_count'];
    state = json['state'];
    startBusiness = json['start_business'];
    lastCommentReaded = json['last_comment_readed'];
    isCashouting = json['is_cashouting'];
    selfOfferLunchBox = json['self_offer_lunch_box'];
    lastQueryTime = json['last_query_time'];
    deviceToken = json['device_token'];
    rankState = json['rank_state'];
    score = json['score'];
    resDealerYourselfTakePercent = json['res_dealer_yourself_take_percent'];
    resDealerPercent = json['res_dealer_percent'];
    resMpYourselfTakePercent = json['res_mp_yourself_take_percent'];
    resMpPercent = json['res_mp_percent'];
    canCashOut = json['can_cash_out'];
    displayShipment = json['display_shipment'];
    displayMerchantProfit = json['display_merchant_profit'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    restaurantType = json['restaurant_type'];
    canSelfTake = json['can_self_take'];
    canMulazimTake = json['can_mulazim_take'];
    cashPlatform = json['cash_platform'];
    payPlatform = json['pay_platform'];
    cashPerDay = json['cash_per_day'];
    lunchBoxRate = json['lunch_box_rate'];
    deliveryTime = json['delivery_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['tag'] = this.tag;
    data['dealer_id'] = this.dealerId;
    data['city_id'] = this.cityId;
    data['area_id'] = this.areaId;
    data['street_id'] = this.streetId;
    data['name_ug'] = this.nameUg;
    data['name_zh'] = this.nameZh;
    data['address'] = this.address;
    data['address_ug'] = this.addressUg;
    data['address_zh'] = this.addressZh;
    data['name'] = this.name;
    data['description'] = this.description;
    data['description_ug'] = this.descriptionUg;
    data['description_zh'] = this.descriptionZh;
    data['full_name_zh'] = this.fullNameZh;
    data['full_name_ug'] = this.fullNameUg;
    data['famous'] = this.famous;
    data['logo'] = this.logo;
    data['lat'] = this.lat;
    data['lng'] = this.lng;
    data['admin_tel'] = this.adminTel;
    data['tel'] = this.tel;
    data['tel2'] = this.tel2;
    data['tel3'] = this.tel3;
    data['tel4'] = this.tel4;
    data['tel5'] = this.tel5;
    data['open_time'] = this.openTime;
    data['close_time'] = this.closeTime;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['has_stamp'] = this.hasStamp;
    data['mark'] = this.mark;
    data['shipper_avg'] = this.shipperAvg;
    data['month_order_count'] = this.monthOrderCount;
    data['print_lang'] = this.printLang;
    data['weight'] = this.weight;
    data['open_id'] = this.openId;
    data['type'] = this.type;
    data['star_avg'] = this.starAvg;
    data['food_star_avg'] = this.foodStarAvg;
    data['box_star_avg'] = this.boxStarAvg;
    data['comment_count'] = this.commentCount;
    data['state'] = this.state;
    data['start_business'] = this.startBusiness;
    data['last_comment_readed'] = this.lastCommentReaded;
    data['is_cashouting'] = this.isCashouting;
    data['self_offer_lunch_box'] = this.selfOfferLunchBox;
    data['last_query_time'] = this.lastQueryTime;
    data['device_token'] = this.deviceToken;
    data['rank_state'] = this.rankState;
    data['score'] = this.score;
    data['res_dealer_yourself_take_percent'] =
        this.resDealerYourselfTakePercent;
    data['res_dealer_percent'] = this.resDealerPercent;
    data['res_mp_yourself_take_percent'] = this.resMpYourselfTakePercent;
    data['res_mp_percent'] = this.resMpPercent;
    data['can_cash_out'] = this.canCashOut;
    data['display_shipment'] = this.displayShipment;
    data['display_merchant_profit'] = this.displayMerchantProfit;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    data['restaurant_type'] = this.restaurantType;
    data['can_self_take'] = this.canSelfTake;
    data['can_mulazim_take'] = this.canMulazimTake;
    data['cash_platform'] = this.cashPlatform;
    data['pay_platform'] = this.payPlatform;
    data['cash_per_day'] = this.cashPerDay;
    data['lunch_box_rate'] = this.lunchBoxRate;
    data['delivery_time'] = this.deliveryTime;
    return data;
  }
}

class RestaurantBuilding {
  int? id;
  int? restaurantId;
  int? buildingId;
  int? distance;
  int? fixedShipment;
  int? shipment;
  int? time;
  int? state;
  String? createdAt;
  String? updatedAt;
  Null? deletedAt;

  RestaurantBuilding(
      {this.id,
        this.restaurantId,
        this.buildingId,
        this.distance,
        this.fixedShipment,
        this.shipment,
        this.time,
        this.state,
        this.createdAt,
        this.updatedAt,
        this.deletedAt});

  RestaurantBuilding.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    restaurantId = json['restaurant_id'];
    buildingId = json['building_id'];
    distance = json['distance'];
    fixedShipment = json['fixed_shipment'];
    shipment = json['shipment'];
    time = json['time'];
    state = json['state'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['restaurant_id'] = this.restaurantId;
    data['building_id'] = this.buildingId;
    data['distance'] = this.distance;
    data['fixed_shipment'] = this.fixedShipment;
    data['shipment'] = this.shipment;
    data['time'] = this.time;
    data['state'] = this.state;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    return data;
  }
}