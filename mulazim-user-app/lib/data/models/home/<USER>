class SelfTakeModel {
  SelfTakeData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  SelfTakeModel({this.data, this.lang, this.msg, this.status, this.time});

  SelfTakeModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new SelfTakeData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class SelfTakeData {
  List<DataList>? dataList;

  SelfTakeData({this.dataList});

  SelfTakeData.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      dataList = <DataList>[];
      json['data'].forEach((v) {
        dataList!.add(new DataList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.dataList != null) {
      data['data'] = this.dataList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataList {
  int? id;
  int? resId;
  String? restaurantName;
  String? logo;
  num? starAvg;
  int? canSelfTake;
  int? canMulazimTake;
  List<Foods>? foods;

  DataList(
      {this.id,
        this.resId,
        this.restaurantName,
        this.logo,
        this.starAvg,
        this.canSelfTake,
        this.canMulazimTake,
        this.foods});

  DataList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    resId = json['res_id'];
    restaurantName = json['restaurant_name'];
    logo = json['logo'];
    starAvg = json['star_avg'];
    canSelfTake = json['can_self_take'];
    canMulazimTake = json['can_mulazim_take'];
    if (json['foods'] != null) {
      foods = <Foods>[];
      json['foods'].forEach((v) {
        foods!.add(new Foods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['res_id'] = this.resId;
    data['restaurant_name'] = this.restaurantName;
    data['logo'] = this.logo;
    data['star_avg'] = this.starAvg;
    data['can_self_take'] = this.canSelfTake;
    data['can_mulazim_take'] = this.canMulazimTake;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Foods {
  int? id;
  int? restaurantId;
  String? foodsName;
  int? monthOrderCount;
  String? image;
  num? starAvg;
  num? price;
  int? activityType;

  Foods(
      {this.id,
        this.restaurantId,
        this.foodsName,
        this.monthOrderCount,
        this.image,
        this.starAvg,
        this.price,
        this.activityType
      });

  Foods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    restaurantId = json['restaurant_id'];
    foodsName = json['foods_name'];
    monthOrderCount = json['month_order_count'];
    image = json['image'];
    starAvg = json['star_avg'];
    price = json['price'];
    activityType = json['activity_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['restaurant_id'] = this.restaurantId;
    data['foods_name'] = this.foodsName;
    data['month_order_count'] = this.monthOrderCount;
    data['image'] = this.image;
    data['star_avg'] = this.starAvg;
    data['price'] = this.price;
    data['activity_type'] = this.activityType;
    return data;
  }
}