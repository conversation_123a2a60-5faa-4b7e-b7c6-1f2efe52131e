import 'package:user_app/data/models/home/<USER>';

import '../my_order/order_ranking_active.dart';

class HomeInfoModel {
  int? status;
  String? msg;
  HomeData? data;
  String? lang;

  HomeInfoModel({this.status, this.msg, this.data, this.lang});

  HomeInfoModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    data = json['data'] != null ? new HomeData.fromJson(json['data']) : null;
    lang = json['lang'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    return data;
  }
}

class HomeData {
  RestaurantList? restaurantList;
  Discount? discount;
  Seckill? seckill;
  List<PopupAdver>? popupAdver;
  // Null? noticeBox;
  List<Tags>? tags;
  bool? isCollegeCountShow;
  LocationInfo? location;
  List<Categories>? categories;
  List<PopupAdver>? adverList;
  List<Special>? special;
  SortSetting? sortSetting;
  ThemActive? themActive;
  List<ThemActivePreferential>? themActivePreferential;
  OrderRankingActive? orderRankingActivity;
  List<RankingItem>? ranking;
  // List<Null>? coupon;
  bool? yunshanfuCouponFlag;
  // List<Null>? lotteryLucky;
  // Null? snowActivity;
  int? yearSummaryActive;

  HomeData(
      {this.restaurantList,
      this.discount,
      this.seckill,
      this.popupAdver,
      // this.noticeBox,
      this.tags,
      this.isCollegeCountShow,
      this.location,
      this.categories,
      this.adverList,
      this.special,
      this.sortSetting,
      this.themActive,
      // this.preferential,
      this.themActivePreferential,
      this.orderRankingActivity,
      this.ranking,
      // this.coupon,
      this.yunshanfuCouponFlag,
      // this.lotteryLucky,
      // this.snowActivity,
      this.yearSummaryActive});

  HomeData.fromJson(Map<String, dynamic> json) {
    restaurantList = json['restaurant_list'] != null
        ? (json['restaurant_list'] is Map<String, dynamic>
            ? new RestaurantList.fromJson(json['restaurant_list'])
            : null)
        : null;
    discount = json['discount'] != null
        ? new Discount.fromJson(json['discount'])
        : null;
    seckill =
        json['seckill'] != null ? new Seckill.fromJson(json['seckill']) : null;
    if (json['popup_adver'] != null) {
      popupAdver = <PopupAdver>[];
      json['popup_adver'].forEach((v) {
        popupAdver!.add(new PopupAdver.fromJson(v));
      });
    }
    // noticeBox = json['notice_box'];
    if (json['tags'] != null) {
      tags = <Tags>[];
      json['tags'].forEach((v) {
        tags!.add(new Tags.fromJson(v));
      });
    }
    isCollegeCountShow = json['is_college_count_show'];
    location = json['location'] != null
        ? new LocationInfo.fromJson(json['location'])
        : null;
    if (json['categories'] != null) {
      categories = <Categories>[];
      json['categories'].forEach((v) {
        categories!.add(new Categories.fromJson(v));
      });
    }
    if (json['adver_list'] != null) {
      adverList = <PopupAdver>[];
      json['adver_list'].forEach((v) {
        adverList!.add(new PopupAdver.fromJson(v));
      });
    }
    if (json['special'] != null) {
      special = <Special>[];
      json['special'].forEach((v) {
        special!.add(new Special.fromJson(v));
      });
    }
    sortSetting = json['sortSetting'] != null
        ? new SortSetting.fromJson(json['sortSetting'])
        : null;
    // themActive = json['them_active'];
    // if (json['preferential'] != null) {
    //   preferential = <Null>[];
    //   json['preferential'].forEach((v) {
    //     preferential!.add(new Null.fromJson(v));
    //   });
    // }
    // if (json['them_active_preferential'] != null) {
    //   themActivePreferential = <Null>[];
    //   json['them_active_preferential'].forEach((v) {
    //     themActivePreferential!.add(new Null.fromJson(v));
    //   });
    // }
    if (json['ranking'] != null) {
      ranking = <RankingItem>[];
      json['ranking'].forEach((v) {
        ranking!.add(RankingItem.fromJson(v));
      });
    }
    // if (json['coupon'] != null) {
    //   coupon = <Null>[];
    //   json['coupon'].forEach((v) {
    //     coupon!.add(new Null.fromJson(v));
    //   });
    // }

    themActive = json['them_active'] != null
        ? new ThemActive.fromJson(json['them_active'])
        : null;

    if (json['them_active_preferential'] != null) {
      themActivePreferential = <ThemActivePreferential>[];
      json['them_active_preferential'].forEach((v) {
        themActivePreferential!.add(new ThemActivePreferential.fromJson(v));
      });
    }

    if (json['order_ranking_active'] != null) {
      orderRankingActivity = OrderRankingActive.fromJson(json['order_ranking_active']);
    }

    yunshanfuCouponFlag = json['yunshanfu_coupon_flag'];
    // if (json['lottery_lucky'] != null) {
    //   lotteryLucky = <Null>[];
    //   json['lottery_lucky'].forEach((v) {
    //     lotteryLucky!.add(new Null.fromJson(v));
    //   });
    // }
    // snowActivity = json['snow_activity'];
    yearSummaryActive = json['year_summary_active'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.restaurantList != null) {
      data['restaurant_list'] = this.restaurantList!.toJson();
    }

    if (this.discount != null) {
      data['discount'] = this.discount!.toJson();
    }

    if (this.seckill != null) {
      data['seckill'] = this.seckill!.toJson();
    }
    if (this.popupAdver != null) {
      data['popup_adver'] = this.popupAdver!.map((v) => v.toJson()).toList();
    }
    // data['notice_box'] = this.noticeBox;
    if (this.tags != null) {
      data['tags'] = this.tags!.map((v) => v.toJson()).toList();
    }
    data['is_college_count_show'] = this.isCollegeCountShow;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    if (this.categories != null) {
      data['categories'] = this.categories!.map((v) => v.toJson()).toList();
    }
    if (this.adverList != null) {
      data['adver_list'] = this.adverList!.map((v) => v.toJson()).toList();
    }
    if (this.special != null) {
      data['special'] = this.special!.map((v) => v.toJson()).toList();
    }
    if (this.sortSetting != null) {
      data['sortSetting'] = this.sortSetting!.toJson();
    }
    // data['them_active'] = this.themActive;
    // if (this.preferential != null) {
    //   data['preferential'] = this.preferential!.map((v) => v.toJson()).toList();
    // }
    // if (this.themActivePreferential != null) {
    //   data['them_active_preferential'] =
    //       this.themActivePreferential!.map((v) => v.toJson()).toList();
    // }
    // if (this.ranking != null) {
    //   data['ranking'] = this.ranking!.map((v) => v.toJson()).toList();
    // }
    // if (this.coupon != null) {
    //   data['coupon'] = this.coupon!.map((v) => v.toJson()).toList();
    // }

    if (this.themActive != null) {
      data['them_active'] = this.themActive!.toJson();
    }

    if (this.themActivePreferential != null) {
      data['them_active_preferential'] =
          this.themActivePreferential!.map((v) => v.toJson()).toList();
    }

    if (this.orderRankingActivity != null) {
      data['order_ranking_activity'] = this.orderRankingActivity!.toJson();
    }

    data['yunshanfu_coupon_flag'] = this.yunshanfuCouponFlag;
    // if (this.lotteryLucky != null) {
    //   data['lottery_lucky'] = this.lotteryLucky!.map((v) => v.toJson()).toList();
    // }
    // data['snow_activity'] = this.snowActivity;
    data['year_summary_active'] = this.yearSummaryActive;
    return data;
  }
}

/// 排行榜
class RankingItem {
  /// 区域id
  int? areaId;
  /// 排行榜id
  int? id;
  /// 图片
  String? image;
  /// 名称
  String? name;
  /// 排行榜类型
  int? rankingType;
  /// 餐厅id
  int? restaurantId;

  RankingItem({this.areaId, this.id, this.image, this.name, this.rankingType, this.restaurantId});

  RankingItem.fromJson(Map<String, dynamic> json) {
    areaId = json['area_id'];
    id = json['id'];
    image = json['image'];
    name = json['name'];
    rankingType = json['ranking_type'];
    restaurantId = json['restaurant_id'];
  }

}

class RestaurantList {
  int? perPage;
  int? currentPage;
  int? total;
  int? totalPage;
  List<RestaurantItems>? items;

  RestaurantList(
      {this.perPage, this.currentPage, this.total, this.totalPage, this.items});

  RestaurantList.fromJson(Map<String, dynamic> json) {
    perPage = json['per_page'];
    currentPage = json['current_page'];
    total = json['total'];
    totalPage = json['total_page'];
    if (json['items'] != null) {
      items = <RestaurantItems>[];
      json['items'].forEach((v) {
        items!.add(new RestaurantItems.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['per_page'] = this.perPage;
    data['current_page'] = this.currentPage;
    data['total'] = this.total;
    data['total_page'] = this.totalPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Discount {
  String? name;
  String? name2;
  String? logo;
  int? id;
  List<DiscountFoods>? foods;

  Discount({this.name, this.name2, this.logo, this.id, this.foods});

  Discount.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    name2 = json['name2'];
    logo = json['logo'];
    id = json['id'];
    if (json['foods'] != null) {
      foods = <DiscountFoods>[];
      if (json['foods'] is List) {
        (json['foods'] as List).forEach((v) {
          foods!.add(new DiscountFoods.fromJson(v));
        });
      } else if (json['foods'] is Map) {
        (json['foods'] as Map<String, dynamic>).forEach((key, value) {
          foods!.add(new DiscountFoods.fromJson(value));
        });
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['name2'] = this.name2;
    data['logo'] = this.logo;
    data['id'] = this.id;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DiscountFoods {
  int? id;
  String? name;
  String? name2;
  num? price;
  num? discountPrice;
  String? image;

  DiscountFoods(
      {this.id,
      this.name,
      this.name2,
      this.price,
      this.discountPrice,
      this.image});

  DiscountFoods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    name2 = json['name2'];
    price = json['price'];
    discountPrice = json['discount_price'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['name2'] = this.name2;
    data['price'] = this.price;
    data['discount_price'] = this.discountPrice;
    data['image'] = this.image;
    return data;
  }
}

class RestaurantItems {
  int? id;
  String? name;
  String? logo;
  num? starAvg;
  int? commentCount;
  int? monthOrderCount;
  int? orderFoodCount;
  int? hasFoodPre;
  num? distance;
  int? isNew;
  int? weight;
  int? canSelfTake;
  int? canMulazimTake;
  String? avgDeliveryTime;
  num? score;
  num? resting;
  List<MarketTag>? marketTag;
  List<TakeTag>? takeTag;

  RestaurantItems(
      {this.id,
      this.name,
      this.logo,
      this.starAvg,
      this.commentCount,
      this.monthOrderCount,
      this.orderFoodCount,
      this.hasFoodPre,
      this.distance,
      this.isNew,
      this.weight,
      this.canSelfTake,
      this.canMulazimTake,
      this.avgDeliveryTime,
      this.score,
      this.resting,
      this.marketTag,
      this.takeTag});

  RestaurantItems.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    logo = json['logo'];
    starAvg = json['star_avg'];
    commentCount = json['comment_count'];
    monthOrderCount = json['month_order_count'];
    orderFoodCount = json['order_food_count'];
    hasFoodPre = json['hasFoodPre'];
    distance = json['distance'];
    isNew = json['is_new'];
    weight = json['weight'];
    canSelfTake = json['can_self_take'];
    canMulazimTake = json['can_mulazim_take'];
    avgDeliveryTime = json['avg_delivery_time'].toString();
    score = json['score'];
    resting = json['resting'];
    if (json['market_tag'] != null) {
      marketTag = <MarketTag>[];
      json['market_tag'].forEach((v) {
        marketTag!.add(new MarketTag.fromJson(v));
      });
    }
    if (json['take_tag'] != null) {
      takeTag = <TakeTag>[];
      json['take_tag'].forEach((v) {
        takeTag!.add(new TakeTag.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['logo'] = this.logo;
    data['star_avg'] = this.starAvg;
    data['comment_count'] = this.commentCount;
    data['month_order_count'] = this.monthOrderCount;
    data['order_food_count'] = this.orderFoodCount;
    data['hasFoodPre'] = this.hasFoodPre;
    data['distance'] = this.distance;
    data['is_new'] = this.isNew;
    data['weight'] = this.weight;
    data['can_self_take'] = this.canSelfTake;
    data['can_mulazim_take'] = this.canMulazimTake;
    data['avg_delivery_time'] = this.avgDeliveryTime;
    data['score'] = this.score;
    data['resting'] = this.resting;
    if (this.marketTag != null) {
      data['market_tag'] = this.marketTag!.map((v) => v.toJson()).toList();
    }
    if (this.takeTag != null) {
      data['take_tag'] = this.takeTag!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MarketTag {
  int? type;
  String? color;
  String? titleUg;
  String? titleZh;
  String? background;
  String? borderColor;
  String? image;
  String? title;

  MarketTag(
      {this.type,
      this.color,
      this.titleUg,
      this.titleZh,
      this.background,
      this.borderColor,
      this.image,
      this.title});

  MarketTag.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    color = json['color'];
    titleUg = json['title_ug'];
    titleZh = json['title_zh'];
    background = json['background'];
    borderColor = json['border_color'];
    image = json['image'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['color'] = this.color;
    data['title_ug'] = this.titleUg;
    data['title_zh'] = this.titleZh;
    data['background'] = this.background;
    data['border_color'] = this.borderColor;
    data['image'] = this.image;
    data['title'] = this.title;
    return data;
  }
}

class TakeTag {
  String? title;
  String? color;
  String? background;
  String? borderColor;

  TakeTag({this.title, this.color, this.background, this.borderColor});

  TakeTag.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    color = json['color'];
    background = json['background'];
    borderColor = json['border_color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['color'] = this.color;
    data['background'] = this.background;
    data['border_color'] = this.borderColor;
    return data;
  }
}

class Seckill {
  int? seckillActive;
  String? seckillingTime;
  int? remainingSecond;
  List<SecKillFoods>? foods;

  Seckill(
      {this.seckillActive,
      this.seckillingTime,
      this.remainingSecond,
      this.foods});

  Seckill.fromJson(Map<String, dynamic> json) {
    seckillActive = json['seckill_active'];
    seckillingTime = json['seckilling_time'];
    remainingSecond = json['remaining_second'];
    if (json['foods'] != null) {
      foods = <SecKillFoods>[];
      json['foods'].forEach((v) {
        foods!.add(new SecKillFoods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['seckill_active'] = this.seckillActive;
    data['seckilling_time'] = this.seckillingTime;
    data['remaining_second'] = this.remainingSecond;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SecKillFoods {
  int? id;
  int? seckillId;
  num? price;
  int? restaurantId;
  String? orderTime;
  num? oldPrice;
  int? saledCount;
  int? totalCount;
  String? beginTime;
  String? endTime;
  int? maxOrderCount;
  String? image;
  String? name;
  String? restaurantName;

  SecKillFoods(
      {this.id,
      this.seckillId,
      this.price,
      this.restaurantId,
      this.orderTime,
      this.oldPrice,
      this.saledCount,
      this.totalCount,
      this.beginTime,
      this.endTime,
      this.maxOrderCount,
      this.image,
      this.name,
      this.restaurantName});

  SecKillFoods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    seckillId = json['seckill_id'];
    price = json['price'];
    restaurantId = json['restaurant_id'];
    orderTime = json['order_time'];
    oldPrice = json['old_price'];
    saledCount = json['saled_count'];
    totalCount = json['total_count'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    maxOrderCount = json['max_order_count'];
    image = json['image'];
    name = json['name'];
    restaurantName = json['restaurant_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['seckill_id'] = this.seckillId;
    data['price'] = this.price;
    data['restaurant_id'] = this.restaurantId;
    data['order_time'] = this.orderTime;
    data['old_price'] = this.oldPrice;
    data['saled_count'] = this.saledCount;
    data['total_count'] = this.totalCount;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['max_order_count'] = this.maxOrderCount;
    data['image'] = this.image;
    data['name'] = this.name;
    data['restaurant_name'] = this.restaurantName;
    return data;
  }
}

class PopupAdver {
  int? id;
  int? type;
  String? content;
  String? imageUrl;
  String? videoUrl;
  int? linkType;
  int? linkId;
  int? storeId;
  String? linkUrl;
  String? miniProgramId;
  String? miniProgramLinkPage;
  Restaurant? restaurant;
  RestaurantBuilding? restaurantBuilding;

  PopupAdver(
      {this.id,
      this.type,
      this.content,
      this.imageUrl,
      this.videoUrl,
      this.linkType,
      this.linkId,
      this.storeId,
      this.linkUrl,
      this.miniProgramId,
      this.miniProgramLinkPage,
      this.restaurant,
      this.restaurantBuilding});

  PopupAdver.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['type'];
    content = json['content'];
    imageUrl = json['image_url'];
    videoUrl = json['video_url'];
    linkType = json['link_type'];
    linkId = json['link_id'];
    storeId = json['store_id'];
    linkUrl = json['link_url'];
    miniProgramId = json['mini_program_id'];
    miniProgramLinkPage = json['mini_program_link_page'];
    restaurant = json['restaurant'] != null
        ? new Restaurant.fromJson(json['restaurant'])
        : null;
    restaurantBuilding = json['restaurant_building'] != null
        ? new RestaurantBuilding.fromJson(json['restaurant_building'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['type'] = this.type;
    data['content'] = this.content;
    data['image_url'] = this.imageUrl;
    data['video_url'] = this.videoUrl;
    data['link_type'] = this.linkType;
    data['link_id'] = this.linkId;
    data['store_id'] = this.storeId;
    data['link_url'] = this.linkUrl;
    data['mini_program_id'] = this.miniProgramId;
    data['mini_program_link_page'] = this.miniProgramLinkPage;
    if (this.restaurant != null) {
      data['restaurant'] = this.restaurant!.toJson();
    }
    if (this.restaurantBuilding != null) {
      data['restaurant_building'] = this.restaurantBuilding!.toJson();
    }
    return data;
  }
}

class Restaurant {
  int? id;
  String? tag;
  int? dealerId;
  int? cityId;
  int? areaId;
  int? streetId;
  String? nameUg;
  String? nameZh;
  String? address;
  String? addressUg;
  String? addressZh;
  String? name;
  String? description;
  String? descriptionUg;
  String? descriptionZh;
  String? fullNameZh;
  String? fullNameUg;
  int? famous;
  String? logo;
  num? lat;
  num? lng;
  String? adminTel;
  String? tel;
  String? tel2;
  String? tel3;
  String? tel4;
  String? tel5;
  String? openTime;
  String? closeTime;
  String? beginTime;
  String? endTime;
  int? hasStamp;
  String? mark;
  num? shipperAvg;
  int? monthOrderCount;
  int? printLang;
  int? weight;
  String? openId;
  int? type;
  num? starAvg;
  num? foodStarAvg;
  num? boxStarAvg;
  int? commentCount;
  int? state;
  String? startBusiness;
  String? lastCommentReaded;
  int? isCashouting;
  int? selfOfferLunchBox;
  String? lastQueryTime;
  String? deviceToken;
  int? rankState;
  num? score;
  int? resDealerYourselfTakePercent;
  int? resDealerPercent;
  int? resMpYourselfTakePercent;
  num? resMpPercent;
  int? canCashOut;
  int? displayShipment;
  int? displayMerchantProfit;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? restaurantType;
  int? canSelfTake;
  int? canMulazimTake;
  int? cashPlatform;
  int? payPlatform;
  int? cashPerDay;
  num? lunchBoxRate;
  num? deliveryTime;

  Restaurant(
      {this.id,
      this.tag,
      this.dealerId,
      this.cityId,
      this.areaId,
      this.streetId,
      this.nameUg,
      this.nameZh,
      this.address,
      this.addressUg,
      this.addressZh,
      this.name,
      this.description,
      this.descriptionUg,
      this.descriptionZh,
      this.fullNameZh,
      this.fullNameUg,
      this.famous,
      this.logo,
      this.lat,
      this.lng,
      this.adminTel,
      this.tel,
      this.tel2,
      this.tel3,
      this.tel4,
      this.tel5,
      this.openTime,
      this.closeTime,
      this.beginTime,
      this.endTime,
      this.hasStamp,
      this.mark,
      this.shipperAvg,
      this.monthOrderCount,
      this.printLang,
      this.weight,
      this.openId,
      this.type,
      this.starAvg,
      this.foodStarAvg,
      this.boxStarAvg,
      this.commentCount,
      this.state,
      this.startBusiness,
      this.lastCommentReaded,
      this.isCashouting,
      this.selfOfferLunchBox,
      this.lastQueryTime,
      this.deviceToken,
      this.rankState,
      this.score,
      this.resDealerYourselfTakePercent,
      this.resDealerPercent,
      this.resMpYourselfTakePercent,
      this.resMpPercent,
      this.canCashOut,
      this.displayShipment,
      this.displayMerchantProfit,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.restaurantType,
      this.canSelfTake,
      this.canMulazimTake,
      this.cashPlatform,
      this.payPlatform,
      this.cashPerDay,
      this.lunchBoxRate,
      this.deliveryTime});

  Restaurant.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tag = json['tag'];
    dealerId = json['dealer_id'];
    cityId = json['city_id'];
    areaId = json['area_id'];
    streetId = json['street_id'];
    nameUg = json['name_ug'];
    nameZh = json['name_zh'];
    address = json['address'];
    addressUg = json['address_ug'];
    addressZh = json['address_zh'];
    name = json['name'];
    description = json['description'];
    descriptionUg = json['description_ug'];
    descriptionZh = json['description_zh'];
    fullNameZh = json['full_name_zh'];
    fullNameUg = json['full_name_ug'];
    famous = json['famous'];
    logo = json['logo'];
    lat = json['lat'];
    lng = json['lng'];
    adminTel = json['admin_tel'];
    tel = json['tel'];
    tel2 = json['tel2'];
    tel3 = json['tel3'];
    tel4 = json['tel4'];
    tel5 = json['tel5'];
    openTime = json['open_time'];
    closeTime = json['close_time'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    hasStamp = json['has_stamp'];
    mark = json['mark'];
    shipperAvg = json['shipper_avg'];
    monthOrderCount = json['month_order_count'];
    printLang = json['print_lang'];
    weight = json['weight'];
    openId = json['open_id'];
    type = json['type'];
    starAvg = json['star_avg'];
    foodStarAvg = json['food_star_avg'];
    boxStarAvg = json['box_star_avg'];
    commentCount = json['comment_count'];
    state = json['state'];
    startBusiness = json['start_business'];
    lastCommentReaded = json['last_comment_readed'];
    isCashouting = json['is_cashouting'];
    selfOfferLunchBox = json['self_offer_lunch_box'];
    lastQueryTime = json['last_query_time'];
    deviceToken = json['device_token'];
    rankState = json['rank_state'];
    score = json['score'];
    resDealerYourselfTakePercent = json['res_dealer_yourself_take_percent'];
    resDealerPercent = json['res_dealer_percent'];
    resMpYourselfTakePercent = json['res_mp_yourself_take_percent'];
    resMpPercent = json['res_mp_percent'];
    canCashOut = json['can_cash_out'];
    displayShipment = json['display_shipment'];
    displayMerchantProfit = json['display_merchant_profit'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    restaurantType = json['restaurant_type'];
    canSelfTake = json['can_self_take'];
    canMulazimTake = json['can_mulazim_take'];
    cashPlatform = json['cash_platform'];
    payPlatform = json['pay_platform'];
    cashPerDay = json['cash_per_day'];
    lunchBoxRate = json['lunch_box_rate'];
    deliveryTime = json['delivery_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['tag'] = this.tag;
    data['dealer_id'] = this.dealerId;
    data['city_id'] = this.cityId;
    data['area_id'] = this.areaId;
    data['street_id'] = this.streetId;
    data['name_ug'] = this.nameUg;
    data['name_zh'] = this.nameZh;
    data['address'] = this.address;
    data['address_ug'] = this.addressUg;
    data['address_zh'] = this.addressZh;
    data['name'] = this.name;
    data['description'] = this.description;
    data['description_ug'] = this.descriptionUg;
    data['description_zh'] = this.descriptionZh;
    data['full_name_zh'] = this.fullNameZh;
    data['full_name_ug'] = this.fullNameUg;
    data['famous'] = this.famous;
    data['logo'] = this.logo;
    data['lat'] = this.lat;
    data['lng'] = this.lng;
    data['admin_tel'] = this.adminTel;
    data['tel'] = this.tel;
    data['tel2'] = this.tel2;
    data['tel3'] = this.tel3;
    data['tel4'] = this.tel4;
    data['tel5'] = this.tel5;
    data['open_time'] = this.openTime;
    data['close_time'] = this.closeTime;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['has_stamp'] = this.hasStamp;
    data['mark'] = this.mark;
    data['shipper_avg'] = this.shipperAvg;
    data['month_order_count'] = this.monthOrderCount;
    data['print_lang'] = this.printLang;
    data['weight'] = this.weight;
    data['open_id'] = this.openId;
    data['type'] = this.type;
    data['star_avg'] = this.starAvg;
    data['food_star_avg'] = this.foodStarAvg;
    data['box_star_avg'] = this.boxStarAvg;
    data['comment_count'] = this.commentCount;
    data['state'] = this.state;
    data['start_business'] = this.startBusiness;
    data['last_comment_readed'] = this.lastCommentReaded;
    data['is_cashouting'] = this.isCashouting;
    data['self_offer_lunch_box'] = this.selfOfferLunchBox;
    data['last_query_time'] = this.lastQueryTime;
    data['device_token'] = this.deviceToken;
    data['rank_state'] = this.rankState;
    data['score'] = this.score;
    data['res_dealer_yourself_take_percent'] =
        this.resDealerYourselfTakePercent;
    data['res_dealer_percent'] = this.resDealerPercent;
    data['res_mp_yourself_take_percent'] = this.resMpYourselfTakePercent;
    data['res_mp_percent'] = this.resMpPercent;
    data['can_cash_out'] = this.canCashOut;
    data['display_shipment'] = this.displayShipment;
    data['display_merchant_profit'] = this.displayMerchantProfit;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    data['restaurant_type'] = this.restaurantType;
    data['can_self_take'] = this.canSelfTake;
    data['can_mulazim_take'] = this.canMulazimTake;
    data['cash_platform'] = this.cashPlatform;
    data['pay_platform'] = this.payPlatform;
    data['cash_per_day'] = this.cashPerDay;
    data['lunch_box_rate'] = this.lunchBoxRate;
    data['delivery_time'] = this.deliveryTime;
    return data;
  }
}

class RestaurantBuilding {
  int? id;
  int? restaurantId;
  int? buildingId;
  int? distance;
  int? fixedShipment;
  int? shipment;
  int? time;
  int? state;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;

  RestaurantBuilding(
      {this.id,
      this.restaurantId,
      this.buildingId,
      this.distance,
      this.fixedShipment,
      this.shipment,
      this.time,
      this.state,
      this.createdAt,
      this.updatedAt,
      this.deletedAt});

  RestaurantBuilding.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    restaurantId = json['restaurant_id'];
    buildingId = json['building_id'];
    distance = json['distance'];
    fixedShipment = json['fixed_shipment'];
    shipment = json['shipment'];
    time = json['time'];
    state = json['state'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['restaurant_id'] = this.restaurantId;
    data['building_id'] = this.buildingId;
    data['distance'] = this.distance;
    data['fixed_shipment'] = this.fixedShipment;
    data['shipment'] = this.shipment;
    data['time'] = this.time;
    data['state'] = this.state;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    return data;
  }
}

class Tags {
  int? id;
  String? name;
  int? sort;

  Tags({this.id, this.name, this.sort});

  Tags.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    sort = json['sort'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['sort'] = this.sort;
    return data;
  }
}

class Categories {
  int? id;
  String? name;
  String? nameUg;
  String? nameZh;
  int? pageType;
  String? linkUrl;
  String? icon;
  String? rgb;
  int? active;
  String? textColor;
  int? cartoonType;
  String? cartoonIcon;

  Categories(
      {this.id,
      this.name,
      this.nameUg,
      this.nameZh,
      this.pageType,
      this.linkUrl,
      this.icon,
      this.rgb,
      this.active,
      this.textColor,
      this.cartoonType,
      this.cartoonIcon});

  Categories.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameUg = json['name_ug'];
    nameZh = json['name_zh'];
    pageType = json['page_type'];
    linkUrl = json['link_url'];
    icon = json['icon'];
    rgb = json['rgb'];
    active = json['active'];
    textColor = json['text_color'];
    cartoonType = json['cartoon_type'];
    cartoonIcon = json['cartoon_icon'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['name_ug'] = this.nameUg;
    data['name_zh'] = this.nameZh;
    data['page_type'] = this.pageType;
    data['link_url'] = this.linkUrl;
    data['icon'] = this.icon;
    data['rgb'] = this.rgb;
    data['active'] = this.active;
    data['text_color'] = this.textColor;
    data['cartoon_type'] = this.cartoonType;
    data['cartoon_icon'] = this.cartoonIcon;
    return data;
  }
}

class Special {
  int? shipmentType;
  num? shipmentFee;
  num? price;
  int? takeTime;
  int? totalCount;
  int? saledCount;
  num? oldPrice;
  String? image;
  String? name;

  Special(
      {this.shipmentType,
      this.shipmentFee,
      this.price,
      this.takeTime,
      this.totalCount,
      this.saledCount,
      this.oldPrice,
      this.image,
      this.name});

  Special.fromJson(Map<String, dynamic> json) {
    shipmentType = json['shipment_type'];
    shipmentFee = json['shipment_fee'];
    price = json['price'];
    takeTime = json['take_time'];
    totalCount = json['total_count'];
    saledCount = json['saled_count'];
    oldPrice = json['old_price'];
    image = json['image'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['shipment_type'] = this.shipmentType;
    data['shipment_fee'] = this.shipmentFee;
    data['price'] = this.price;
    data['take_time'] = this.takeTime;
    data['total_count'] = this.totalCount;
    data['saled_count'] = this.saledCount;
    data['old_price'] = this.oldPrice;
    data['image'] = this.image;
    data['name'] = this.name;
    return data;
  }
}

class SortSetting {
  int? userLikeRes;
  int? newRes;
  int? discountRes;
  int? searchHotRes;
  int? searchResSwipeTime;
  String? howOrderVideo;

  SortSetting(
      {this.userLikeRes,
      this.newRes,
      this.discountRes,
      this.searchHotRes,
      this.searchResSwipeTime,
      this.howOrderVideo});

  SortSetting.fromJson(Map<String, dynamic> json) {
    userLikeRes = json['user_like_res'];
    newRes = json['new_res'];
    discountRes = json['discount_res'];
    searchHotRes = json['search_hot_res'];
    searchResSwipeTime = json['search_res_swipe_time'];
    howOrderVideo = json['how_order_video'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_like_res'] = this.userLikeRes;
    data['new_res'] = this.newRes;
    data['discount_res'] = this.discountRes;
    data['search_hot_res'] = this.searchHotRes;
    data['search_res_swipe_time'] = this.searchResSwipeTime;
    data['how_order_video'] = this.howOrderVideo;
    return data;
  }
}

class ThemActive {
  int? id;
  String? name;
  String? desc;
  String? beginTime;
  String? endTime;
  int? discountPercent;
  String? cover;
  String? color;
  int? state;
  int? hasKeyword;
  String? keywordUg;
  String? shareContent;
  String? shareCover;
  String? shareCoverToFriend;
  int? showFoodType;
  int? showPreferential;
  int? showSeckill;

  ThemActive(
      {this.id,
      this.name,
      this.desc,
      this.beginTime,
      this.endTime,
      this.discountPercent,
      this.cover,
      this.color,
      this.state,
      this.hasKeyword,
      this.keywordUg,
      this.shareContent,
      this.shareCover,
      this.shareCoverToFriend,
      this.showFoodType,
      this.showPreferential,
      this.showSeckill});

  ThemActive.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    desc = json['desc'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    discountPercent = json['discount_percent'];
    cover = json['cover'];
    color = json['color'];
    state = json['state'];
    hasKeyword = json['has_keyword'];
    keywordUg = json['keyword_ug'];
    shareContent = json['share_content'];
    shareCover = json['share_cover'];
    shareCoverToFriend = json['share_cover_to_friend'];
    showFoodType = json['show_food_type'];
    showPreferential = json['show_preferential'];
    showSeckill = json['show_seckill'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['desc'] = this.desc;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['discount_percent'] = this.discountPercent;
    data['cover'] = this.cover;
    data['color'] = this.color;
    data['state'] = this.state;
    data['has_keyword'] = this.hasKeyword;
    data['keyword_ug'] = this.keywordUg;
    data['share_content'] = this.shareContent;
    data['share_cover'] = this.shareCover;
    data['share_cover_to_friend'] = this.shareCoverToFriend;
    data['show_food_type'] = this.showFoodType;
    data['show_preferential'] = this.showPreferential;
    data['show_seckill'] = this.showSeckill;
    return data;
  }
}

class ThemActivePreferential {
  int? categoryId;
  int? restaurantId;
  String? restaurantName;
  double? distance;
  int? discountType;
  int? foodId;
  String? foodName;
  num? originPrice;
  num? price;
  int? maxOrderCount;
  String? image;
  int? starAvg;
  int? monthOrderCount;
  String? date;
  String? time;
  int? isStart;
  int? festiveState;

  ThemActivePreferential(
      {this.categoryId,
      this.restaurantId,
      this.restaurantName,
      this.distance,
      this.discountType,
      this.foodId,
      this.foodName,
      this.originPrice,
      this.price,
      this.maxOrderCount,
      this.image,
      this.starAvg,
      this.monthOrderCount,
      this.date,
      this.time,
      this.isStart,
      this.festiveState});

  ThemActivePreferential.fromJson(Map<String, dynamic> json) {
    categoryId = json['category_id'];
    restaurantId = json['restaurant_id'];
    restaurantName = json['restaurant_name'];
    distance = json['distance'];
    discountType = json['discount_type'];
    foodId = json['food_id'];
    foodName = json['food_name'];
    originPrice = json['origin_price'];
    price = json['price'];
    maxOrderCount = json['max_order_count'];
    image = json['image'];
    starAvg = json['star_avg'];
    monthOrderCount = json['month_order_count'];
    date = json['date'];
    time = json['time'];
    isStart = json['is_start'];
    festiveState = json['festive_state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['category_id'] = this.categoryId;
    data['restaurant_id'] = this.restaurantId;
    data['restaurant_name'] = this.restaurantName;
    data['distance'] = this.distance;
    data['discount_type'] = this.discountType;
    data['food_id'] = this.foodId;
    data['food_name'] = this.foodName;
    data['origin_price'] = this.originPrice;
    data['price'] = this.price;
    data['max_order_count'] = this.maxOrderCount;
    data['image'] = this.image;
    data['star_avg'] = this.starAvg;
    data['month_order_count'] = this.monthOrderCount;
    data['date'] = this.date;
    data['time'] = this.time;
    data['is_start'] = this.isStart;
    data['festive_state'] = this.festiveState;
    return data;
  }
}
