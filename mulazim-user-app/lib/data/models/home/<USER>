class QuickInfoModel {
  QuickInfoData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  QuickInfoModel({this.data, this.lang, this.msg, this.status, this.time});

  QuickInfoModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new QuickInfoData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class QuickInfoData {
  List<ChildCategories>? childCategories;
  RestaurantList? restaurantList;
  SortSettingQuick? sortSetting;
  int? currChildCategory;

  QuickInfoData(
      {this.childCategories,
        this.restaurantList,
        this.sortSetting,
        this.currChildCategory});

  QuickInfoData.fromJson(Map<String, dynamic> json) {
    if (json['child_categories'] != null) {
      childCategories = <ChildCategories>[];
      json['child_categories'].forEach((v) {
        childCategories!.add(new ChildCategories.fromJson(v));
      });
    }
    restaurantList = json['restaurant_list'] != null
        ? new RestaurantList.fromJson(json['restaurant_list'])
        : null;
    sortSetting = json['sortSetting'] != null
        ? new SortSettingQuick.fromJson(json['sortSetting'])
        : null;
    currChildCategory = json['curr_child_category'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.childCategories != null) {
      data['child_categories'] =
          this.childCategories!.map((v) => v.toJson()).toList();
    }
    if (this.restaurantList != null) {
      data['restaurant_list'] = this.restaurantList!.toJson();
    }
    if (this.sortSetting != null) {
      data['sortSetting'] = this.sortSetting!.toJson();
    }
    data['curr_child_category'] = this.currChildCategory;
    return data;
  }
}

class ChildCategories {
  int? id;
  int? parentId;
  String? name;
  int? pageType;
  String? icon;
  String? rgb;

  ChildCategories(
      {this.id, this.parentId, this.name, this.pageType, this.icon, this.rgb});

  ChildCategories.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parent_id'];
    name = json['name'];
    pageType = json['page_type'];
    icon = json['icon'];
    rgb = json['rgb'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['parent_id'] = this.parentId;
    data['name'] = this.name;
    data['page_type'] = this.pageType;
    data['icon'] = this.icon;
    data['rgb'] = this.rgb;
    return data;
  }
}

class RestaurantList {
  int? perPage;
  int? currentPage;
  List<Items>? items;

  RestaurantList({this.perPage, this.currentPage, this.items});

  RestaurantList.fromJson(Map<String, dynamic> json) {
    perPage = json['per_page'];
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['per_page'] = this.perPage;
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Items {
  int? id;
  String? name;
  String? logo;
  num? starAvg;
  int? state;
  int? monthOrderCount;
  int? orderFoodCount;
  int? hasFoodPre;
  double? distance;
  int? isNew;
  int? resting;
  int? weight;
  List<TakeTag>? takeTag;

  Items(
      {this.id,
        this.name,
        this.logo,
        this.starAvg,
        this.state,
        this.monthOrderCount,
        this.orderFoodCount,
        this.hasFoodPre,
        this.distance,
        this.isNew,
        this.resting,
        this.weight,
        this.takeTag});

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    logo = json['logo'];
    starAvg = json['star_avg'];
    state = json['state'];
    monthOrderCount = json['month_order_count'];
    orderFoodCount = json['order_food_count'];
    hasFoodPre = json['hasFoodPre'];
    distance = json['distance'];
    isNew = json['is_new'];
    resting = json['resting'];
    weight = json['weight'];
    if (json['take_tag'] != null) {
      takeTag = <TakeTag>[];
      json['take_tag'].forEach((v) {
        takeTag!.add(new TakeTag.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['logo'] = this.logo;
    data['star_avg'] = this.starAvg;
    data['state'] = this.state;
    data['month_order_count'] = this.monthOrderCount;
    data['order_food_count'] = this.orderFoodCount;
    data['hasFoodPre'] = this.hasFoodPre;
    data['distance'] = this.distance;
    data['is_new'] = this.isNew;
    data['resting'] = this.resting;
    data['weight'] = this.weight;
    if (this.takeTag != null) {
      data['take_tag'] = this.takeTag!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TakeTag {
  String? title;
  String? color;
  String? background;
  String? borderColor;

  TakeTag({this.title, this.color, this.background, this.borderColor});

  TakeTag.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    color = json['color'];
    background = json['background'];
    borderColor = json['border_color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['color'] = this.color;
    data['background'] = this.background;
    data['border_color'] = this.borderColor;
    return data;
  }
}

class SortSettingQuick {
  int? userLikeRes;
  int? newRes;
  int? discountRes;
  int? searchHotRes;
  int? searchResSwipeTime;
  String? howOrderVideo;

  SortSettingQuick(
      {this.userLikeRes,
        this.newRes,
        this.discountRes,
        this.searchHotRes,
        this.searchResSwipeTime,
        this.howOrderVideo});

  SortSettingQuick.fromJson(Map<String, dynamic> json) {
    userLikeRes = json['user_like_res'];
    newRes = json['new_res'];
    discountRes = json['discount_res'];
    searchHotRes = json['search_hot_res'];
    searchResSwipeTime = json['search_res_swipe_time'];
    howOrderVideo = json['how_order_video'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_like_res'] = this.userLikeRes;
    data['new_res'] = this.newRes;
    data['discount_res'] = this.discountRes;
    data['search_hot_res'] = this.searchHotRes;
    data['search_res_swipe_time'] = this.searchResSwipeTime;
    data['how_order_video'] = this.howOrderVideo;
    return data;
  }
}