class DiscountFoodsModel {
  DiscountFoodsData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  DiscountFoodsModel({this.data, this.lang, this.msg, this.status, this.time});

  DiscountFoodsModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new DiscountFoodsData.fromJson(json['data'])
        : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class DiscountFoodsData {
  int? lastPage;
  int? perPage;
  int? currentPage;
  List<Items>? items;

  DiscountFoodsData(
      {this.lastPage, this.perPage, this.currentPage, this.items});

  DiscountFoodsData.fromJson(Map<String, dynamic> json) {
    lastPage = json['last_page'];
    perPage = json['per_page'];
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['last_page'] = this.lastPage;
    data['per_page'] = this.perPage;
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Items {
  int? categoryId;
  String? date;
  int? discountType;
  int? festiveState;
  String? foodName;
  int? isStart;
  num? originPrice;
  String? restaurantName;
  String? time;
  num? distance;
  int? foodId;
  String? image;
  int? maxOrderCount;
  int? monthOrderCount;
  num? price;
  int? restaurantId;
  int? starAvg;
  int? foodType; // 美食类型 1-规格商品 2-套餐商品
  SelectedSpec? selectedSpec; // 选中的规格信息

  Items(
      {this.categoryId,
      this.date,
      this.discountType,
      this.festiveState,
      this.foodName,
      this.isStart,
      this.originPrice,
      this.restaurantName,
      this.time,
      this.distance,
      this.foodId,
      this.image,
      this.maxOrderCount,
      this.monthOrderCount,
      this.price,
      this.restaurantId,
      this.starAvg,
      this.foodType,
      this.selectedSpec});

  Items.fromJson(Map<String, dynamic> json) {
    categoryId = json['category_id'];
    date = json['date'];
    discountType = json['discount_type'];
    festiveState = json['festive_state'];
    foodName = json['food_name'];
    isStart = json['is_start'];
    originPrice = json['origin_price'];
    restaurantName = json['restaurant_name'];
    time = json['time'];
    distance = json['distance'];
    foodId = json['food_id'];
    image = json['image'];
    maxOrderCount = json['max_order_count'];
    monthOrderCount = json['month_order_count'];
    price = json['price'];
    restaurantId = json['restaurant_id'];
    starAvg = json['star_avg'];
    foodType = json['food_type'];
    selectedSpec = json['selected_spec'] != null
        ? SelectedSpec.fromJson(json['selected_spec'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['category_id'] = this.categoryId;
    data['date'] = this.date;
    data['discount_type'] = this.discountType;
    data['festive_state'] = this.festiveState;
    data['food_name'] = this.foodName;
    data['is_start'] = this.isStart;
    data['origin_price'] = this.originPrice;
    data['restaurant_name'] = this.restaurantName;
    data['time'] = this.time;
    data['distance'] = this.distance;
    data['food_id'] = this.foodId;
    data['image'] = this.image;
    data['max_order_count'] = this.maxOrderCount;
    data['month_order_count'] = this.monthOrderCount;
    data['price'] = this.price;
    data['restaurant_id'] = this.restaurantId;
    data['star_avg'] = this.starAvg;
    data['food_type'] = this.foodType;
    if (this.selectedSpec != null) {
      data['selected_spec'] = this.selectedSpec!.toJson();
    }
    return data;
  }
}

/// 折扣页面选中的规格信息
class SelectedSpec {
  int? id;
  num? price;
  List<SpecOption>? specOptions;

  SelectedSpec({this.id, this.price, this.specOptions});

  SelectedSpec.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    price = json['price'];
    if (json['spec_options'] != null) {
      specOptions = <SpecOption>[];
      json['spec_options'].forEach((v) {
        specOptions!.add(SpecOption.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = this.id;
    data['price'] = this.price;
    if (this.specOptions != null) {
      data['spec_options'] = this.specOptions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

/// 折扣页面规格选项信息
class SpecOption {
  int? id;
  String? name;
  num? price;
  int? isSelected;
  int? state;

  SpecOption({this.id, this.name, this.price, this.isSelected, this.state});

  SpecOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    price = json['price'];
    isSelected = json['is_selected'];
    state = json['state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    data['is_selected'] = this.isSelected;
    data['state'] = this.state;
    return data;
  }
}
