class UpgradeModel {
  UpgradeData? data;
  String? msg;
  int? status;
  String? time;

  UpgradeModel({this.data, this.msg, this.status, this.time});

  UpgradeModel.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null ? null : UpgradeData.fromJson(json["data"]);
    msg = json["msg"] ?? '';
    status = json["status"] ?? 0;
    time = json["time"] ?? '';
  }
}

class UpgradeData {
  int? id;
  String? name;
  int? osType;
  String? icon;
  String? version;
  int? versionCode;
  String? packageName;
  int? forceUpdate;
  String? url;
  String? des;

  UpgradeData(
      {this.id,
      this.name,
      this.osType,
      this.icon,
      this.version,
      this.versionCode,
      this.packageName,
      this.forceUpdate,
      this.url,
      this.des});

  UpgradeData.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    osType = json["os_type"];
    icon = json["icon"];
    version = json["version"];
    versionCode = json["version_code"];
    packageName = json["package_name"];
    forceUpdate = json["force_update"];
    url = json["url"];
    des = json["des"];
  }
}
