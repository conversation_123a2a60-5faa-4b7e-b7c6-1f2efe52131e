/// 积分记录模型
class PointsLog {
  /// 积分变动数量
  final int count;

  /// 创建时间
  final String createdAt;

  /// 变动原因
  final String reason;

  /// 构造函数
  const PointsLog({
    required this.count,
    required this.createdAt,
    required this.reason,
  });

  /// 从JSON创建
  factory PointsLog.fromJson(final Map<String, dynamic> json) {
    return PointsLog(
      count: json['count'] as int,
      createdAt: json['created_at'] as String,
      reason: json['reason'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'created_at': createdAt,
      'reason': reason,
    };
  }
}

/// 积分数据模型
class PointsData {
  /// 当前积分
  final int points;

  /// 积分记录列表
  final List<PointsLog> pointsLog;

  /// 当前页码
  final int currentPage;

  /// 每页数量
  final int perPage;

  /// 积分兑换率
  final bool pointExchangeRate;

  /// 构造函数
  const PointsData({
    required this.points,
    required this.pointsLog,
    required this.currentPage,
    required this.perPage,
    required this.pointExchangeRate,
  });

  /// 从JSON创建
  factory PointsData.fromJson(final Map<String, dynamic> json) {
    return PointsData(
      points: json['points'] as int,
      pointsLog: (json['points_log'] as List<dynamic>)
          .map((e) => PointsLog.fromJson(e as Map<String, dynamic>))
          .toList(),
      currentPage: json['current_page'] as int,
      perPage: json['per_page'] as int,
      pointExchangeRate: json['point_exchange_rate'] as bool,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'points': points,
      'points_log': pointsLog.map((e) => e.toJson()).toList(),
      'current_page': currentPage,
      'per_page': perPage,
      'point_exchange_rate': pointExchangeRate,
    };
  }
}
