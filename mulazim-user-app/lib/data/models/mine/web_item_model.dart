/// 网页项数据模型
class WebItem {
  /// 名称
  final String name;

  /// URL
  final String url;

  /// 构造函数
  const WebItem({
    required this.name,
    required this.url,
  });

  /// 从JSON构造
  factory WebItem.fromJson(final Map<String, dynamic> json) {
    return WebItem(
      name: json['name'] as String,
      url: json['url'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
    };
  }

  /// 复制方法
  WebItem copyWith({
    final String? name,
    final String? url,
  }) {
    return WebItem(
      name: name ?? this.name,
      url: url ?? this.url,
    );
  }
}
