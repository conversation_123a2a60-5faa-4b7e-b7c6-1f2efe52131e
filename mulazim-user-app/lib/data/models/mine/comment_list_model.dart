/// 评论列表数据模型
class CommentListModel {
  CommentListData? data;
  String? lang;
  String? msg;
  int? status;

  CommentListModel({this.data, this.lang, this.msg, this.status});

  CommentListModel.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null ? null : CommentListData.fromJson(json["data"]);
    lang = json["lang"];
    msg = json["msg"];
    status = json["status"];
  }
}

/// 评论列表数据
class CommentListData {
  int? perPage;
  int? currentPage;
  int? lastPage;
  List<CommentItem>? items;

  CommentListData({this.perPage, this.currentPage, this.lastPage, this.items});

  CommentListData.fromJson(Map<String, dynamic> json) {
    perPage = json["per_page"];
    currentPage = json["current_page"];
    lastPage = json["last_page"];
    items = json["items"] == null
        ? null
        : (json["items"] as List).map((e) => CommentItem.fromJson(e)).toList();
  }
}

/// 评论项
class CommentItem {
  int? id;
  int? type; // 1:配送员评价 2:美食评价
  int? state; // 评论状态 0:审核中 1:已审核 2:未审核
  String? createdAt;
  num? star;
  String? text;
  num? foodStar;
  num? boxStar;
  String? foodName;
  String? userAvatar;
  String? userName;
  String? shipperAvatar;
  String? shipperName;
  String? areaName;
  String? shipperMobile;
  int? shipperId;
  int? orderId;
  String? foodImage;
  String? foodPrice;
  List<String>? images;
  List<CommentReply>? replies;
  bool? deleted;

  CommentItem({
    this.id,
    this.type,
    this.state,
    this.createdAt,
    this.star,
    this.text,
    this.foodStar,
    this.boxStar,
    this.foodName,
    this.userAvatar,
    this.userName,
    this.shipperAvatar,
    this.shipperName,
    this.areaName,
    this.shipperMobile,
    this.shipperId,
    this.orderId,
    this.foodImage,
    this.foodPrice,
    this.images,
    this.replies,
    this.deleted = false,
  });

  CommentItem.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    type = json["type"];
    state = json["state"];
    createdAt = json["created_at"];
    star = json["star"];
    text = json["text"];
    foodStar = json["food_star"];
    boxStar = json["box_star"];
    foodName = json["food_name"];
    userAvatar = json["user_avatar"];
    userName = json["user_name"];
    shipperAvatar = json["shipper_avatar"];
    shipperName = json["shipper_name"];
    areaName = json["area_name"];
    shipperMobile = json["shipper_mobile"];
    shipperId = json["shipper_id"];
    orderId = json["order_id"];
    foodImage = json["food_image"];
    foodPrice = json["food_price"];
    images = json["images"] == null ? [] : List<String>.from(json["images"]);
    replies = json["replies"] == null
        ? []
        : (json["replies"] as List)
            .map((e) => CommentReply.fromJson(e))
            .toList();
    deleted = false;
  }
}

/// 评论回复
class CommentReply {
  int? id;
  int? type; // 1:商家回复 2:平台回复
  String? text;

  CommentReply({this.id, this.type, this.text});

  CommentReply.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    type = json["type"];
    text = json["text"];
  }
}
