import 'package:user_app/data/models/my_order/order_detail_model.dart';

class ShipperPositionModel {
  ShipperPositionData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  ShipperPositionModel(
      {this.data, this.lang, this.msg, this.status, this.time});

  ShipperPositionModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new ShipperPositionData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class ShipperPositionData {
  Shipper? shipper;

  ShipperPositionData({this.shipper});

  ShipperPositionData.fromJson(Map<String, dynamic> json) {
    shipper =
    json['shipper'] != null ? new Shipper.fromJson(json['shipper']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.shipper != null) {
      data['shipper'] = this.shipper!.toJson();
    }
    return data;
  }
}
