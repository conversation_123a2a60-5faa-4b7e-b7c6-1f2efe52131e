class OrderRankingActive {
  /// 活动运行状态：1-进行中
  final int? runningState;

  /// 分享封面图片列表
  final List<String>? shareCoverImages;

  /// 公告入口图片
  final String? announceEntranceImage;

  /// 活动标题
  final String? title;

  /// 活动规则
  final String? rule;

  /// 奖品列表
  final List<OrderRankingPrize>? prize;

  /// 中奖用户列表
  final List<OrderRankingLuckyUser>? luckUserList;

  /// 活动ID
  final int? activityId;

  OrderRankingActive({
    this.runningState,
    this.shareCoverImages,
    this.announceEntranceImage,
    this.title,
    this.rule,
    this.prize,
    this.luckUserList,
    this.activityId,
  });

  factory OrderRankingActive.fromJson(Map<String, dynamic> json) {
    return OrderRankingActive(
      runningState: json['runningState'],
      shareCoverImages: json['share_cover_images'] != null
          ? List<String>.from(json['share_cover_images'])
          : null,
      announceEntranceImage: json['announce_entrance_image'],
      title: json['title'],
      rule: json['rule'],
      prize: json['prize'] != null
          ? List<OrderRankingPrize>.from(
              json['prize'].map((x) => OrderRankingPrize.fromJson(x)))
          : null,
      luckUserList: json['luck_user_list'] != null
          ? List<OrderRankingLuckyUser>.from(json['luck_user_list']
              .map((x) => OrderRankingLuckyUser.fromJson(x)))
          : null,
      activityId: json['activity_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['runningState'] = this.runningState;
    if (this.shareCoverImages != null) {
      data['share_cover_images'] = this.shareCoverImages;
    }
    data['announce_entrance_image'] = this.announceEntranceImage;
    data['title'] = this.title;
    data['rule'] = this.rule;
    if (this.prize != null) {
      data['prize'] = this.prize!.map((v) => v.toJson()).toList();
    }
    if (this.luckUserList != null) {
      data['luck_user_list'] =
          this.luckUserList!.map((v) => v.toJson()).toList();
    }
    data['activity_id'] = this.activityId;
    return data;
  }
}

class OrderRankingPrize {
  /// 奖品ID
  final int? prizeId;

  /// 奖品名称
  final String? prizeName;

  /// 奖品图片
  final String? prizeImage;

  /// 中奖编号
  final int? luckyUserIndex;

  OrderRankingPrize({
    this.prizeId,
    this.prizeName,
    this.prizeImage,
    this.luckyUserIndex,
  });

  factory OrderRankingPrize.fromJson(Map<String, dynamic> json) {
    return OrderRankingPrize(
      prizeId: json['prize_id'],
      prizeName: json['prize_name'],
      prizeImage: json['prize_image'],
      luckyUserIndex: json['lucky_user_index'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['prize_id'] = this.prizeId;
    data['prize_name'] = this.prizeName;
    data['prize_image'] = this.prizeImage;
    data['lucky_user_index'] = this.luckyUserIndex;
    return data;
  }
}

class OrderRankingLuckyUser {
  /// 用户ID
  final int? userId;

  /// 用户名称
  final String? userName;

  /// 用户头像
  final String? userImage;

  /// 奖品ID
  final int? prizeId;

  /// 奖品名称
  final String? prizeName;

  /// 奖品图片
  final String? prizeImage;

  OrderRankingLuckyUser({
    this.userId,
    this.userName,
    this.userImage,
    this.prizeId,
    this.prizeName,
    this.prizeImage,
  });

  factory OrderRankingLuckyUser.fromJson(Map<String, dynamic> json) {
    return OrderRankingLuckyUser(
      userId: json['user_id'],
      userName: json['user_name'],
      userImage: json['user_image'],
      prizeId: json['prize_id'],
      prizeName: json['prize_name'],
      prizeImage: json['prize_image'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['user_name'] = this.userName;
    data['user_image'] = this.userImage;
    data['prize_id'] = this.prizeId;
    data['prize_name'] = this.prizeName;
    data['prize_image'] = this.prizeImage;
    return data;
  }
}
