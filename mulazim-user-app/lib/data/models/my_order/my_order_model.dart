class MyOrderModel {
  MyOrderData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  MyOrderModel({this.data, this.lang, this.msg, this.status, this.time});

  MyOrderModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new MyOrderData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class MyOrderData {
  int? perPage;
  int? currentPage;
  List<MyOrderItems>? items;

  MyOrderData({this.perPage, this.currentPage, this.items});

  MyOrderData.fromJson(Map<String, dynamic> json) {
    perPage = json['per_page'];
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <MyOrderItems>[];
      json['items'].forEach((v) {
        items!.add(new MyOrderItems.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['per_page'] = this.perPage;
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MyOrderItems {
  int? id;
  int? sum;
  num? price;
  num? actualPaid;
  num? shipment;
  int? deliveryType;
  int? lunchBoxFee;
  int? stateId;
  String? state;
  int? seckillState;
  int? restaurantId;
  String? restaurantName;
  String? restaurantLogo;
  int? restaurantState;
  int? payPlatform;
  String? payPlatformLabel;
  int? canClose;
  bool? isToday;
  bool? isCommented;
  bool? isComplaints;
  String? createdAt;
  int? expired;
  bool? isCoupon;
  num? totalDiscountAmount;
  num? couponPrice;
  bool? hasOrderRankingLucky;
  int? partRefundId;
  int? partRefundType;

  MyOrderItems(
      {this.id,
      this.sum,
      this.price,
      this.actualPaid,
      this.shipment,
      this.deliveryType,
      this.lunchBoxFee,
      this.stateId,
      this.state,
      this.seckillState,
      this.restaurantId,
      this.restaurantName,
      this.restaurantLogo,
      this.restaurantState,
      this.payPlatform,
      this.payPlatformLabel,
      this.canClose,
      this.isToday,
      this.isCommented,
      this.isComplaints,
      this.createdAt,
      this.expired,
      this.isCoupon,
      this.totalDiscountAmount,
      this.couponPrice,
      this.hasOrderRankingLucky,
      this.partRefundId,
      this.partRefundType});

  MyOrderItems.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    sum = json['sum'];
    price = json['price'];
    actualPaid = json['actual_paid'];
    shipment = json['shipment'];
    deliveryType = json['delivery_type'];
    lunchBoxFee = json['lunch_box_fee'];
    stateId = json['state_id'];
    state = json['state'];
    seckillState = json['seckill_state'];
    restaurantId = json['restaurant_id'];
    restaurantName = json['restaurant_name'];
    restaurantLogo = json['restaurant_logo'];
    restaurantState = json['restaurant_state'];
    payPlatform = json['pay_platform'];
    payPlatformLabel = json['pay_platform_label'];
    canClose = json['can_close'];
    isToday = json['isToday'];
    isCommented = json['is_commented'];
    isComplaints = json['is_complaints'];
    createdAt = json['created_at'];
    expired = json['expired'];
    isCoupon = json['is_coupon'];
    totalDiscountAmount = json['total_discount_amount'];
    couponPrice = json['coupon_price'];
    hasOrderRankingLucky = json['has_order_ranking_lucky'];
    partRefundId = json['part_refund_id'];
    partRefundType = json['part_refund_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['sum'] = this.sum;
    data['price'] = this.price;
    data['actual_paid'] = this.actualPaid;
    data['shipment'] = this.shipment;
    data['delivery_type'] = this.deliveryType;
    data['lunch_box_fee'] = this.lunchBoxFee;
    data['state_id'] = this.stateId;
    data['state'] = this.state;
    data['seckill_state'] = this.seckillState;
    data['restaurant_id'] = this.restaurantId;
    data['restaurant_name'] = this.restaurantName;
    data['restaurant_logo'] = this.restaurantLogo;
    data['restaurant_state'] = this.restaurantState;
    data['pay_platform'] = this.payPlatform;
    data['pay_platform_label'] = this.payPlatformLabel;
    data['can_close'] = this.canClose;
    data['isToday'] = this.isToday;
    data['is_commented'] = this.isCommented;
    data['is_complaints'] = this.isComplaints;
    data['created_at'] = this.createdAt;
    data['expired'] = this.expired;
    data['is_coupon'] = this.isCoupon;
    data['total_discount_amount'] = this.totalDiscountAmount;
    data['coupon_price'] = this.couponPrice;
    data['has_order_ranking_lucky'] = this.hasOrderRankingLucky;
    data['part_refund_id'] = this.partRefundId;
    data['part_refund_type'] = this.partRefundType;
    return data;
  }
}
