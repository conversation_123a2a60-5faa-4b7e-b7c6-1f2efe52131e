/// 订单评价数据模型
class OrderEvaluateModel {
  final ShipperEvaluate? shipper;
  final OrderDetailEvaluate orderDetail;
  final RestaurantInfo? restaurant;

  OrderEvaluateModel({
    this.shipper,
    required this.orderDetail,
    this.restaurant,
  });

  factory OrderEvaluateModel.fromJson(Map<String, dynamic> json) {
    // 处理配送员数据 - 按照API返回的数据结构
    ShipperEvaluate? shipper;
    if (json['shipper'] != null) {
      final shipperData = json['shipper'] as Map<String, dynamic>;
      final suggest = json['suggest'] as Map<String, dynamic>? ?? {};

      shipper = ShipperEvaluate(
        id: shipperData['id'],
        shipperName: shipperData['shipper_name'],
        shipperAvatar: shipperData['shipper_avatar'],
        areaName: shipperData['area_name'],
        orderId: 0, // 默认值，会在控制器中设置
        star: 5, // 默认5星
        comment: '', // 默认空评论
        isAnonymous: false, // 默认不匿名
        isSatisfied: 1, // 默认满意
        shipperBad: (suggest['shipper_bad'] as List?)
                ?.map((final item) =>
                    SuggestTag(name: item.toString(), active: false))
                .toList() ??
            [],
        shipperGood: (suggest['shipper_good'] as List?)
                ?.map((final item) =>
                    SuggestTag(name: item.toString(), active: false))
                .toList() ??
            [],
      );
    }

    // 处理订单详情数据 - 按照API返回的数据结构
    final orderDetailList = json['order_detail'] as List? ?? [];

    final foods = orderDetailList
        .map((final item) => FoodEvaluate(
              id: item['id'],
              foodId: item['food_id'],
              foodName: item['food_name'] ?? '',
              foodImage: item['food_image'],
              foodCount: item['food_count'],
              star: 5, // 总体评分默认5
              foodStar: 5, // 味道评分默认5
              boxStar: 5, // 包装评分默认5
              comment: item['comment'] ?? '', // 评论默认空
              isAnonymous: false, // 默认不匿名
              active: false, // 默认未选中
            ))
        .toList();

    final orderDetail = OrderDetailEvaluate(
      foods: foods,
      images: [], // 图片列表默认空
      comment: '', // 评论默认空
      isAnonymous: false, // 默认不匿名
      star: 5, // 总体评分默认5
      foodStar: 5, // 味道评分默认5
      boxStar: 5, // 包装评分默认5
    );

    // 处理餐厅数据 - 按照API返回的数据结构
    RestaurantInfo? restaurant;
    if (json['restaurant'] != null) {
      final restaurantData = json['restaurant'] as Map<String, dynamic>;
      restaurant = RestaurantInfo(
        name: restaurantData['name'],
        logo: restaurantData['logo'],
      );
    }

    return OrderEvaluateModel(
      shipper: shipper,
      orderDetail: orderDetail,
      restaurant: restaurant,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shipper': shipper?.toJson(),
      'order_detail': orderDetail.toJson(),
      'restaurant': restaurant?.toJson(),
    };
  }

  OrderEvaluateModel copyWith({
    ShipperEvaluate? shipper,
    OrderDetailEvaluate? orderDetail,
    RestaurantInfo? restaurant,
  }) {
    return OrderEvaluateModel(
      shipper: shipper ?? this.shipper,
      orderDetail: orderDetail ?? this.orderDetail,
      restaurant: restaurant ?? this.restaurant,
    );
  }
}

/// 配送员评价数据
class ShipperEvaluate {
  final int? id;
  final String? shipperName;
  final String? shipperAvatar;
  final String? areaName;
  final int orderId;
  final int star;
  final String comment;
  final bool isAnonymous;
  final int isSatisfied; // 1: 满意, 0: 不满意
  final List<SuggestTag> shipperBad;
  final List<SuggestTag> shipperGood;

  ShipperEvaluate({
    this.id,
    this.shipperName,
    this.shipperAvatar,
    this.areaName,
    required this.orderId,
    this.star = 5,
    this.comment = '',
    this.isAnonymous = false,
    this.isSatisfied = 1,
    this.shipperBad = const [],
    this.shipperGood = const [],
  });

  factory ShipperEvaluate.fromJson(Map<String, dynamic> json) {
    return ShipperEvaluate(
      id: json['id'],
      shipperName: json['shipper_name'],
      shipperAvatar: json['shipper_avatar'],
      areaName: json['area_name'],
      orderId: json['order_id'] ?? 0,
      star: json['star'] ?? 5,
      comment: json['comment'] ?? '',
      isAnonymous: json['is_anonymous'] ?? false,
      isSatisfied: json['is_satisfied'] ?? 1,
      shipperBad: (json['shipper_bad'] as List?)
              ?.map((item) => SuggestTag.fromString(item.toString()))
              .toList() ??
          [],
      shipperGood: (json['shipper_good'] as List?)
              ?.map((item) => SuggestTag.fromString(item.toString()))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shipper_name': shipperName,
      'shipper_avatar': shipperAvatar,
      'area_name': areaName,
      'order_id': orderId,
      'star': star,
      'comment': comment,
      'is_anonymous': isAnonymous,
      'is_satisfied': isSatisfied,
      'shipper_bad': shipperBad.map((item) => item.toJson()).toList(),
      'shipper_good': shipperGood.map((item) => item.toJson()).toList(),
    };
  }

  ShipperEvaluate copyWith({
    int? id,
    String? shipperName,
    String? shipperAvatar,
    String? areaName,
    int? orderId,
    int? star,
    String? comment,
    bool? isAnonymous,
    int? isSatisfied,
    List<SuggestTag>? shipperBad,
    List<SuggestTag>? shipperGood,
  }) {
    return ShipperEvaluate(
      id: id ?? this.id,
      shipperName: shipperName ?? this.shipperName,
      shipperAvatar: shipperAvatar ?? this.shipperAvatar,
      areaName: areaName ?? this.areaName,
      orderId: orderId ?? this.orderId,
      star: star ?? this.star,
      comment: comment ?? this.comment,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      isSatisfied: isSatisfied ?? this.isSatisfied,
      shipperBad: shipperBad ?? this.shipperBad,
      shipperGood: shipperGood ?? this.shipperGood,
    );
  }
}

/// 订单详情评价数据
class OrderDetailEvaluate {
  final List<FoodEvaluate> foods;
  final List<String> images;
  final String comment;
  final bool isAnonymous;
  final int star;
  final int foodStar;
  final int boxStar;

  OrderDetailEvaluate({
    this.foods = const [],
    this.images = const [],
    this.comment = '',
    this.isAnonymous = false,
    this.star = 5,
    this.foodStar = 5,
    this.boxStar = 5,
  });

  factory OrderDetailEvaluate.fromJson(dynamic json) {
    if (json is List) {
      // 处理数组形式的order_detail
      return OrderDetailEvaluate(
        foods: json.map((item) => FoodEvaluate.fromJson(item)).toList(),
      );
    } else if (json is Map<String, dynamic>) {
      // 处理对象形式的order_detail
      return OrderDetailEvaluate(
        foods: (json['foods'] as List?)
                ?.map((item) => FoodEvaluate.fromJson(item))
                .toList() ??
            [],
        images: (json['images'] as List?)?.cast<String>() ?? [],
        comment: json['comment'] ?? '',
        isAnonymous: json['is_anonymous'] ?? false,
        star: json['star'] ?? 5,
        foodStar: json['food_star'] ?? 5,
        boxStar: json['box_star'] ?? 5,
      );
    }
    return OrderDetailEvaluate();
  }

  Map<String, dynamic> toJson() {
    return {
      'foods': foods.map((item) => item.toJson()).toList(),
      'images': images,
      'comment': comment,
      'is_anonymous': isAnonymous,
      'star': star,
      'food_star': foodStar,
      'box_star': boxStar,
    };
  }

  OrderDetailEvaluate copyWith({
    List<FoodEvaluate>? foods,
    List<String>? images,
    String? comment,
    bool? isAnonymous,
    int? star,
    int? foodStar,
    int? boxStar,
  }) {
    return OrderDetailEvaluate(
      foods: foods ?? this.foods,
      images: images ?? this.images,
      comment: comment ?? this.comment,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      star: star ?? this.star,
      foodStar: foodStar ?? this.foodStar,
      boxStar: boxStar ?? this.boxStar,
    );
  }
}

/// 美食评价数据
class FoodEvaluate {
  final int? id;
  final int? foodId;
  final String? foodName;
  final String? foodImage;
  final int? foodCount;
  final int star;
  final int foodStar;
  final int boxStar;
  final String comment;
  final bool isAnonymous;
  final bool active;
  final List<String> images;

  FoodEvaluate({
    this.id,
    this.foodId,
    this.foodName,
    this.foodImage,
    this.foodCount,
    this.star = 5,
    this.foodStar = 5,
    this.boxStar = 5,
    this.comment = '',
    this.isAnonymous = false,
    this.active = false,
    this.images = const [],
  });

  factory FoodEvaluate.fromJson(Map<String, dynamic> json) {
    return FoodEvaluate(
      id: json['id'],
      foodId: json['food_id'],
      foodName: json['food_name'],
      foodImage: json['food_image'],
      foodCount: json['food_count'],
      star: json['star'] ?? 5,
      foodStar: json['food_star'] ?? 5,
      boxStar: json['box_star'] ?? 5,
      comment: json['comment'] ?? '',
      isAnonymous: json['is_anonymous'] ?? false,
      active: json['active'] ?? false,
      images: (json['images'] as List?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'food_id': foodId,
      'food_name': foodName,
      'food_image': foodImage,
      'food_count': foodCount,
      'star': star,
      'food_star': foodStar,
      'box_star': boxStar,
      'comment': comment,
      'is_anonymous': isAnonymous,
      'active': active,
      'images': images,
    };
  }

  FoodEvaluate copyWith({
    int? id,
    int? foodId,
    String? foodName,
    String? foodImage,
    int? foodCount,
    int? star,
    int? foodStar,
    int? boxStar,
    String? comment,
    bool? isAnonymous,
    bool? active,
    List<String>? images,
  }) {
    return FoodEvaluate(
      id: id ?? this.id,
      foodId: foodId ?? this.foodId,
      foodName: foodName ?? this.foodName,
      foodImage: foodImage ?? this.foodImage,
      foodCount: foodCount ?? this.foodCount,
      star: star ?? this.star,
      foodStar: foodStar ?? this.foodStar,
      boxStar: boxStar ?? this.boxStar,
      comment: comment ?? this.comment,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      active: active ?? this.active,
      images: images ?? this.images,
    );
  }
}

/// 餐厅信息
class RestaurantInfo {
  final String? name;
  final String? logo;

  RestaurantInfo({
    this.name,
    this.logo,
  });

  factory RestaurantInfo.fromJson(Map<String, dynamic> json) {
    return RestaurantInfo(
      name: json['name'],
      logo: json['logo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'logo': logo,
    };
  }
}

/// 建议标签
class SuggestTag {
  final String name;
  final bool active;

  SuggestTag({
    required this.name,
    this.active = false,
  });

  factory SuggestTag.fromString(String name) {
    return SuggestTag(name: name);
  }

  factory SuggestTag.fromJson(Map<String, dynamic> json) {
    return SuggestTag(
      name: json['name'] ?? '',
      active: json['active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'active': active,
    };
  }

  SuggestTag copyWith({
    String? name,
    bool? active,
  }) {
    return SuggestTag(
      name: name ?? this.name,
      active: active ?? this.active,
    );
  }
}

/// 建议词汇数据
class SuggestWords {
  final List<String> shipperBad;
  final List<String> shipperGood;

  SuggestWords({
    this.shipperBad = const [],
    this.shipperGood = const [],
  });

  factory SuggestWords.fromJson(Map<String, dynamic> json) {
    return SuggestWords(
      shipperBad: (json['shipper_bad'] as List?)?.cast<String>() ?? [],
      shipperGood: (json['shipper_good'] as List?)?.cast<String>() ?? [],
    );
  }
}
