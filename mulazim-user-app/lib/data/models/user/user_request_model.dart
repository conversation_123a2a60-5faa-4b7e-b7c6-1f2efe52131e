/// 用户信息模型
class UserRequestModel {
  /// 用户名
  final String name;

  /// 头像
  final String? avatar;

  /// 性别
  final int? gender;

  /// 生日
  final String? birthDay;

  /// 构造函数
  const UserRequestModel({
    required this.name,
    this.avatar,
    this.gender,
    this.birthDay,
  });

  /// 从JSON创建对象
  factory UserRequestModel.fromJson(final Map<String, dynamic> json) {
    return UserRequestModel(
      name: json['name'] as String,
      avatar: json['avatar'] as String?,
      gender: json['gender'] as int?,
      birthDay: json['birth_day'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'avatar': avatar,
      'gender': gender,
      'birth_day': birthDay,
    };
  }

  /// 创建副本
  UserRequestModel copyWith({
    final String? name,
    final String? avatar,
    final int? gender,
    final String? birthDay,
  }) {
    return UserRequestModel(
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      gender: gender ?? this.gender,
      birthDay: birthDay ?? this.birthDay,
    );
  }
}
