/// 用户排行榜数据模型
class UserRankingModel {
  /// 排行榜活动信息
  final RankingActivity? ranking;

  /// 用户信息
  final RankingUser? user;

  /// 剩余时间（秒）
  final int? remainderTime;

  const UserRankingModel({
    this.ranking,
    this.user,
    this.remainderTime,
  });

  factory UserRankingModel.fromJson(Map<String, dynamic> json) {
    return UserRankingModel(
      ranking: json['ranking'] != null
          ? RankingActivity.fromJson(json['ranking'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? RankingUser.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      remainderTime: json['remainder_time'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ranking': ranking?.toJson(),
      'user': user?.toJson(),
      'remainder_time': remainderTime,
    };
  }
}

/// 排行榜活动信息
class RankingActivity {
  /// 活动ID
  final int? id;

  /// 活动开始时间
  final String? rankingBeginTime;

  /// 活动结束时间
  final String? rankingEndTime;

  /// 奖品开始时间
  final String? rewardBeginTime;

  /// 奖品结束时间
  final String? rewardEndTime;

  /// 活动规则（维语）
  final String? rankingRuleUg;

  /// 活动规则（中文）
  final String? rankingRuleZh;

  /// 活动状态 (0-未开始, 1-进行中, 2-已结束, 3-已完成, 4-已确认)
  final int? rankingState;

  /// 奖品是否完成
  final int? rewardFinish;

  /// 活动是否已确认
  final bool? confirmed;

  /// 奖品列表
  final List<RankingGift>? gift;

  /// 用户排行列表
  final List<RankingUserInfo>? users;

  const RankingActivity({
    this.id,
    this.rankingBeginTime,
    this.rankingEndTime,
    this.rewardBeginTime,
    this.rewardEndTime,
    this.rankingRuleUg,
    this.rankingRuleZh,
    this.rankingState,
    this.rewardFinish,
    this.confirmed,
    this.gift,
    this.users,
  });

  factory RankingActivity.fromJson(Map<String, dynamic> json) {
    return RankingActivity(
      id: json['id'] as int?,
      rankingBeginTime: json['ranking_begin_time'] as String?,
      rankingEndTime: json['ranking_end_time'] as String?,
      rewardBeginTime: json['reward_begin_time'] as String?,
      rewardEndTime: json['reward_end_time'] as String?,
      rankingRuleUg: json['ranking_rule_ug'] as String?,
      rankingRuleZh: json['ranking_rule_zh'] as String?,
      rankingState: json['ranking_state'] as int?,
      rewardFinish: json['reward_finish'] as int?,
      confirmed: json['confirmed'] == 1,
      gift: json['gift'] != null
          ? (json['gift'] as List)
              .map((item) => RankingGift.fromJson(item as Map<String, dynamic>))
              .toList()
          : null,
      users: json['users'] != null
          ? (json['users'] as List)
              .map((item) =>
                  RankingUserInfo.fromJson(item as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ranking_begin_time': rankingBeginTime,
      'ranking_end_time': rankingEndTime,
      'reward_begin_time': rewardBeginTime,
      'reward_end_time': rewardEndTime,
      'ranking_rule_ug': rankingRuleUg,
      'ranking_rule_zh': rankingRuleZh,
      'ranking_state': rankingState,
      'reward_finish': rewardFinish,
      'confirmed': confirmed == true ? 1 : 0,
      'gift': gift?.map((item) => item.toJson()).toList(),
      'users': users?.map((item) => item.toJson()).toList(),
    };
  }
}

/// 奖品信息
class RankingGift {
  /// 奖品排名
  final int? rewardRank;

  /// 奖品名称（中文）
  final String? rewardNameZh;

  /// 奖品名称（维语）
  final String? rewardNameUg;

  /// 奖品图片
  final String? rewardImage;

  /// 奖品数量
  final int? rewardCount;

  /// 原价
  final String? rewardOldPrice;

  const RankingGift({
    this.rewardRank,
    this.rewardNameZh,
    this.rewardNameUg,
    this.rewardImage,
    this.rewardCount,
    this.rewardOldPrice,
  });

  factory RankingGift.fromJson(Map<String, dynamic> json) {
    return RankingGift(
      rewardRank: json['reward_rank'] as int?,
      rewardNameZh: json['reward_name_zh'] as String?,
      rewardNameUg: json['reward_name_ug'] as String?,
      rewardImage: json['reward_image'] as String?,
      rewardCount: int.parse(json['reward_count'].toString()),
      rewardOldPrice: json['reward_old_price'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reward_rank': rewardRank,
      'reward_name_zh': rewardNameZh,
      'reward_name_ug': rewardNameUg,
      'reward_image': rewardImage,
      'reward_count': rewardCount,
      'reward_old_price': rewardOldPrice,
    };
  }
}

/// 排行榜用户信息
class RankingUserInfo {
  /// 排行榜标题（维语）
  final String? rankingTitleUg;

  /// 排行榜标题（中文）
  final String? rankingTitleZh;

  /// 奖励礼品
  final String? rewardGift;

  /// 用户ID
  final int? userId;

  /// 用户名称
  final String? name;

  /// 用户手机号
  final String? mobile;

  /// 用户头像
  final String? avatar;

  /// 是否中奖
  final int? isWinner;

  /// 奖品等级
  final int? prizeLavel;

  /// 排行榜ID
  final int? rankingId;

  /// 订单总金额
  final num? totalPrice;

  /// 中奖礼品名称
  final String? giftName;

  /// 中奖礼品图片
  final String? giftImage;

  const RankingUserInfo({
    this.rankingTitleUg,
    this.rankingTitleZh,
    this.rewardGift,
    this.userId,
    this.name,
    this.mobile,
    this.avatar,
    this.isWinner,
    this.prizeLavel,
    this.rankingId,
    this.totalPrice,
    this.giftName,
    this.giftImage,
  });

  factory RankingUserInfo.fromJson(Map<String, dynamic> json) {
    return RankingUserInfo(
      rankingTitleUg: json['ranking_title_ug'] as String?,
      rankingTitleZh: json['ranking_title_zh'] as String?,
      rewardGift: json['reward_gift'] as String?,
      userId: json['user_id'] as int?,
      name: json['name'] as String?,
      mobile: json['mobile'] as String?,
      avatar: json['avatar'] as String?,
      isWinner: json['is_winner'] as int?,
      prizeLavel: json['prize_lavel'] as int?,
      rankingId: json['ranking_id'] as int?,
      totalPrice: json['total_price'] as num?,
      giftName: json['gift_name'] as String?,
      giftImage: json['gift_image'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ranking_title_ug': rankingTitleUg,
      'ranking_title_zh': rankingTitleZh,
      'reward_gift': rewardGift,
      'user_id': userId,
      'name': name,
      'mobile': mobile,
      'avatar': avatar,
      'is_winner': isWinner,
      'prize_lavel': prizeLavel,
      'ranking_id': rankingId,
      'total_price': totalPrice,
      'gift_name': giftName,
      'gift_image': giftImage,
    };
  }
}

/// 当前用户排行榜信息
class RankingUser {
  /// 用户ID
  final int? id;

  /// 用户名称
  final String? name;

  /// 用户手机号
  final String? mobile;

  /// 用户头像
  final String? avatar;

  /// 用户状态 (1-被限制)
  final int? userState;

  /// 是否已参加活动
  final bool? isJoined;

  /// 是否中奖 (1-中奖, 0-未中奖)
  final int? isWinner;

  /// 当前排名 (0表示未上榜)
  final int? currentRanking;

  /// 消费金额
  final num? amount;

  /// 剩余金额
  final num? remainingAmount;

  /// 城市名称
  final String? cityName;

  /// 城市名称对象
  final CityNames? cityNames;

  /// 中奖礼品信息
  final WinnerGift? winnerGift;

  const RankingUser({
    this.id,
    this.name,
    this.mobile,
    this.avatar,
    this.userState,
    this.isJoined,
    this.isWinner,
    this.currentRanking,
    this.amount,
    this.remainingAmount,
    this.cityName,
    this.cityNames,
    this.winnerGift,
  });

  factory RankingUser.fromJson(Map<String, dynamic> json) {
    return RankingUser(
      id: json['id'] as int?,
      name: json['name'] as String?,
      mobile: json['mobile'] as String?,
      avatar: json['avatar'] as String?,
      userState: json['user_state'] as int?,
      isJoined: json['is_joined'] as bool?,
      isWinner: json['is_winner'] as int?,
      currentRanking: json['current_ranking'] as int?,
      amount: json['amount'] as num?,
      remainingAmount: json['remaining_amount'] as num?,
      cityName: json['city_name'] as String?,
      cityNames: json['city_names'] != null
          ? CityNames.fromJson(json['city_names'] as Map<String, dynamic>)
          : null,
      winnerGift: json['winner_gift'] != null
          ? WinnerGift.fromJson(json['winner_gift'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mobile': mobile,
      'avatar': avatar,
      'user_state': userState,
      'is_joined': isJoined,
      'is_winner': isWinner,
      'current_ranking': currentRanking,
      'amount': amount,
      'remaining_amount': remainingAmount,
      'city_name': cityName,
      'city_names': cityNames?.toJson(),
      'winner_gift': winnerGift?.toJson(),
    };
  }
}

/// 城市名称
class CityNames {
  /// 维语城市名
  final String? ug;

  /// 中文城市名
  final String? zh;

  const CityNames({
    this.ug,
    this.zh,
  });

  factory CityNames.fromJson(Map<String, dynamic> json) {
    return CityNames(
      ug: json['ug'] as String?,
      zh: json['zh'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ug': ug,
      'zh': zh,
    };
  }
}

/// 中奖礼品信息
class WinnerGift {
  /// 礼品排名
  final int? rewardRank;

  /// 礼品名称（中文）
  final String? rewardNameZh;

  /// 礼品名称（维语）
  final String? rewardNameUg;

  /// 礼品图片
  final String? rewardImage;

  /// 礼品数量
  final int? rewardCount;

  /// 原价
  final String? rewardOldPrice;

  const WinnerGift({
    this.rewardRank,
    this.rewardNameZh,
    this.rewardNameUg,
    this.rewardImage,
    this.rewardCount,
    this.rewardOldPrice,
  });

  factory WinnerGift.fromJson(Map<String, dynamic> json) {
    return WinnerGift(
      rewardRank: json['reward_rank'] as int?,
      rewardNameZh: json['reward_name_zh'] as String?,
      rewardNameUg: json['reward_name_ug'] as String?,
      rewardImage: json['reward_image'] as String?,
      rewardCount: int.parse(json['reward_count'].toString()),
      rewardOldPrice: json['reward_old_price'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reward_rank': rewardRank,
      'reward_name_zh': rewardNameZh,
      'reward_name_ug': rewardNameUg,
      'reward_image': rewardImage,
      'reward_count': rewardCount,
      'reward_old_price': rewardOldPrice,
    };
  }
}

/// 排行榜页面参数
class RankingPageParams {
  /// 区域ID
  final String? areaId;

  /// 餐厅ID
  final String? restaurantId;

  /// 排行榜ID
  final String? rankingId;

  /// 类型 (1-平台活动, 2-餐厅活动)
  final String? type;

  const RankingPageParams({
    this.areaId,
    this.restaurantId,
    this.rankingId,
    this.type,
  });

  factory RankingPageParams.fromMap(Map<String, dynamic> map) {
    return RankingPageParams(
      areaId: map['area_id']?.toString(),
      restaurantId: map['restaurant_id']?.toString(),
      rankingId: map['ranking_id']?.toString(),
      type: map['type']?.toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'area_id': areaId,
      'restaurant_id': restaurantId,
      'ranking_id': rankingId,
      'type': type,
    };
  }
}
