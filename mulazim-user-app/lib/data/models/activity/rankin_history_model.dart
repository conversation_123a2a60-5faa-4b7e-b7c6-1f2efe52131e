
class RankingHistoryModel {
  int? status;
  String? msg;
  List<RankingHistoryData>? data;
  String? lang;

  RankingHistoryModel({this.status, this.msg, this.data, this.lang});

  RankingHistoryModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : (json["data"] as List).map((e) => RankingHistoryData.fromJson(e)).toList();
    lang = json["lang"];
  }
}

class RankingHistoryData {
  String? rewardNameUg;
  String? rewardImage;
  int? rewardRank;
  String? rewardOldPrice;
  int? rewardCount;
  String? rewardNameZh;
  int? level;
  String? time;
  int? rankingId;

  RankingHistoryData({this.rewardNameUg, this.rewardImage, this.rewardRank, this.rewardOldPrice, this.rewardCount, this.rewardNameZh, this.level, this.time, this.rankingId});

  RankingHistoryData.fromJson(Map<String, dynamic> json) {
    rewardNameUg = json["reward_name_ug"];
    rewardImage = json["reward_image"];
    rewardRank = json["reward_rank"];
    rewardOldPrice = json["reward_old_price"];
    rewardCount = json["reward_count"];
    rewardNameZh = json["reward_name_zh"];
    level = json["level"];
    time = json["time"];
    rankingId = json["ranking_id"];
  }
}