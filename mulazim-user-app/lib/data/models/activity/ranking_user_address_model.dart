
class RankingUserAddressModel {
  int? status;
  String? msg;
  RankingUserAddressData? data;
  String? lang;

  RankingUserAddressModel({this.status, this.msg, this.data, this.lang});

  RankingUserAddressModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : RankingUserAddressData.fromJson(json["data"]);
    lang = json["lang"];
  }

}

class RankingUserAddressData {
  List<AddressList>? addressList;
  dynamic confirmedAddressId;

  RankingUserAddressData({this.addressList, this.confirmedAddressId});

  RankingUserAddressData.fromJson(Map<String, dynamic> json) {
    addressList = json["address_list"] == null ? null : (json["address_list"] as List).map((e) => AddressList.fromJson(e)).toList();
    confirmedAddressId = json["confirmed_address_id"];
  }

}

class AddressList {
  int? id;
  String? name;
  String? tel;
  int? cityId;
  String? cityName;
  int? areaId;
  String? areaName;
  String? servicePhone;
  int? streetId;
  String? streetName;
  int? buildingId;
  String? buildingName;
  String? address;
  double? lat;
  double? lng;
  int? state;

  AddressList({this.id, this.name, this.tel, this.cityId, this.cityName, this.areaId, this.areaName, this.servicePhone, this.streetId, this.streetName, this.buildingId, this.buildingName, this.address, this.lat, this.lng, this.state});

  AddressList.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    tel = json["tel"];
    cityId = json["city_id"];
    cityName = json["city_name"];
    areaId = json["area_id"];
    areaName = json["area_name"];
    servicePhone = json["service_phone"];
    streetId = json["street_id"];
    streetName = json["street_name"];
    buildingId = json["building_id"];
    buildingName = json["building_name"];
    address = json["address"];
    lat = json["lat"];
    lng = json["lng"];
    state = json["state"];
  }

}