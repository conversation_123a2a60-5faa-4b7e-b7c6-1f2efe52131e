class SecKillFoodModel {
  SecKillFoodData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  SecKillFoodModel({this.data, this.lang, this.msg, this.status, this.time});

  SecKillFoodModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new SecKillFoodData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class SecKillFoodData {
  int? refreshSecond;
  List<SecKillFoodTimes>? times;
  List<Foods>? foods;

  SecKillFoodData({this.refreshSecond, this.times, this.foods});

  SecKillFoodData.fromJson(Map<String, dynamic> json) {
    refreshSecond = json['refresh_second'];
    if (json['times'] != null) {
      times = <SecKillFoodTimes>[];
      json['times'].forEach((v) {
        times!.add(new SecKillFoodTimes.fromJson(v));
      });
    }
    if (json['foods'] != null) {
      foods = <Foods>[];
      json['foods'].forEach((v) {
        foods!.add(new Foods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['refresh_second'] = this.refreshSecond;
    if (this.times != null) {
      data['times'] = this.times!.map((v) => v.toJson()).toList();
    }
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SecKillFoodTimes {
  int? areaId;
  String? beginTime;
  String? endTime;
  int? active;
  int? isToday;

  SecKillFoodTimes({this.areaId, this.beginTime, this.endTime, this.active, this.isToday});

  SecKillFoodTimes.fromJson(Map<String, dynamic> json) {
    areaId = json['area_id'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    active = json['active'];
    isToday = json['is_today'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['area_id'] = this.areaId;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['active'] = this.active;
    data['is_today'] = this.isToday;
    return data;
  }
}

class Foods {
  int? id;
  int? seckillId;
  num? price;
  int? restaurantId;
  num? oldPrice;
  dynamic? saledCount;
  int? totalCount;
  String? beginTime;
  String? endTime;
  int? maxOrderCount;
  String? image;
  String? name;
  String? restaurantName;
  int? seckillActive;

  Foods(
      {this.id,
        this.seckillId,
        this.price,
        this.restaurantId,
        this.oldPrice,
        this.saledCount,
        this.totalCount,
        this.beginTime,
        this.endTime,
        this.maxOrderCount,
        this.image,
        this.name,
        this.restaurantName,
        this.seckillActive});

  Foods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    seckillId = json['seckill_id'];
    price = json['price'];
    restaurantId = json['restaurant_id'];
    oldPrice = json['old_price'];
    saledCount = json['saled_count'];
    totalCount = json['total_count'];
    beginTime = json['begin_time'];
    endTime = json['end_time'];
    maxOrderCount = json['max_order_count'];
    image = json['image'];
    name = json['name'];
    restaurantName = json['restaurant_name'];
    seckillActive = json['seckill_active'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['seckill_id'] = this.seckillId;
    data['price'] = this.price;
    data['restaurant_id'] = this.restaurantId;
    data['old_price'] = this.oldPrice;
    data['saled_count'] = this.saledCount;
    data['total_count'] = this.totalCount;
    data['begin_time'] = this.beginTime;
    data['end_time'] = this.endTime;
    data['max_order_count'] = this.maxOrderCount;
    data['image'] = this.image;
    data['name'] = this.name;
    data['restaurant_name'] = this.restaurantName;
    data['seckill_active'] = this.seckillActive;
    return data;
  }
}