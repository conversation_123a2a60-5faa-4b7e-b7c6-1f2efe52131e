/// 创建订单所需要的参数
class CreateOrderParams {
  /// 订单ID
  final int restaurantId;

  /// 时间区
  final String timezone;

  /// 配送时间
  final String bookingTime;

  /// 订单模式（实时订单 0｜预订单 1）
  final int orderType;

  /// 订单备注
  final String description;

  /// 订单金额
  final num price;

  /// 美食金额
  final num foodsPrice;

  /// 饭盒费
  final num lunchBoxFee;

  /// 配送模式（配送｜自取）
  final int deliveryType;

  /// 活动ID
  final int activityId;

  ///
  final String takeTimeListGetTime;

  /// 配送地址ID
  final int addressId;

  /// 配送费用
  final num shipment;

  /// 选中的美食
  final List<Food> foods;

  /// 优惠之前的金额
  final num originalPrice;

  /// 减配送费金额
  final num? reduceShipment;

  /// 满减活动
  final num? reductionFee;

  /// 优惠券ID
  final int? couponId;

  /// 特殊ID
  final int? specialId;

  /// 美食原始价格
  final num allFoodPrice;

  /// 构造方法
  CreateOrderParams({
    required this.restaurantId,
    required this.timezone,
    required this.bookingTime,
    required this.orderType,
    required this.description,
    required this.price,
    required this.foodsPrice,
    required this.lunchBoxFee,
    required this.deliveryType,
    required this.activityId,
    required this.takeTimeListGetTime,
    required this.addressId,
    required this.shipment,
    required this.foods,
    required this.originalPrice,
    this.reduceShipment,
    this.reductionFee,
    this.couponId,
    this.specialId,
    required this.allFoodPrice,
  });
}

/// 美食模型
class Food {
  /// 美食ID
  final int id;

  /// 美食数量
  final int count;

  /// 秒杀ID
  final int? seckillId;

  /// 食品类型（0:普通食品, 1:规格食品, 2:套餐食品）
  final int foodType;

  /// 多重折扣ID
  final int? discountId;

  /// 多重折扣步骤号
  final int? discountNumber;

  /// 规格选项ID列表
  final List<int>? optionIds;

  /// 构造方法
  Food({
    required this.id,
    required this.count,
    this.seckillId,
    this.foodType = 0,
    this.discountId,
    this.discountNumber,
    this.optionIds,
  });
}
