class TakeTimeListModel {
  int? status;
  String? msg;
  TakeTimeListData? data;
  String? lang;

  TakeTimeListModel({this.status, this.msg, this.data, this.lang});

  TakeTimeListModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : TakeTimeListData.fromJson(json["data"]);
    lang = json["lang"];
  }

  static List<TakeTimeListModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeListModel.fromJson).toList();
  }
}

class TakeTimeListData {
  TakeTimeAddress? address;
  List<TakeTimeCst>? cst;
  bool? canOrder;
  int? maxOrderPerHour;
  int? receiveOrderTime;
  int? takeOrderTime;
  String? nowDateTime;
  bool? isBindAvator;
  TakeTimeShipmentInfo? shipmentInfo;
  List<dynamic>? shipmentSteps;
  TakeTimeCoupon? coupon;
  String? selfTakeConfirm;
  TakeTimeSelfTakeMsg? selfTakeMsg;
  TakeTimeSnowGame? snowGame;

  TakeTimeListData({this.address, this.cst, this.canOrder, this.maxOrderPerHour, this.receiveOrderTime, this.takeOrderTime, this.nowDateTime, this.isBindAvator, this.shipmentInfo, this.shipmentSteps, this.coupon, this.selfTakeConfirm, this.selfTakeMsg, this.snowGame});

  TakeTimeListData.fromJson(Map<String, dynamic> json) {
    address = json["address"] == null ? null : TakeTimeAddress.fromJson(json["address"]);
    cst = json["cst"] == null ? null : (json["cst"] as List).map((e) => TakeTimeCst.fromJson(e)).toList();
    canOrder = json["can_order"];
    maxOrderPerHour = json["max_order_per_hour"];
    receiveOrderTime = json["receive_order_time"];
    takeOrderTime = json["take_order_time"];
    nowDateTime = json["now_date_time"];
    isBindAvator = json["is_bind_avator"];
    shipmentInfo = json["shipment_info"] == null ? null : TakeTimeShipmentInfo.fromJson(json["shipment_info"]);
    shipmentSteps = json["shipment_steps"] ?? [];
    coupon = json["coupon"] == null ? null : TakeTimeCoupon.fromJson(json["coupon"]);
    selfTakeConfirm = json["self_take_confirm"].toString();
    selfTakeMsg = json["self_take_msg"] == null ? null : TakeTimeSelfTakeMsg.fromJson(json["self_take_msg"]);
    snowGame = json["snow_game"] == null ? null : TakeTimeSnowGame.fromJson(json["snow_game"]);
  }

  static List<TakeTimeListData> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeListData.fromJson).toList();
  }
}

class TakeTimeSnowGame {
  int? percent;
  int? leftAmount;
  int? activityId;
  int? addRate;

  TakeTimeSnowGame({this.percent, this.leftAmount, this.activityId, this.addRate});

  TakeTimeSnowGame.fromJson(Map<String, dynamic> json) {
    percent = json["percent"];
    leftAmount = json["left_amount"];
    activityId = json["activity_id"];
    addRate = json["add_rate"];
  }

  static List<TakeTimeSnowGame> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeSnowGame.fromJson).toList();
  }
}

class TakeTimeSelfTakeMsg {
  String? title;
  String? msg;
  String? btn;
  String? closeBtn;

  TakeTimeSelfTakeMsg({this.title, this.msg, this.btn, this.closeBtn});

  TakeTimeSelfTakeMsg.fromJson(Map<String, dynamic> json) {
    title = json["title"];
    msg = json["msg"];
    btn = json["btn"];
    closeBtn = json["close_btn"];
  }

  static List<TakeTimeSelfTakeMsg> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeSelfTakeMsg.fromJson).toList();
  }
}

class TakeTimeCoupon {
  List<CouponItem>? list;
  int? total;
  int? page;
  int? limit;
  List<dynamic>? notNow;
  List<ExpiredCoupon>? expired;
  List<CouponItem>? using;

  TakeTimeCoupon({this.list, this.total, this.page, this.limit, this.notNow, this.expired, this.using});

  TakeTimeCoupon.fromJson(Map<String, dynamic> json) {
    list = json["list"] == null ? [] : (json["list"] as List).map((e) => CouponItem.fromJson(e)).toList();
    total = json["total"];
    page = json["page"];
    limit = json["limit"];
    notNow = json["not_now"] ?? [];
    expired = json["expired"] == null ? null : (json["expired"] as List).map((e) => ExpiredCoupon.fromJson(e)).toList();
    using = json["using"] == null
        ? []
        : (json["using"] as List).map((e) => CouponItem.fromJson(e)).toList();
  }

  static List<TakeTimeCoupon> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeCoupon.fromJson).toList();
  }
}

class CouponItem {
  int? id;
  dynamic lotteryOrderId;
  dynamic couponType;
  String? userCouponStartUseTime;
  String? userCouponEndUseTime;
  int? state;
  int? userId;
  int? couponId;
  String? notice;
  String? name;
  int? cityId;
  int? areaId;
  String? price;
  String? minPrice;
  int? count;
  String? startTime;
  String? endTime;
  String? startUseTime;
  String? endUseTime;
  String? createdAt;
  String? updatedAt;
  TakeTimeCouponItem? coupon;
  String? order_price;

  CouponItem({this.id, this.lotteryOrderId, this.couponType, this.userCouponStartUseTime, 
    this.userCouponEndUseTime, this.state, this.userId, this.couponId, this.notice, 
    this.name, this.cityId, this.areaId, this.price, this.minPrice, this.count, 
    this.startTime, this.endTime, this.startUseTime, this.endUseTime, 
    this.createdAt, this.updatedAt, this.coupon, this.order_price});

  CouponItem.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    lotteryOrderId = json["lottery_order_id"];
    couponType = json["coupon_type"];
    userCouponStartUseTime = json["user_coupon_start_use_time"];
    userCouponEndUseTime = json["user_coupon_end_use_time"];
    state = json["state"];
    userId = json["user_id"];
    couponId = json["coupon_id"];
    notice = json["notice"];
    name = json["name"];
    cityId = json["city_id"];
    areaId = json["area_id"];
    price = json["price"];
    minPrice = json["min_price"];
    count = json["count"];
    startTime = json["start_time"];
    endTime = json["end_time"];
    startUseTime = json["start_use_time"];
    endUseTime = json["end_use_time"];
    createdAt = json["created_at"];
    updatedAt = json["updated_at"];
    coupon = json["coupon"] == null ? null : TakeTimeCouponItem.fromJson(json["coupon"]);
    order_price = json["order_price"];
  }

  static List<CouponItem> fromList(List<Map<String, dynamic>> list) {
    return list.map(CouponItem.fromJson).toList();
  }
}

class ExpiredCoupon {
  int? id;
  int? lotteryOrderId;
  int? couponType;
  String? userCouponStartUseTime;
  String? userCouponEndUseTime;
  int? state;
  int? userId;
  int? couponId;
  String? notice;
  String? name;
  int? cityId;
  int? areaId;
  String? price;
  String? minPrice;
  int? count;
  String? startTime;
  String? endTime;
  String? startUseTime;
  String? endUseTime;
  String? createdAt;
  String? updatedAt;
  TakeTimeCouponItem? coupon;

  ExpiredCoupon({this.id, this.lotteryOrderId, this.couponType, this.userCouponStartUseTime, this.userCouponEndUseTime, this.state, this.userId, this.couponId, this.notice, this.name, this.cityId, this.areaId, this.price, this.minPrice, this.count, this.startTime, this.endTime, this.startUseTime, this.endUseTime, this.createdAt, this.updatedAt, this.coupon});

  ExpiredCoupon.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    lotteryOrderId = json["lottery_order_id"];
    couponType = json["coupon_type"];
    userCouponStartUseTime = json["user_coupon_start_use_time"];
    userCouponEndUseTime = json["user_coupon_end_use_time"];
    state = json["state"];
    userId = json["user_id"];
    couponId = json["coupon_id"];
    notice = json["notice"];
    name = json["name"];
    cityId = json["city_id"];
    areaId = json["area_id"];
    price = json["price"];
    minPrice = json["min_price"];
    count = json["count"];
    startTime = json["start_time"];
    endTime = json["end_time"];
    startUseTime = json["start_use_time"];
    endUseTime = json["end_use_time"];
    createdAt = json["created_at"];
    updatedAt = json["updated_at"];
    coupon = json["coupon"] == null ? null : TakeTimeCouponItem.fromJson(json["coupon"]);
  }

  static List<ExpiredCoupon> fromList(List<Map<String, dynamic>> list) {
    return list.map(ExpiredCoupon.fromJson).toList();
  }
}

class TakeTimeCouponItem {
  int? id;
  String? nameUg;
  String? nameZh;
  int? cityId;
  int? areaId;
  int? resId;
  int? type;
  int? typeObjectId;
  dynamic lotteryGroupCouponId;
  int? adminId;
  String? minPrice;
  String? price;
  int? count;
  int? payAmount;
  int? takenCount;
  int? saledCount;
  String? startTime;
  String? endTime;
  String? startUseTime;
  String? endUseTime;
  int? state;
  int? payStatus;
  int? chargeId;
  int? refunded;
  int? percent;
  String? createdAt;
  String? updatedAt;
  dynamic deletedAt;

  TakeTimeCouponItem({this.id, this.nameUg, this.nameZh, this.cityId, this.areaId, this.resId, this.type, this.typeObjectId, this.lotteryGroupCouponId, this.adminId, this.minPrice, this.price, this.count, this.payAmount, this.takenCount, this.saledCount, this.startTime, this.endTime, this.startUseTime, this.endUseTime, this.state, this.payStatus, this.chargeId, this.refunded, this.percent, this.createdAt, this.updatedAt, this.deletedAt});

  TakeTimeCouponItem.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    nameUg = json["name_ug"];
    nameZh = json["name_zh"];
    cityId = json["city_id"];
    areaId = json["area_id"];
    resId = json["res_id"];
    type = json["type"];
    typeObjectId = json["type_object_id"];
    lotteryGroupCouponId = json["lottery_group_coupon_id"];
    adminId = json["admin_id"];
    minPrice = json["min_price"];
    price = json["price"];
    count = json["count"];
    payAmount = json["pay_amount"];
    takenCount = json["taken_count"];
    saledCount = json["saled_count"];
    startTime = json["start_time"];
    endTime = json["end_time"];
    startUseTime = json["start_use_time"];
    endUseTime = json["end_use_time"];
    state = json["state"];
    payStatus = json["pay_status"];
    chargeId = json["charge_id"];
    refunded = json["refunded"];
    percent = json["percent"];
    createdAt = json["created_at"];
    updatedAt = json["updated_at"];
    deletedAt = json["deleted_at"];
  }

  static List<TakeTimeCouponItem> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeCouponItem.fromJson).toList();
  }

}

class TakeTimeShipmentInfo {
  int? canMulazimTake;
  int? canSelfTake;
  int? areaId;
  TakeTimeDistribution? distribution;
  String? priceDiscountImage;
  String? shipmentDiscountImage;

  TakeTimeShipmentInfo({this.canMulazimTake, this.canSelfTake, this.areaId, this.distribution, this.priceDiscountImage, this.shipmentDiscountImage});

  TakeTimeShipmentInfo.fromJson(Map<String, dynamic> json) {
    canMulazimTake = json["can_mulazim_take"];
    canSelfTake = json["can_self_take"];
    areaId = json["area_id"];
    distribution = json["distribution"] == null ? null : TakeTimeDistribution.fromJson(json["distribution"]);
    priceDiscountImage = json["price_discount_image"];
    shipmentDiscountImage = json["shipment_discount_image"];
  }

  static List<TakeTimeShipmentInfo> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeShipmentInfo.fromJson).toList();
  }
}

class TakeTimeDistribution {
  num? shipment;
  num? param;
  num? distance;

  TakeTimeDistribution({this.shipment, this.param, this.distance});

  TakeTimeDistribution.fromJson(Map<String, dynamic> json) {
    shipment = json["shipment"];
    param = json["param"];
    distance = json["distance"];
  }

  static List<TakeTimeDistribution> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeDistribution.fromJson).toList();
  }
}

class TakeTimeCst {
  String? time;
  int? isRealTime;
  String? dateTime;
  String? realBookingTime;
  int? state;
  bool? isToday;
  String? bookingDay;

  TakeTimeCst({this.time, this.isRealTime, this.dateTime, this.realBookingTime, this.state, this.isToday, this.bookingDay});

  TakeTimeCst.fromJson(Map<String, dynamic> json) {
    time = json["time"];
    isRealTime = json["is_real_time"];
    dateTime = json["date_time"];
    realBookingTime = json["real_booking_time"];
    state = json["state"];
    isToday = json["is_today"];
    bookingDay = json["booking_day"];
  }

  static List<TakeTimeCst> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeCst.fromJson).toList();
  }
}

class TakeTimeAddress {
  int? id;
  int? buildingId;
  String? address;
  String? tel;
  String? name;
  num? distance;
  String? buildingName;
  int? addressId;
  bool? showDistanceWarn;
  TakeTimeBuilding? building;

  TakeTimeAddress({this.id, this.buildingId, this.address, this.tel, this.name, this.distance, this.buildingName, this.addressId, this.showDistanceWarn, this.building});

  TakeTimeAddress.fromJson(Map<String, dynamic> json) {
    id = json["id"];
  buildingId = json["building_id"];
    address = json["address"];
    tel = json["tel"];
    name = json["name"];
    distance = json["distance"];
    buildingName = json["building_name"];
    addressId = json["address_id"];
    showDistanceWarn = json["show_distance_warn"];
    building = json["building"] == null ? null : TakeTimeBuilding.fromJson(json["building"]);
  }

  static List<TakeTimeAddress> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeAddress.fromJson).toList();
  }
}

class TakeTimeBuilding {
  int? id;
  int? cityId;
  int? areaId;
  int? streetId;
  int? buildingType;
  String? name;
  String? nameUg;
  String? nameZh;
  String? prefixUg;
  String? prefixZh;
  double? lat;
  double? lng;
  int? weight;
  int? state;
  String? createdAt;
  String? updatedAt;
  dynamic deletedAt;

  TakeTimeBuilding({this.id, this.cityId, this.areaId, this.streetId, this.buildingType, this.name, this.nameUg, this.nameZh, this.prefixUg, this.prefixZh, this.lat, this.lng, this.weight, this.state, this.createdAt, this.updatedAt, this.deletedAt});

  TakeTimeBuilding.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    cityId = json["city_id"];
    areaId = json["area_id"];
    streetId = json["street_id"];
    buildingType = json["building_type"];
    name = json["name"];
    nameUg = json["name_ug"];
    nameZh = json["name_zh"];
    prefixUg = json["prefix_ug"];
    prefixZh = json["prefix_zh"];
    lat = json["lat"];
    lng = json["lng"];
    weight = json["weight"];
    state = json["state"];
    createdAt = json["created_at"];
    updatedAt = json["updated_at"];
    deletedAt = json["deleted_at"];
  }

  static List<TakeTimeBuilding> fromList(List<Map<String, dynamic>> list) {
    return list.map(TakeTimeBuilding.fromJson).toList();
  }
}