class QueryOrderResponseData {
  String? orderId;
  String? tempOrderId;
  int? state;
  String? totalPrice;
  int? paymentMethod;

  QueryOrderResponseData({
    this.orderId,
    this.tempOrderId,
    this.state,
    this.totalPrice,
    this.paymentMethod,
  });

  factory QueryOrderResponseData.fromJson(Map<String, dynamic> json) {
    return QueryOrderResponseData(
      orderId: json['orderId'] as String?,
      tempOrderId: json['tempOrderId'] as String?,
      state: json['state'] as int?,
      totalPrice: json['totalPrice'] as String?,
      paymentMethod: json['paymentMethod'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'tempOrderId': tempOrderId,
      'state': state,
      'totalPrice': totalPrice,
      'paymentMethod': paymentMethod,
    };
  }
} 