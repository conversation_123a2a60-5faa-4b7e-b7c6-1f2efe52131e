class CheckPayResultModel {
  int? status;
  String? msg;
  Data? data;
  String? lang;

  CheckPayResultModel({this.status, this.msg, this.data, this.lang});

  CheckPayResultModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : Data.fromJson(json["data"]);
    lang = json["lang"];
  }
}

class Data {
  int? payState;

  Data({this.payState});

  Data.fromJson(Map<String, dynamic> json) {
    payState = json["pay_state"];
  }
}

/// 现金支付检查响应模型
class CashPayCheckModel {
  int? status;
  String? msg;
  CashPayData? data;

  CashPayCheckModel({this.status, this.msg, this.data});

  CashPayCheckModel.fromJson(Map<String, dynamic> json) {
    status = json["status"];
    msg = json["msg"];
    data = json["data"] == null ? null : CashPayData.fromJson(json["data"]);
  }
}

/// 现金支付数据
class CashPayData {
  /// 是否可以现金支付
  bool? canCashPay;

  CashPayData({this.canCashPay});

  CashPayData.fromJson(Map<String, dynamic> json) {
    canCashPay = json["can_cash_pay"];
  }
}
