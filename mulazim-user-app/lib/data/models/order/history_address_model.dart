class HistoryAddressModel {
  List<HistoryAddressData>? data;
  String? lang;
  String? msg;
  int? status;

  HistoryAddressModel({this.data, this.lang, this.msg, this.status});

  HistoryAddressModel.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null
        ? null
        : (json["data"] as List)
            .map((e) => HistoryAddressData.fromJson(e))
            .toList();
    lang = json["lang"];
    msg = json["msg"];
    status = json["status"];
  }

  static List<HistoryAddressModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(HistoryAddressModel.fromJson).toList();
  }
}

class HistoryAddressData {
  int? id;
  String? name;
  String? tel;
  int? cityId;
  String? cityName;
  int? areaId;
  String? areaName;
  String? servicePhone;
  int? streetId;
  String? streetName;
  int? buildingId;
  String? buildingName;
  String? buildingNameZh;
  String? address;
  num? lat;
  num? lng;
  num? distance;
  bool? showDistanceWarn;
  int? state;

  HistoryAddressData(
      {this.id,
      this.name,
      this.tel,
      this.cityId,
      this.cityName,
      this.areaId,
      this.areaName,
      this.servicePhone,
      this.streetId,
      this.streetName,
      this.buildingId,
      this.buildingName,
      this.buildingNameZh,
      this.address,
      this.lat,
      this.lng,
      this.distance,
      this.showDistanceWarn,
      this.state});

  HistoryAddressData.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    tel = json["tel"];
    cityId = json["city_id"];
    cityName = json["city_name"];
    areaId = json["area_id"];
    areaName = json["area_name"];
    servicePhone = json["service_phone"];
    streetId = json["street_id"];
    streetName = json["street_name"];
    buildingId = json["building_id"];
    buildingName = json["building_name"];
    buildingNameZh = json["building_name_zh"];
    address = json["address"];
    lat = json["lat"];
    lng = json["lng"];
    distance = json["distance"];
    showDistanceWarn = json["show_distance_warn"];
    state = json["state"];
  }

  static List<HistoryAddressData> fromList(List<Map<String, dynamic>> list) {
    return list.map(HistoryAddressData.fromJson).toList();
  }
}
