class CollectStoreList {
  CollectStoreData? data;
  String? lang;
  String? msg;
  int? status;
  String? time;

  CollectStoreList({this.data, this.lang, this.msg, this.status, this.time});

  CollectStoreList.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new CollectStoreData.fromJson(json['data']) : null;
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}

class CollectStoreData {
  int? currentPage;
  List<Items>? items;
  int? perPage;

  CollectStoreData({this.currentPage, this.items, this.perPage});

  CollectStoreData.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(new Items.fromJson(v));
      });
    }
    perPage = json['per_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['per_page'] = this.perPage;
    return data;
  }
}

class Items {
  int? restaurantId;
  String? name;
  String? logo;
  double? starAvg;
  int? categoryId;
  String? categoryName;
  int? resting;
  int? hasFoodPre;
  int? is_new;
  int? mounthOrderCount;
  List<TakeTag>? takeTag;

  Items(
      {this.restaurantId,
      this.name,
      this.logo,
      this.starAvg,
      this.categoryId,
      this.categoryName,
      this.resting,
      this.hasFoodPre,
      this.is_new,
      this.mounthOrderCount,
      this.takeTag});

  Items.fromJson(Map<String, dynamic> json) {
    restaurantId = json['restaurant_id'];
    name = json['name'];
    logo = json['logo'];
    starAvg = json['star_avg']?.toDouble();
    categoryId = json['category_id'];
    categoryName = json['category_name'];
    resting = json['resting'];
    hasFoodPre = json['hasFoodPre'];
    is_new = json['is_new'];
    mounthOrderCount = json['mounth_order_count'];
    if (json['take_tag'] != null) {
      takeTag = <TakeTag>[];
      json['take_tag'].forEach((v) {
        takeTag!.add(new TakeTag.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['restaurant_id'] = this.restaurantId;
    data['name'] = this.name;
    data['logo'] = this.logo;
    data['star_avg'] = this.starAvg;
    data['category_id'] = this.categoryId;
    data['category_name'] = this.categoryName;
    data['resting'] = this.resting;
    data['hasFoodPre'] = this.hasFoodPre;
    data['is_new'] = this.is_new;
    data['mounth_order_count'] = this.mounthOrderCount;
    if (this.takeTag != null) {
      data['take_tag'] = this.takeTag!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TakeTag {
  String? title;
  String? color;
  String? background;
  String? borderColor;

  TakeTag({this.title, this.color, this.background, this.borderColor});

  TakeTag.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    color = json['color'];
    background = json['background'];
    borderColor = json['border_color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['color'] = this.color;
    data['background'] = this.background;
    data['border_color'] = this.borderColor;
    return data;
  }
}
