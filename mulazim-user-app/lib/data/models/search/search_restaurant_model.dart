class SearchRestaurantModel {
  SearchRestaurantData? data;
  String? msg;
  int? status;

  SearchRestaurantModel({this.data, this.msg, this.status});

  SearchRestaurantModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new SearchRestaurantData.fromJson(json['data']) : null;
    msg = json['msg'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['msg'] = this.msg;
    data['status'] = this.status;
    return data;
  }
}

class SearchRestaurantData {
  SearchData? searchData;

  SearchRestaurantData({this.searchData});

  SearchRestaurantData.fromJson(Map<String, dynamic> json) {
    searchData = json['search_data'] != null
        ? new SearchData.fromJson(json['search_data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.searchData != null) {
      data['search_data'] = this.searchData!.toJson();
    }
    return data;
  }
}

class SearchData {
  int? currentPage;
  List<SearchedRestaurantItems>? items;
  int? perPage;

  SearchData({this.currentPage, this.items, this.perPage});

  SearchData.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    if (json['items'] != null) {
      items = <SearchedRestaurantItems>[];
      json['items'].forEach((v) {
        items!.add(new SearchedRestaurantItems.fromJson(v));
      });
    }
    perPage = json['per_page'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    if (this.items != null) {
      data['items'] = this.items!.map((v) => v.toJson()).toList();
    }
    data['per_page'] = this.perPage;
    return data;
  }
}

class SearchedRestaurantItems {
  String? address;
  String? categoryId;
  String? description;
  List<SearchedRestaurantFoods>? foods;
  int? id;
  String? logo;
  int? monthOrderCount;
  String? name;
  num? starAvg;

  SearchedRestaurantItems(
      {this.address,
        this.categoryId,
        this.description,
        this.foods,
        this.id,
        this.logo,
        this.monthOrderCount,
        this.name,
        this.starAvg});

  SearchedRestaurantItems.fromJson(Map<String, dynamic> json) {
    address = json['address'];
    categoryId = json['category_id'];
    description = json['description'];
    if (json['foods'] != null) {
      foods = <SearchedRestaurantFoods>[];
      json['foods'].forEach((v) {
        foods!.add(new SearchedRestaurantFoods.fromJson(v));
      });
    }
    id = json['id'];
    logo = json['logo'];
    monthOrderCount = json['month_order_count'];
    name = json['name'];
    starAvg = json['star_avg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['address'] = this.address;
    data['category_id'] = this.categoryId;
    data['description'] = this.description;
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    data['id'] = this.id;
    data['logo'] = this.logo;
    data['month_order_count'] = this.monthOrderCount;
    data['name'] = this.name;
    data['star_avg'] = this.starAvg;
    return data;
  }
}

class SearchedRestaurantFoods {
  int? id;
  String? image;
  int? monthOrderCount;
  String? name;
  num? price;
  num? starAvg;

  SearchedRestaurantFoods(
      {this.id,
        this.image,
        this.monthOrderCount,
        this.name,
        this.price,
        this.starAvg});

  SearchedRestaurantFoods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    image = json['image'];
    monthOrderCount = json['month_order_count'];
    name = json['name'];
    price = json['price'];
    starAvg = json['star_avg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['image'] = this.image;
    data['month_order_count'] = this.monthOrderCount;
    data['name'] = this.name;
    data['price'] = this.price;
    data['star_avg'] = this.starAvg;
    return data;
  }
}