class SearchWordModel {
  SearchWordData? data;
  String? language;
  String? msg;
  int? status;

  SearchWordModel({this.data, this.language, this.msg, this.status});

  SearchWordModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? new SearchWordData.fromJson(json['data']) : null;
    language = json['language'];
    msg = json['msg'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['language'] = this.language;
    data['msg'] = this.msg;
    data['status'] = this.status;
    return data;
  }
}

class SearchWordData {
  int? currentPage;
  int? perPage;
  List<String>? words;

  SearchWordData({this.currentPage, this.perPage, this.words});

  SearchWordData.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    perPage = json['per_page'];
    words = json['words'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    data['per_page'] = this.perPage;
    data['words'] = this.words;
    return data;
  }
}