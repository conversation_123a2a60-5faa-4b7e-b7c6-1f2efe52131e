class SearchPageModel {
  int? status;
  String? msg;
  SearchPageData? data;
  String? lang;

  SearchPageModel({this.status, this.msg, this.data, this.lang});

  SearchPageModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    msg = json['msg'];
    data = json['data'] != null ? new SearchPageData.fromJson(json['data']) : null;
    lang = json['lang'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['lang'] = this.lang;
    return data;
  }
}

class SearchPageData {
  List<SearchPageRestaurants>? restaurants;
  List<SearchPageFoods>? foods;

  SearchPageData({this.restaurants, this.foods});

  SearchPageData.fromJson(Map<String, dynamic> json) {
    if (json['restaurants'] != null) {
      restaurants = <SearchPageRestaurants>[];
      json['restaurants'].forEach((v) {
        restaurants!.add(new SearchPageRestaurants.fromJson(v));
      });
    }
    if (json['foods'] != null) {
      foods = <SearchPageFoods>[];
      json['foods'].forEach((v) {
        foods!.add(new SearchPageFoods.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.restaurants != null) {
      data['restaurants'] = this.restaurants!.map((v) => v.toJson()).toList();
    }
    if (this.foods != null) {
      data['foods'] = this.foods!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SearchPageRestaurants {
  int? id;
  String? name;
  double? starAvg;
  String? logo;
  int? monthOrderCount;
  int? commentCount;
  int? canSelfTake;
  int? canMulazimTake;
  List<TakeTag>? takeTag;
  String? distance;

  SearchPageRestaurants(
      {this.id,
        this.name,
        this.starAvg,
        this.logo,
        this.monthOrderCount,
        this.commentCount,
        this.canSelfTake,
        this.canMulazimTake,
        this.takeTag,
        this.distance});

  SearchPageRestaurants.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    starAvg = json['star_avg'];
    logo = json['logo'];
    monthOrderCount = json['month_order_count'];
    commentCount = json['comment_count'];
    canSelfTake = json['can_self_take'];
    canMulazimTake = json['can_mulazim_take'];
    if (json['take_tag'] != null) {
      takeTag = <TakeTag>[];
      json['take_tag'].forEach((v) {
        takeTag!.add(new TakeTag.fromJson(v));
      });
    }
    distance = json['distance'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['star_avg'] = this.starAvg;
    data['logo'] = this.logo;
    data['month_order_count'] = this.monthOrderCount;
    data['comment_count'] = this.commentCount;
    data['can_self_take'] = this.canSelfTake;
    data['can_mulazim_take'] = this.canMulazimTake;
    if (this.takeTag != null) {
      data['take_tag'] = this.takeTag!.map((v) => v.toJson()).toList();
    }
    data['distance'] = this.distance;
    return data;
  }
}

class TakeTag {
  String? title;
  String? color;
  String? background;
  String? borderColor;

  TakeTag({this.title, this.color, this.background, this.borderColor});

  TakeTag.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    color = json['color'];
    background = json['background'];
    borderColor = json['border_color'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['color'] = this.color;
    data['background'] = this.background;
    data['border_color'] = this.borderColor;
    return data;
  }
}

class SearchPageFoods {
  int? id;
  String? name;
  int? restaurantId;

  SearchPageFoods({this.id, this.name, this.restaurantId});

  SearchPageFoods.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    restaurantId = json['restaurant_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['restaurant_id'] = this.restaurantId;
    return data;
  }
}