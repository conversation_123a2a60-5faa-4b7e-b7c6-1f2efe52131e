/// 抽奖详情模型
class LotteryDetailModel {
  final int id;
  final int state;
  final String stateName;
  final TableInfo tableInfo;
  final List<LotteryPrizeItem> prizeItems;

  LotteryDetailModel({
    required this.id,
    required this.state,
    required this.stateName,
    required this.tableInfo,
    required this.prizeItems,
  });

  factory LotteryDetailModel.fromJson(Map<String, dynamic> json) {
    return LotteryDetailModel(
      id: json['id'] as int? ?? 0,
      state: json['state'] as int? ?? 0,
      stateName: json['state_name'] as String? ?? '',
      tableInfo:
          TableInfo.fromJson(json['table_info'] as Map<String, dynamic>? ?? {}),
      prizeItems: (json['prize_items'] as List<dynamic>?)
              ?.map((e) => LotteryPrizeItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

/// 转盘信息
class TableInfo {
  final int canTurnCount;
  final List<int> canTurnItems;
  final String bgColor;
  final List<TurntableItem> items;

  TableInfo({
    required this.canTurnCount,
    required this.canTurnItems,
    required this.bgColor,
    required this.items,
  });

  factory TableInfo.fromJson(Map<String, dynamic> json) {
    return TableInfo(
      canTurnCount: json['can_turn_count'] as int? ?? 0,
      canTurnItems: (json['can_turn_items'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          [],
      bgColor: json['bg_color'] as String? ?? '#FC2E45',
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => TurntableItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

/// 转盘项目
class TurntableItem {
  final int id;
  final String name;
  final String image;
  final String fontSize;
  final String fontColor;
  final String bgColor;

  TurntableItem({
    required this.id,
    required this.name,
    required this.image,
    required this.fontSize,
    required this.fontColor,
    required this.bgColor,
  });

  factory TurntableItem.fromJson(Map<String, dynamic> json) {
    return TurntableItem(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      image: json['image'] as String? ?? '',
      fontSize: json['font_size'] as String? ?? '14px',
      fontColor: json['font_color'] as String? ?? '#ffffff',
      bgColor: json['bg_color'] as String? ?? '#FC2E45',
    );
  }
}

/// 抽奖奖品项
class LotteryPrizeItem {
  final int id;
  final String name;
  final String image;
  final double probability;

  LotteryPrizeItem({
    required this.id,
    required this.name,
    required this.image,
    required this.probability,
  });

  factory LotteryPrizeItem.fromJson(Map<String, dynamic> json) {
    return LotteryPrizeItem(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      image: json['image'] as String? ?? '',
      probability: (json['probability'] as num?)?.toDouble() ?? 0.0,
    );
  }
}
