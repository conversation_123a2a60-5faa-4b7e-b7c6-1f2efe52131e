/// 规格相关数据模型

/// 规格组模型
class SpecGroup {
  /// 规格组ID
  final int? id;

  /// 规格组名称
  final String? name;

  /// 规格选项列表 (微信小程序中为spec_options)
  final List<SpecOption>? specOptions;

  /// 是否必选
  final bool? required;

  /// 最小选择数量
  final int? minSelect;

  /// 最大选择数量
  final int? maxSelect;

  /// 状态 (API返回state字段)
  final int? state;

  /// 价格类型 (API返回price_type字段)
  final int? priceType;

  const SpecGroup({
    this.id,
    this.name,
    this.specOptions,
    this.required,
    this.minSelect,
    this.maxSelect,
    this.state,
    this.priceType,
  });

  /// 从JSON创建对象
  factory SpecGroup.fromJson(Map<String, dynamic> json) {
    return SpecGroup(
      id: json['id'],
      name: json['name'],
      specOptions: json['spec_options'] != null
          ? (json['spec_options'] as List)
              .map((item) => SpecOption.fromJson(item))
              .toList()
          : null,
      required: json['required'],
      minSelect: json['min_select'],
      maxSelect: json['max_select'],
      state: json['state'],
      priceType: json['price_type'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'spec_options': specOptions?.map((item) => item.toJson()).toList(),
      'required': required,
      'min_select': minSelect,
      'max_select': maxSelect,
      'state': state,
      'price_type': priceType,
    };
  }

  /// 拷贝方法
  SpecGroup copyWith({
    int? id,
    String? name,
    List<SpecOption>? specOptions,
    bool? required,
    int? minSelect,
    int? maxSelect,
    int? state,
    int? priceType,
  }) {
    return SpecGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      specOptions: specOptions ?? this.specOptions,
      required: required ?? this.required,
      minSelect: minSelect ?? this.minSelect,
      maxSelect: maxSelect ?? this.maxSelect,
      state: state ?? this.state,
      priceType: priceType ?? this.priceType,
    );
  }
}

/// 规格选项模型
class SpecOption {
  /// 选项ID
  final int? id;

  /// 选项名称
  final String? name;

  /// 选项价格（相对于基础价格的增减）
  final double? price;

  /// 是否选中 (微信小程序中的is_selected)
  final bool? isSelected;

  /// 数量 (微信小程序中的count)
  final int? count;

  /// 是否可选择
  final bool? enabled;

  /// 状态 (API返回state字段)
  final int? state;

  /// 排序
  final int? sort;

  const SpecOption({
    this.id,
    this.name,
    this.price,
    this.isSelected,
    this.count,
    this.enabled,
    this.state,
    this.sort,
  });

  /// 从JSON创建对象
  factory SpecOption.fromJson(Map<String, dynamic> json) {
    return SpecOption(
      id: json['id'],
      name: json['name'],
      price: json['price'] != null
          ? (json['price'] as num).toDouble() / 100
          : null,
      isSelected: json['is_selected'] == 1,
      count: json['count'],
      enabled: json['enabled'],
      state: json['state'],
      sort: json['sort'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'is_selected': isSelected == true ? 1 : 0,
      'count': count,
      'enabled': enabled,
      'state': state,
      'sort': sort,
    };
  }

  /// 拷贝方法
  SpecOption copyWith({
    int? id,
    String? name,
    double? price,
    bool? isSelected,
    int? count,
    bool? enabled,
    int? state,
    int? sort,
  }) {
    return SpecOption(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      isSelected: isSelected ?? this.isSelected,
      count: count ?? this.count,
      enabled: enabled ?? this.enabled,
      state: state ?? this.state,
      sort: sort ?? this.sort,
    );
  }
}

/// 美食规格数据模型 (对应微信小程序API返回结构)
class FoodWithSpec {
  /// 美食ID (API返回food_id)
  final int? foodId;

  /// 规格组列表 (微信小程序中为specs)
  final List<SpecGroup>? specs;

  /// 秒杀活动列表
  final List<SeckillActivity>? seckill;

  /// 优惠活动列表
  final List<PrefActivity>? pref;

  /// 满减活动列表
  final List<MarketActivity>? market;

  const FoodWithSpec({
    this.foodId,
    this.specs,
    this.seckill,
    this.pref,
    this.market,
  });

  /// 从JSON创建对象
  factory FoodWithSpec.fromJson(Map<String, dynamic> json) {
    return FoodWithSpec(
      foodId: json['food_id'],
      specs: json['specs'] != null
          ? (json['specs'] as List)
              .map((item) => SpecGroup.fromJson(item))
              .toList()
          : null,
      seckill: json['seckill'] != null
          ? (json['seckill'] as List)
              .map((item) => SeckillActivity.fromJson(item))
              .toList()
          : null,
      pref: json['pref'] != null
          ? (json['pref'] as List)
              .map((item) => PrefActivity.fromJson(item))
              .toList()
          : null,
      market: json['market'] != null
          ? (json['market'] as List)
              .map((item) => MarketActivity.fromJson(item))
              .toList()
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'food_id': foodId,
      'specs': specs?.map((item) => item.toJson()).toList(),
      'seckill': seckill?.map((item) => item.toJson()).toList(),
      'pref': pref?.map((item) => item.toJson()).toList(),
      'market': market?.map((item) => item.toJson()).toList(),
    };
  }
}

/// 规格数据响应模型
class SpecDataResponse {
  /// 规格数据列表 (API返回的是数组)
  final List<FoodWithSpec>? data;

  const SpecDataResponse({
    this.data,
  });

  /// 从JSON创建对象
  factory SpecDataResponse.fromJson(List<dynamic> json) {
    return SpecDataResponse(
      data: json.map((item) => FoodWithSpec.fromJson(item)).toList(),
    );
  }
}

/// 已选规格模型
class SelectedSpec {
  /// 规格组ID
  final int groupId;

  /// 规格选项ID
  final int optionId;

  /// 规格组名称
  final String groupName;

  /// 规格选项名称
  final String optionName;

  /// 规格价格
  final double price;

  const SelectedSpec({
    required this.groupId,
    required this.optionId,
    required this.groupName,
    required this.optionName,
    required this.price,
  });

  /// 从JSON创建对象
  factory SelectedSpec.fromJson(Map<String, dynamic> json) {
    return SelectedSpec(
      groupId: json['group_id'],
      optionId: json['option_id'],
      groupName: json['group_name'],
      optionName: json['option_name'],
      price:
          json['price'] != null ? (json['price'] as num).toDouble() / 100 : 0.0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'group_id': groupId,
      'option_id': optionId,
      'group_name': groupName,
      'option_name': optionName,
      'price': price,
    };
  }

  /// 拷贝方法
  SelectedSpec copyWith({
    int? groupId,
    int? optionId,
    String? groupName,
    String? optionName,
    double? price,
  }) {
    return SelectedSpec(
      groupId: groupId ?? this.groupId,
      optionId: optionId ?? this.optionId,
      groupName: groupName ?? this.groupName,
      optionName: optionName ?? this.optionName,
      price: price ?? this.price,
    );
  }
}

/// 秒杀活动模型
class SeckillActivity {
  /// 秒杀ID
  final int? seckillId;

  /// 秒杀状态 (1-活跃，0-未开始)
  final int? seckillActive;

  /// 秒杀价格
  final double? price;

  /// 用户最大订购数量
  final int? userMaxOrderCount;

  /// 匹配的规格选项ID列表
  final List<int>? optionIds;

  const SeckillActivity({
    this.seckillId,
    this.seckillActive,
    this.price,
    this.userMaxOrderCount,
    this.optionIds,
  });

  /// 从JSON创建对象
  factory SeckillActivity.fromJson(Map<String, dynamic> json) {
    return SeckillActivity(
      seckillId: json['seckill_id'],
      seckillActive: json['seckill_active'],
      price: json['price'] != null
          ? (json['price'] as num).toDouble() / 100
          : null,
      userMaxOrderCount: json['user_max_order_count'],
      optionIds: json['option_ids'] != null
          ? List<int>.from(json['option_ids'].map((x) => x))
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'seckill_id': seckillId,
      'seckill_active': seckillActive,
      'price': price,
      'user_max_order_count': userMaxOrderCount,
      'option_ids': optionIds,
    };
  }
}

/// 优惠活动模型
class PrefActivity {
  /// 优惠ID
  final int? preferentialId;

  /// 优惠价格
  final double? discountPrice;

  /// 最大订购数量
  final int? maxOrderCount;

  /// 匹配的规格选项ID列表
  final List<int>? optionIds;

  const PrefActivity({
    this.preferentialId,
    this.discountPrice,
    this.maxOrderCount,
    this.optionIds,
  });

  /// 从JSON创建对象
  factory PrefActivity.fromJson(Map<String, dynamic> json) {
    return PrefActivity(
      preferentialId: json['preferential_id'],
      discountPrice: json['discount_price'] != null
          ? (json['discount_price'] as num).toDouble() / 100
          : null,
      maxOrderCount: json['max_order_count'],
      optionIds: json['option_ids'] != null
          ? List<int>.from(json['option_ids'].map((x) => x))
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'preferential_id': preferentialId,
      'discount_price': discountPrice,
      'max_order_count': maxOrderCount,
      'option_ids': optionIds,
    };
  }
}

/// 满减活动模型
class MarketActivity {
  /// 满减ID
  final int? marketId;

  /// 满减标签
  final List<dynamic>? reductionFoodsTags;

  /// 满减步骤
  final List<dynamic>? steps;

  /// 匹配的规格选项ID列表
  final List<int>? optionIds;

  const MarketActivity({
    this.marketId,
    this.reductionFoodsTags,
    this.steps,
    this.optionIds,
  });

  /// 从JSON创建对象
  factory MarketActivity.fromJson(Map<String, dynamic> json) {
    return MarketActivity(
      marketId: json['market_id'],
      reductionFoodsTags: json['reduction_foods_tags'],
      steps: json['steps'],
      optionIds: json['option_ids'] != null
          ? List<int>.from(json['option_ids'].map((x) => x))
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'market_id': marketId,
      'reduction_foods_tags': reductionFoodsTags,
      'steps': steps,
      'option_ids': optionIds,
    };
  }
}
