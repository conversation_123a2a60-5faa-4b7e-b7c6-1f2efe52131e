/// 聊天列表响应模型
class ChatListResponse {
  final int status;
  final String? msg;
  final List<ChatItem>? data;

  /// 构造函数
  ChatListResponse({
    required this.status,
    this.msg,
    this.data,
  });

  /// 从JSON创建
  factory ChatListResponse.fromJson(Map<String, dynamic> json) {
    return ChatListResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: json['data'] != null
          ? List<ChatItem>.from(json['data'].map((x) => ChatItem.fromJson(x)))
          : null,
    );
  }
}

/// 聊天列表项
class ChatItem {
  final String orderId;
  final String restaurantName;
  final String restaurantImage;
  final int msgCount;
  final String msgTime;
  final String newMsg;

  /// 构造函数
  ChatItem({
    required this.orderId,
    required this.restaurantName,
    required this.restaurantImage,
    required this.msgCount,
    required this.msgTime,
    required this.newMsg,
  });

  /// 从JSON创建
  factory ChatItem.fromJson(Map<String, dynamic> json) {
    return ChatItem(
      orderId: json['order_id']?.toString() ?? '',
      restaurantName: json['restaurant_name'] ?? '',
      restaurantImage: json['restaurant_image'] ?? '',
      msgCount: json['msg_count'] ?? 0,
      msgTime: json['msg_time'] ?? '',
      newMsg: json['new_msg'] ?? '',
    );
  }
}

/// 聊天室详情响应模型
class ChatRoomResponse {
  final int status;
  final String? msg;
  final ChatRoomData? data;

  /// 构造函数
  ChatRoomResponse({
    required this.status,
    this.msg,
    this.data,
  });

  /// 从JSON创建
  factory ChatRoomResponse.fromJson(Map<String, dynamic> json) {
    return ChatRoomResponse(
      status: json['status'] ?? 0,
      msg: json['msg'],
      data: json['data'] != null ? ChatRoomData.fromJson(json['data']) : null,
    );
  }
}

/// 聊天室数据模型
class ChatRoomData {
  final List<ChatMessage> messages;

  /// 构造函数
  ChatRoomData({
    required this.messages,
  });

  /// 从JSON创建
  factory ChatRoomData.fromJson(Map<String, dynamic> json) {
    return ChatRoomData(
      messages: json['messages'] != null
          ? List<ChatMessage>.from(
              json['messages'].map((x) => ChatMessage.fromJson(x)))
          : [],
    );
  }
}

/// 聊天消息模型
class ChatMessage {
  final String? id;
  final String orderId;
  final String msgDatetime;
  final String type;
  String content;
  final String avatar;
  String? cardContent;
  final int cardType;
  final int? rule;
  String? relativeTime;

  /// 构造函数
  ChatMessage({
    this.id,
    required this.orderId,
    required this.msgDatetime,
    required this.type,
    required this.content,
    required this.avatar,
    this.cardContent,
    required this.cardType,
    this.rule,
    this.relativeTime,
  });

  /// 从JSON创建
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id']?.toString(),
      orderId: json['order_id']?.toString() ?? '',
      msgDatetime: json['msg_datetime'] ?? '',
      type: json['type'] ?? '',
      content: json['content'] ?? '',
      avatar: json['avatar'] ?? '',
      cardContent: json['card_content']?.toString(),
      cardType: json['card_type'] ?? 0,
      rule: json['rule'],
      relativeTime: json['relativeTime'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'msg_datetime': msgDatetime,
      'type': type,
      'content': content,
      'avatar': avatar,
      'card_content': cardContent,
      'card_type': cardType,
      'rule': rule,
      'relativeTime': relativeTime,
    };
  }
}
