class ShipperDetailModel {
  final String? shipperName;
  final String? areaName;
  final String? shipperAvatar;
  final String? shipperMobile;
  final int? id;
  final double? shipperDistance;
  final int? shipperOnTimeDeliveryRate;
  final int? shipperCustomerRate;
  final int? shipperDeliveryAvgTime;
  final List<ShipperTipsModel>? shipperTips;
  final int? shipperTipsCount;
  final List<CommentModel>? comments;

  ShipperDetailModel({
    this.shipperName,
    this.areaName,
    this.shipperAvatar,
    this.shipperMobile,
    this.id,
    this.shipperDistance,
    this.shipperOnTimeDeliveryRate,
    this.shipperCustomerRate,
    this.shipperDeliveryAvgTime,
    this.shipperTips,
    this.shipperTipsCount,
    this.comments,
  });

  factory ShipperDetailModel.fromJson(Map<String, dynamic> json) {
    return ShipperDetailModel(
      shipperName: json['shipper_name'],
      areaName: json['area_name'],
      shipperAvatar: json['shipper_avatar'],
      shipperMobile: json['shipper_mobile'],
      id: json['id'],
      shipperDistance: (json['shipper_distance'] ?? 0).toDouble(),
      shipperOnTimeDeliveryRate: json['shipper_on_time_delivery_rate'],
      shipperCustomerRate: json['shipper_customer_rate'],
      shipperDeliveryAvgTime: json['shipper_delivery_avg_time'],
      shipperTipsCount: json['shipper_tips_count'],
      shipperTips: json['shipper_tips'] != null
          ? List<ShipperTipsModel>.from(
              json['shipper_tips'].map((x) => ShipperTipsModel.fromJson(x)))
          : null,
      comments: json['comments'] != null
          ? List<CommentModel>.from(
              json['comments'].map((x) => CommentModel.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['shipper_name'] = shipperName;
    data['area_name'] = areaName;
    data['shipper_avatar'] = shipperAvatar;
    data['shipper_mobile'] = shipperMobile;
    data['id'] = id;
    data['shipper_distance'] = shipperDistance;
    data['shipper_on_time_delivery_rate'] = shipperOnTimeDeliveryRate;
    data['shipper_customer_rate'] = shipperCustomerRate;
    data['shipper_delivery_avg_time'] = shipperDeliveryAvgTime;
    data['shipper_tips_count'] = shipperTipsCount;
    if (shipperTips != null) {
      data['shipper_tips'] = shipperTips!.map((v) => v.toJson()).toList();
    }
    if (comments != null) {
      data['comments'] = comments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ShipperTipsModel {
  final int? id;
  final String? userAvatar;
  final String? userName;
  final double? amount;

  ShipperTipsModel({
    this.id,
    this.userAvatar,
    this.userName,
    this.amount,
  });

  factory ShipperTipsModel.fromJson(Map<String, dynamic> json) {
    return ShipperTipsModel(
      id: json['id'],
      userAvatar: json['user_avatar'],
      userName: json['user_name'],
      amount: (json['amount'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_avatar'] = userAvatar;
    data['user_name'] = userName;
    data['amount'] = amount;
    return data;
  }
}

class CommentModel {
  final int? id;
  final String? userAvatar;
  final String? userName;
  final String? createdAt;
  final int? star;
  final int? foodStar;
  final int? boxStar;
  final String? text;
  final bool? hideBorder;
  final List<String>? images;
  final List<ReplyModel>? replies;
  final String? foodName;
  final int? status;

  CommentModel({
    this.id,
    this.userAvatar,
    this.userName,
    this.createdAt,
    this.star,
    this.foodStar,
    this.boxStar,
    this.text,
    this.hideBorder,
    this.images,
    this.replies,
    this.foodName,
    this.status,
  });

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json['id'],
      userAvatar: json['user_avatar'],
      userName: json['user_name'],
      createdAt: json['created_at'],
      star: json['star'],
      foodStar: json['food_star'],
      boxStar: json['box_star'],
      text: json['text'],
      hideBorder: json['hideBorder'] ?? false,
      images: json['images'] != null ? List<String>.from(json['images']) : null,
      replies: json['replies'] != null
          ? List<ReplyModel>.from(
              json['replies'].map((x) => ReplyModel.fromJson(x)))
          : null,
      foodName: json['food_name'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_avatar'] = userAvatar;
    data['user_name'] = userName;
    data['created_at'] = createdAt;
    data['star'] = star;
    data['food_star'] = foodStar;
    data['box_star'] = boxStar;
    data['text'] = text;
    data['hideBorder'] = hideBorder;
    data['food_name'] = foodName;
    data['status'] = status;
    if (images != null) {
      data['images'] = images;
    }
    if (replies != null) {
      data['replies'] = replies!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ReplyModel {
  final int? id;
  final int? type;
  final String? text;
  final String? createdAt;

  ReplyModel({
    this.id,
    this.type,
    this.text,
    this.createdAt,
  });

  factory ReplyModel.fromJson(Map<String, dynamic> json) {
    return ReplyModel(
      id: json['id'],
      type: json['type'],
      text: json['text'],
      createdAt: json['created_at'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['type'] = type;
    data['text'] = text;
    data['created_at'] = createdAt;
    return data;
  }
}
