/// 配送员打赏历史模型
class ShipperTipsHistoryModel {
  /// 配送员ID
  final int? shipperId;

  /// 配送员名称
  final String? shipperName;

  /// 配送员头像
  final String? shipperAvatar;

  /// 打赏总额
  final double? tipsTotal;

  /// 打赏总数
  final int? tipsCount;

  /// 打赏列表
  final List<ShipperTipItemModel>? tipsList;

  /// 构造函数
  ShipperTipsHistoryModel({
    this.shipperId,
    this.shipperName,
    this.shipperAvatar,
    this.tipsTotal,
    this.tipsCount,
    this.tipsList,
  });

  /// 从JSON创建模型
  factory ShipperTipsHistoryModel.fromJson(Map<String, dynamic> json) {
    return ShipperTipsHistoryModel(
      shipperId: json['shipper_id'],
      shipperName: json['shipper_name'],
      shipperAvatar: json['shipper_avatar'],
      tipsTotal: (json['tips_total'] ?? 0).toDouble(),
      tipsCount: json['tips_count'],
      tipsList: json['tips_list'] != null
          ? List<ShipperTipItemModel>.from(
              json['tips_list'].map((x) => ShipperTipItemModel.fromJson(x)))
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['shipper_id'] = shipperId;
    data['shipper_name'] = shipperName;
    data['shipper_avatar'] = shipperAvatar;
    data['tips_total'] = tipsTotal;
    data['tips_count'] = tipsCount;
    if (tipsList != null) {
      data['tips_list'] = tipsList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

/// 配送员打赏项模型
class ShipperTipItemModel {
  /// ID
  final int? id;

  /// 用户ID
  final int? userId;

  /// 用户名称
  final String? userName;

  /// 用户头像
  final String? userAvatar;

  /// 打赏金额
  final double? amount;

  /// 创建时间
  final String? createdAt;

  /// 构造函数
  ShipperTipItemModel({
    this.id,
    this.userId,
    this.userName,
    this.userAvatar,
    this.amount,
    this.createdAt,
  });

  /// 从JSON创建模型
  factory ShipperTipItemModel.fromJson(Map<String, dynamic> json) {
    return ShipperTipItemModel(
      id: json['id'],
      userId: json['user_id'],
      userName: json['user_name'],
      userAvatar: json['user_avatar'],
      amount: (json['amount'] ?? 0).toDouble(),
      createdAt: json['created_at'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['user_name'] = userName;
    data['user_avatar'] = userAvatar;
    data['amount'] = amount;
    data['created_at'] = createdAt;
    return data;
  }
}
