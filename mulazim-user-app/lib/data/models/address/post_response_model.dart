class PostResponseModel {
  String? lang;
  String? msg;
  int? status;
  String? time;

  PostResponseModel({this.lang, this.msg, this.status, this.time});

  PostResponseModel.fromJson(Map<String, dynamic> json) {
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
    time = json['time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    data['time'] = this.time;
    return data;
  }
}