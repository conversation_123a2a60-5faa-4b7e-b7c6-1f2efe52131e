class CityAreaModel {
  List<CityAreaData>? data;
  String? lang;
  String? msg;
  int? status;

  CityAreaModel({this.data, this.lang, this.msg, this.status});

  CityAreaModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <CityAreaData>[];
      json['data'].forEach((v) {
        data!.add(new CityAreaData.fromJson(v));
      });
    }
    lang = json['lang'];
    msg = json['msg'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['lang'] = this.lang;
    data['msg'] = this.msg;
    data['status'] = this.status;
    return data;
  }
}

class CityAreaData {
  int? id;
  String? name;
  String? nameUg;
  String? nameZh;
  int? state;
  List<CityArea>? area;

  CityAreaData({this.id, this.name, this.nameUg, this.nameZh, this.state, this.area});

  CityAreaData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameUg = json['name_ug'];
    nameZh = json['name_zh'];
    state = json['state'];
    if (json['area'] != null) {
      area = <CityArea>[];
      json['area'].forEach((v) {
        area!.add(new CityArea.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['name_ug'] = this.nameUg;
    data['name_zh'] = this.nameZh;
    data['state'] = this.state;
    if (this.area != null) {
      data['area'] = this.area!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CityArea {
  int? advancePayment;
  int? agentFee;
  int? cityId;
  int? id;
  String? name;
  String? nameUg;
  String? nameZh;
  String? servicePhone;
  int? state;

  CityArea(
      {this.advancePayment,
        this.agentFee,
        this.cityId,
        this.id,
        this.name,
        this.nameUg,
        this.nameZh,
        this.servicePhone,
        this.state});

  CityArea.fromJson(Map<String, dynamic> json) {
    advancePayment = json['advance_payment'];
    agentFee = json['agent_fee'];
    cityId = json['city_id'];
    id = json['id'];
    name = json['name'];
    nameUg = json['name_ug'];
    nameZh = json['name_zh'];
    servicePhone = json['service_phone'];
    state = json['state'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['advance_payment'] = this.advancePayment;
    data['agent_fee'] = this.agentFee;
    data['city_id'] = this.cityId;
    data['id'] = this.id;
    data['name'] = this.name;
    data['name_ug'] = this.nameUg;
    data['name_zh'] = this.nameZh;
    data['service_phone'] = this.servicePhone;
    data['state'] = this.state;
    return data;
  }
}