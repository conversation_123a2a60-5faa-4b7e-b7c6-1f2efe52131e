// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_storage_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localStorageRepositoryHash() =>
    r'fb4c607f951533551040ab3e85618a596c0b76c2';

/// 本地存储库提供器
///
/// Copied from [localStorageRepository].
@ProviderFor(localStorageRepository)
final localStorageRepositoryProvider =
    AutoDisposeProvider<LocalStorageRepository>.internal(
  localStorageRepository,
  name: r'localStorageRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localStorageRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalStorageRepositoryRef
    = AutoDisposeProviderRef<LocalStorageRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
