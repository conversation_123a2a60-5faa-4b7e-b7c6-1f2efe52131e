import 'package:user_app/data/models/address/add_address_response_model.dart';
import 'package:user_app/data/models/address/city_area_model.dart';
import 'package:user_app/data/models/address/location_list_model.dart';
import 'package:user_app/data/models/address/park_list_model.dart';
import 'package:user_app/data/models/address/street_list_model.dart';
import 'package:user_app/data/models/order/history_address_model.dart';

import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';

/// 地址仓库
///
/// 负责处理与用户地址相关的API请求，包括获取历史地址、添加/更新/删除地址、
/// 获取城市/街道/小区数据等。提供统一的数据访问接口，返回类型安全的ApiResult。
class AddressRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  AddressRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取用户历史地址
  ///
  /// 返回用户历史使用过的地址列表
  Future<ApiResult<List<HistoryAddressData>>> getHistoryAddress({Map<String, dynamic>? params}) async {
    return await _apiClient.get(
      Api.address.history,
      params: params,
      fromJson: (final response, final data) {
        final model = HistoryAddressModel.fromJson(response);
        return model.data ?? [];
      },
    );
  }

  /// 删除地址
  ///
  /// [param] - 请求参数，包含要删除的地址ID
  ///
  /// 返回删除操作的结果
  Future<ApiResult> deleteAddress(final Map<String, dynamic> param) async {
    return await _apiClient.post(
      Api.address.deleteAddress,
      data: param,
      fromJson: (final response, final data) =>
          data,
    );
  }

  /// 更新地址
  ///
  /// [param] - 地址信息，包含地址ID和需要更新的字段
  ///
  /// 返回更新操作的结果
  Future<ApiResult<AddAddressData>> updateAddress(
      final Map<String, dynamic> param) async {
    return await _apiClient.post(
      Api.address.updateAddress,
      data: param,
      fromJson: (final response, final data) => AddAddressData.fromJson(data),
    );
  }

  /// 根据定位获取地址列表
  ///
  /// [param] - 定位参数，包含经纬度等信息
  ///
  /// 返回附近的地址列表
  Future<ApiResult<LocationListData>> getListByLocation(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.address.listByLocation,
      params: param,
      fromJson: (final response, final data) => LocationListData.fromJson(data),
    );
  }

  /// 根据输入内容获取地址列表
  ///
  /// [param] - 搜索参数，包含搜索关键词等
  ///
  /// 返回匹配的地址列表
  Future<ApiResult<LocationListData>> getListByInput(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.address.listByInput,
      params: param,
      fromJson: (final response, final data) => LocationListData.fromJson(data),
    );
  }

  /// 获取城市区域列表
  ///
  /// 返回支持的城市和区域信息
  Future<ApiResult<List<CityAreaData>?>> getCityArea() async {
    return await _apiClient.get(
      Api.address.getCityArea,
      fromJson: (final response, final data) =>
          CityAreaModel.fromJson(response).data,
    );
  }

  /// 获取街道列表
  ///
  /// [areaId] - 区域ID
  ///
  /// 返回指定区域的街道列表
  Future<ApiResult<List<StreetListData>?>> getStreetList(
      final int areaId) async {
    return await _apiClient.get(
      "${Api.address.getStreetList}$areaId",
      fromJson: (final response, final data) =>
          StreetListModel.fromJson(response).data,
    );
  }

  /// 获取小区列表
  ///
  /// [streetId] - 街道ID
  ///
  /// 返回指定街道的小区列表
  Future<ApiResult<List<StreetListData>?>> getParkList(
      final int streetId) async {
    return await _apiClient.get(
      "${Api.address.getParkList}$streetId",
      fromJson: (final response, final data) =>
          ParkListModel.fromJson(response).data,
    );
  }

  /// 添加新地址
  ///
  /// [param] - 地址信息，包含详细地址、联系人、联系电话等
  ///
  /// 返回添加操作的结果
  Future<ApiResult<AddAddressData>> addAddress(
      final Map<String, dynamic> param) async {
    return await _apiClient.post(
      Api.address.addAddress,
      data: param,
      fromJson: (final response, final data) => AddAddressData.fromJson(data),
    );
  }
}
