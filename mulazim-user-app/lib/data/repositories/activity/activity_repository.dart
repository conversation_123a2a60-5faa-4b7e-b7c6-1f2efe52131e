import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/activity/seckill_food_model.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/data/models/activity/special_food_model.dart';
import 'package:user_app/data/models/activity/rankin_history_model.dart';
import 'package:user_app/data/models/activity/ranking_info_model.dart';
import 'package:user_app/data/models/activity/ranking_user_address_model.dart';

/// 活动数据仓库
///
/// 负责处理与活动相关的API请求，如秒杀活动、特价活动等。
/// 提供统一的数据访问接口，返回类型安全的ApiResult。
class ActivityRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  ActivityRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取秒杀活动数据
  ///
  /// [param] - 请求参数，可包含分页信息、筛选条件等
  ///
  /// 返回包含秒杀食品数据的[ApiResult]对象
  Future<ApiResult<SecKillFoodData?>> getSeckillData(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.activity.secKillFoods,
      params: param,
      fromJson: (final response, final data) {
        return SecKillFoodData.fromJson(data);
      },
    );
  }

  /// 获取特价活动数据
  ///
  /// [param] - 请求参数，可包含分页信息、筛选条件等
  ///
  /// 返回包含特价食品列表的[ApiResult]对象
  Future<ApiResult<List<SpecailFoodData>?>> getSpecialData(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.activity.specialFoods,
      params: param,
      fromJson: (final response, final _) {
        return SpecailFoodModel.fromJson(response).data;
      },
    );
  }

  /// 获取排行榜活动信息
  /// [param] - 获取参数
  /// 返回包含排行榜活动信息的[ApiResult]对象
  
  Future<ApiResult<RankingInfoData?>> getRankingInfo(final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.activity.rankingInfo,
      params: param,
      fromJson: (final response, final data) {
        print("data: ${data}");
        return RankingInfoData.fromJson(data);
      },
    );
  }

  /// 获取排名用户地址
  /// [param] - 获取参数
  /// 返回包含排名用户地址的[ApiResult]对象
  Future<ApiResult<RankingUserAddressData?>> getRankingUserAddress(final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.activity.rankingUserAddress,
      params: param,
      fromJson: (final response, final data) {
        print("data: ${data}");
        return RankingUserAddressData.fromJson(data);
      },
    );
  }

  /// 确认排行榜地址
  /// [param] - 确认参数，包含address_id、ranking_id等
  /// 返回确认操作的结果
  Future<ApiResult<bool>> confirmRankingAddress(final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.activity.rankingConfirmAddress,
      params: param,
      fromJson: (final response, final data) => true,
    );
  }

  /// 获取排行榜历史
  /// [param] - 获取参数
  /// 返回包含排行榜历史的[ApiResult]对象
  Future<ApiResult<List<RankingHistoryData>?>> getRankingHistory() async {
    return await _apiClient.get(
      Api.activity.rankingHistory,
      fromJson: (final response, final data) {
        if (data is List) {
          return data.map((e) => RankingHistoryData.fromJson(e as Map<String, dynamic>)).toList();
        }
        return [];
      },
    );
  }

}
