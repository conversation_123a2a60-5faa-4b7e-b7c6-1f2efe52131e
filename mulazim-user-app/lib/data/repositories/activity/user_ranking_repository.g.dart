// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_ranking_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userRankingRepositoryHash() =>
    r'35394564c52617fa9f423e4b45a9de01c7168aad';

/// 用户排行榜数据仓库
///
/// Copied from [userRankingRepository].
@ProviderFor(userRankingRepository)
final userRankingRepositoryProvider =
    AutoDisposeProvider<UserRankingRepository>.internal(
  userRankingRepository,
  name: r'userRankingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRankingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserRankingRepositoryRef
    = AutoDisposeProviderRef<UserRankingRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
