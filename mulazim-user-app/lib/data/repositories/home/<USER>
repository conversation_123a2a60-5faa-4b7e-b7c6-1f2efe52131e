import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/my_order/AgentPayModel.dart';
import 'package:user_app/data/models/my_order/part_refund_detail_model.dart';

/// 首页相关数据仓库类
///
/// 负责获取首页所需的各种数据，包括首页信息、通知、自取、快捷页面、优惠活动等
class HomeRepository {
  final ApiClient _apiClient;

  /// 构造函数
  HomeRepository({required final ApiClient apiClient}) : _apiClient = apiClient;

  /// 获取首页信息
  ///
  /// [param] - 请求参数
  ///
  /// 返回首页基本信息数据，包括轮播图、分类等
  Future<ApiResult<HomeData>> getHomeInfo(
      final Map<String, dynamic> param) async {
    try {
      log("开始获取首页信息: 参数 = $param");
      return await _apiClient.get(
        Api.home.homeInfo,
        params: param,
        fromJson: (final response, final data) {
          log("获取首页信息成功: ${data != null ? '有数据' : '无数据'}");
          if (data == null || data.isEmpty) {
            return HomeData();
          }
          return HomeData.fromJson(data);
        },
      );
    } catch (e, stack) {
      log("获取首页信息失败: $e");
      log("错误堆栈: $stack");
      rethrow;
    }
  }

  /// 获取首页通知
  ///
  /// [param] - 请求参数
  ///
  /// 返回首页通知数据
  Future<ApiResult<HomeNoticeData>> getHomeNotice(
      final Map<String, dynamic> param) async {
    try {
      log("开始获取首页通知: 参数 = $param");
      return await _apiClient.get(
        Api.home.homeNotice,
        params: param,
        fromJson: (final response, final data) {
          log("获取首页通知成功: ${data != null ? '有数据' : '无数据'}");
          if (data == null || data.isEmpty) {
            return HomeNoticeData();
          }
          return HomeNoticeData.fromJson(data);
        },
      );
    } catch (e, stack) {
      log("获取首页通知失败: $e");
      log("错误堆栈: $stack");
      rethrow;
    }
  }

  /// 获取自取页面数据
  ///
  /// [param] - 请求参数
  ///
  /// 返回自取页面所需的数据
  Future<ApiResult<SelfTakeData>> getSelfTake(
      final Map<String, dynamic> param) async {
    try {
      log("开始获取自取数据: 参数 = $param");
      return await _apiClient.get(
        Api.home.selfTake,
        params: param,
        fromJson: (final response, final data) {
          log("获取自取数据成功");
          return SelfTakeData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取自取数据失败: $e");
      rethrow;
    }
  }

  Future<ApiResult<UpgradeData>> terminalInfo(final Map<String, dynamic> param) async {
    try {
      // final response = await _apiClient.get(Api.home.terminalInfo,params: param);
      return await _apiClient.get(
        'http://api.mulazim.com/ug/v1/terminal/show?type=1',
        params: param,
        fromJson: (final response, final data) {
          return UpgradeData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取快捷页面数据失败: $e");
      rethrow;
    }
  }

  /// 获取快捷页面数据
  ///
  /// [param] - 请求参数
  /// [categoryId] - 分类ID，用于请求头
  ///
  /// 返回快捷页面所需的数据
  Future<ApiResult<QuickInfoData>> quickInfo(
      final Map<String, dynamic> param, final int categoryId) async {
    try {
      log("开始获取快捷页面数据: 参数 = $param, categoryId = $categoryId");
      return await _apiClient.get(
        Api.home.restaurantHome,
        params: param,
        options: Options(headers: {'parentCategoryId': categoryId}),
        fromJson: (final response, final data) {
          log("获取快捷页面数据成功");
          return QuickInfoData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取快捷页面数据失败: $e");
      rethrow;
    }
  }

  /// 获取优惠食品数据
  ///
  /// [param] - 请求参数
  ///
  /// 返回优惠食品列表数据
  Future<ApiResult<DiscountFoodsData>> discountFoods(
      final Map<String, dynamic> param) async {
    try {
      log("开始获取优惠页面数据: 参数 = $param");
      return await _apiClient.get(
        Api.home.discountFoods,
        params: param,
        fromJson: (final response, final data) {
          log("获取优惠页面数据成功");
          return DiscountFoodsData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取优惠页面数据失败: $e");
      rethrow;
    }
  }

  /// 获取配送员地址信息
  ///
  /// [param] - 请求参数
  ///
  /// 返回配送员相关的地址信息
  Future<ApiResult<AgentPayData>> getAgentPayInfo(
      final Map<String, dynamic> param) async {
    try {
      return await _apiClient.get(
        Api.home.agentPayInfo,
        params: param,
        fromJson: (final response, final data) {
          return AgentPayData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取配送员地址信息失败: $e");
      rethrow;
    }
  }

  /// 返回部分退款数据
  Future<ApiResult<PartRefundDetailData>> partRefundInfo(
      final Map<String, dynamic> param) async {
    try {
      return await _apiClient.get(
        Api.home.partRefundInfo,
        params: param,
        fromJson: (final response, final data) {
          return PartRefundDetailData.fromJson(data);
        },
      );
    } catch (e) {
      log("获取配送员地址信息失败: $e");
      rethrow;
    }
  }
}
