import 'dart:developer';

import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/order/check_pay_result_model.dart';
import 'package:user_app/data/models/order/create_order_response_model.dart';
import 'package:user_app/data/models/order/query_order_response_model.dart';
import 'package:user_app/data/models/order/take_time_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单模块数据仓库
class OrderRepository {
  final ApiClient _apiClient;

  /// 构造函数
  OrderRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取订单提交页面数据
  ///
  /// [data] - 请求参数
  ///
  /// 返回可选取的时间列表数据
  Future<ApiResult<TakeTimeListData>> getTakeTimeList(
      final Map<String, dynamic> data) async {
    final result = await _apiClient.post(
      Api.order.takeTimeList,
      data: data,
      fromJson: (final response, final data) {
        return TakeTimeListData.fromJson(data);
      },
    );

    if (!result.success) {
      // BotToast.showText(text: result.msg);
      BotToast.showText(text: S.current.input_address);
    }

    return result;
  }

  /// 创建订单
  ///
  /// [data] - 订单信息参数
  ///
  /// 返回创建订单的结果
  Future<ApiResult<CreateOrderDara>> createOrder(
    final Map<String, dynamic> data,
  ) async {
    final result = await _apiClient.post(
      Api.order.createOrder,
      data: data,
      fromJson: (final response, final data) {
        return CreateOrderDara.fromJson(data);
      },
    );

    if (!result.success) {
      BotToast.showText(text: result.msg);
    }

    return result;
  }

  /// 查询订单状态
  ///
  /// [param] - 查询参数，包含订单ID
  ///
  /// 返回订单查询结果
  Future<ApiResult<QueryOrderResponseData>> queryOrder(
      final Map<String, String> param) async {
    final result = await _apiClient.get(
      Api.order.queryOrder,
      params: param,
      fromJson: (final response, final data) {
        return QueryOrderResponseData.fromJson(data);
      },
    );

    if (!result.success) {
      BotToast.showText(text: result.msg);
    }

    return result;
  }

  /// 查询付款结果
  Future<CheckPayResultModel> queryPayResult(
      final Map<String, dynamic> param) async {
    try {
      final response = await _apiClient.get(
        Api.order.queryPayResult,
        params: param,
        fromJson: (final response, final data) {
          return CheckPayResultModel.fromJson(response);
        },
      );
      final model = response.data;
      if (model?.status != 200) {
        BotToast.showText(text: model?.msg ?? '获取数据失败');
        return CheckPayResultModel();
      }
      return model ?? CheckPayResultModel();
    } catch (e, trace) {
      log(e.toString());
      log(trace.toString());
      rethrow;
    }
  }

  /// 检查是否可以现金支付
  ///
  /// [orderId] - 订单ID
  ///
  /// 返回现金支付检查结果
  Future<ApiResult<CashPayCheckModel>> checkCanCashPay(
      final String orderId) async {
    final result = await _apiClient.get(
      Api.payment.canCashPay,
      params: {'order_id': orderId},
      fromJson: (final response, final data) {
        return CashPayCheckModel.fromJson(response);
      },
    );

    if (!result.success) {
      BotToast.showText(text: result.msg);
    }

    return result;
  }

  /// 现金支付
  ///
  /// [orderId] - 订单ID
  ///
  /// 返回支付结果
  Future<ApiResult<dynamic>> cashPayment(final String orderId) async {
    final result = await _apiClient.post(
      Api.payment.pay,
      data: {
        'pay_id': 1, // 现金支付ID为1
        'order_id': orderId,
      },
      fromJson: (final response, final data) {
        return data;
      },
    );

    if (!result.success) {
      BotToast.showText(text: result.msg);
    }

    return result;
  }
}
