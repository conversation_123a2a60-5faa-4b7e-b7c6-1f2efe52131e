import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/data/models/mine/help_model.dart';
import 'package:user_app/data/models/mine/license_model.dart';
import 'package:user_app/data/models/user/user_request_model.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

part 'user_repository.g.dart';

/// 用户信息存储库
/// 集中管理用户信息的存储和获取
class UserRepository {
  final LocalStorageRepository _localStorageRepository;

  /// API客户端
  final ApiClient _apiClient;

  /// 构造函数
  UserRepository({
    required final LocalStorageRepository localStorageRepository,
    required final ApiClient apiClient,
  })  : _localStorageRepository = localStorageRepository,
        _apiClient = apiClient;

  /// 保存用户信息和登录凭证
  Future<void> saveUserInfo(final UserInfo userInfo, final String token) async {
    try {
      // 存储用户信息
      await _localStorageRepository.saveUserInfo(userInfo);

      // 存储令牌
      await _localStorageRepository.saveToken(token);

      if (kDebugMode) {
        print('用户信息和令牌已保存');
      }
    } catch (e) {
      if (kDebugMode) {
        print('保存用户信息失败: $e');
      }
      rethrow;
    }
  }

  /// 获取用户信息
  UserInfo? getUserInfo() {
    try {
      final userInfoJson = _localStorageRepository.getUserInfo();
      if (userInfoJson == null) {
        return null;
      }

      return userInfoJson;
    } catch (e) {
      if (kDebugMode) {
        print('获取用户信息失败: $e');
      }
      return null;
    }
  }

  /// 获取登录令牌
  String? getToken() {
    return _localStorageRepository.getToken();
  }

  /// 更新用户信息
  Future<void> updateUserInfo(final UserInfo userInfo) async {
    try {
      final existingUserInfo = getUserInfo();
      if (existingUserInfo == null) {
        throw Exception('用户未登录，无法更新信息');
      }

      // 合并现有信息和新信息
      final updatedUserInfo = UserInfo(
        id: userInfo.id,
        name: userInfo.name ?? existingUserInfo.name,
        mobile: userInfo.mobile ?? existingUserInfo.mobile,
        avatar: userInfo.avatar ?? existingUserInfo.avatar,
        birthday: userInfo.birthday ?? existingUserInfo.birthday,
        gender: userInfo.gender ?? existingUserInfo.gender,
        money: userInfo.money ?? existingUserInfo.money,
        points: userInfo.points ?? existingUserInfo.points,
        profilePoint: userInfo.profilePoint ?? existingUserInfo.profilePoint,
        recommendClientPoint: userInfo.recommendClientPoint ??
            existingUserInfo.recommendClientPoint,
      );

      // 重新保存用户信息
      final token = getToken() ?? '';
      await saveUserInfo(updatedUserInfo, token);
    } catch (e) {
      if (kDebugMode) {
        print('更新用户信息失败: $e');
      }
      rethrow;
    }
  }

  /// 绑定微信信息
  Future<ApiResult<void>> bindWechatInfo(final UserRequestModel user) async {
    return await _apiClient.post(
      Api.user.bindWechatInfo,
      data: user.toJson(),
      fromJson: (final _, final __) => null, // void 返回类型
    );
  }

  /// 上传图片
  Future<ApiResult<Map<String, dynamic>>> uploadImage({
    required final String filePath,
    required final String typeName,
  }) async {
    return await _apiClient.upload(
      Api.user.uploadImage,
      filePath: filePath,
      name: 'image',
      formData: {
        'type_name': typeName,
      },
      fromJson: (final response, final data) => data as Map<String, dynamic>,
    );
  }

  /// 清除用户信息和登录凭证
  Future<void> clearUserInfo() async {
    await _localStorageRepository.clearUserInfo();
  }

  /// 检查用户是否已登录
  bool isLoggedIn() {
    final token = getToken();
    return token != null && token.isNotEmpty;
  }

  /// 获取网页列表
  Future<ApiResult<Map<String, dynamic>>> fetchWebList() async {
    return await _apiClient.get(
      Api.help.webUrlList,
      params: {
        'terminal_id': 1,
      },
      fromJson: (final _, final data) => data as Map<String, dynamic>,
    );
  }

  /// 获取代理资质列表
  Future<ApiResult<List<LicenseModel>>> fetchLicenseList(final areaId) async {
    return await _apiClient.get(
      Api.help.agentLicense,
      params: {
        'area_id': areaId,
      },
      fromJson: (final _, final dynamic data) {
        if (data is List) {
          return List<dynamic>.from(data)
              .map((final item) =>
                  LicenseModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
        return <LicenseModel>[];
      },
    );
  }

  /// 获取帮助列表
  Future<ApiResult<List<HelpModel>>> fetchHelpList() async {
    return await _apiClient.get(
      Api.help.helpList,
      params: {
        'terminal_id': 1,
      },
      fromJson: (final _, final dynamic data) {
        final Map<String, dynamic> dataMap =
            Map<String, dynamic>.from(data as Map);
        final List<dynamic> items =
            List<dynamic>.from(dataMap['items'] as List);
        return items
            .map((final item) =>
                HelpModel.fromJson(item as Map<String, dynamic>))
            .toList();
      },
    );
  }

  /// 获取帮助详情
  Future<ApiResult<String>> fetchHelpDetail(final int id) async {
    return await _apiClient.get(
      Api.help.helpDetail,
      params: {
        'help_id': id,
      },
      fromJson: (final _, final data) {
        final String html = data as String;
        return html.split("<body>")[1].split("</body>")[0];
      },
    );
  }
}

/// 用户存储库提供者
@riverpod
UserRepository userRepository(final Ref ref) {
  return UserRepository(
    localStorageRepository: ref.watch(localStorageRepositoryProvider),
    apiClient: ref.watch(apiClientProvider),
  );
}
