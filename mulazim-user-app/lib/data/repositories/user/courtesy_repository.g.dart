// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courtesy_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$courtesyRepositoryHash() =>
    r'209e369b558939649f5eb16ba8ea1a12d102c051';

/// 优惠券仓库
///
/// Copied from [courtesyRepository].
@ProviderFor(courtesyRepository)
final courtesyRepositoryProvider =
    AutoDisposeProvider<CourtesyRepository>.internal(
  courtesyRepository,
  name: r'courtesyRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$courtesyRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CourtesyRepositoryRef = AutoDisposeProviderRef<CourtesyRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
