import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/poster/poster_model.dart';

part 'poster_repository.g.dart';

/// 海报数据仓库
class PosterRepository {
  /// 构造函数
  PosterRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  final ApiClient _apiClient;

  /// 获取海报列表
  ///
  /// [type] - 海报类型
  ///
  /// 返回海报列表数据
  Future<ApiResult<List<PosterModel>>> getPosterList(final int type) async {
    return await _apiClient.get(Api.poster.list, params: {'type': type},
        fromJson: (final response, final data) {
      final result = PosterListResponse.fromJson(response);
      return result.data;
    });
  }

  /// 生成海报
  ///
  /// [resId] - 资源ID
  /// [type] - 海报类型
  /// [modelId] - 模板ID
  ///
  /// 返回海报生成任务ID
  Future<ApiResult<String>> generatePoster({
    required final String resId,
    required final int type,
    required final int modelId,
  }) async {
    return await _apiClient.get(
      Api.poster.generate,
      params: {
        'res_id': resId,
        'type': type,
        'model_id': modelId,
      },
      fromJson: (final response, final data) {
        final result = PosterGenerateData.fromJson(data);
        return result.jobId;
      },
    );
  }

  /// 查询海报状态
  ///
  /// [jobId] - 任务ID
  ///
  /// 返回海报URL
  Future<ApiResult<String>> queryPosterStatus(final String jobId) async {
    return await _apiClient.get(Api.poster.query, params: {
      'job_id': jobId,
    }, fromJson: (final response, final data) {
      final result = PosterQueryData.fromJson(data);
      return result.url;
    });
  }
}

/// 依赖注入
@riverpod
PosterRepository posterRepository(final Ref ref) {
  return PosterRepository(
    apiClient: ref.watch(apiClientProvider),
  );
}
