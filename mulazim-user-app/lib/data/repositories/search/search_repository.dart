import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/search/search_page_model.dart';
import 'package:user_app/data/models/search/search_restaurant_model.dart';
import 'package:user_app/data/models/search/search_word_panel.dart';

/// 搜索相关数据仓库
class SearchRepository {
  final ApiClient _apiClient;

  /// 构造函数
  SearchRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取搜索页面推荐信息
  ///
  /// [param] - 搜索参数
  ///
  /// 返回搜索页面模型数据
  Future<ApiResult<SearchPageData>> getSearchInfo(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      '/v1/search/recommended',
      params: param,
      fromJson: (final response, final data) => SearchPageData.fromJson(data),
    );
  }

  /// 根据关键词搜索
  ///
  /// [param] - 搜索参数，包含关键词
  ///
  /// 返回搜索词结果模型
  Future<ApiResult<SearchWordData>> getSearchByWord(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      "/v1/search/words",
      params: param,
      fromJson: (final response, final data) => SearchWordData.fromJson(data),
    );
  }

  /// 搜索餐厅
  ///
  /// [param] - 搜索参数，包含餐厅相关条件
  ///
  /// 返回餐厅搜索结果
  Future<ApiResult<SearchRestaurantData>> searchRestaurant(
      final Map<String, dynamic> param) async {
        return await _apiClient.get("/v1/search/search-restaurant",params: param,fromJson: (final response, final data){
          print('data------>$data');
          print('response------>$response');
          return SearchRestaurantData.fromJson(data);
        },
      );
  }
}
