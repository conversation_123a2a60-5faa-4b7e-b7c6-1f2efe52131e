// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_ranking_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRankingRepositoryHash() =>
    r'8e1eedfb5aa5269a0adaff093c0facecdc43d2fe';

/// 提供OrderRankingRepository实例的Provider
///
/// 使用Riverpod的@riverpod注解自动生成Provider代码
///
/// Copied from [orderRankingRepository].
@ProviderFor(orderRankingRepository)
final orderRankingRepositoryProvider =
    AutoDisposeProvider<OrderRankingRepository>.internal(
  orderRankingRepository,
  name: r'orderRankingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRankingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OrderRankingRepositoryRef
    = AutoDisposeProviderRef<OrderRankingRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
