import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';

part 'order_ranking_repository.g.dart';

/// 订单排名仓库
///
/// 负责处理与订单排名、抽奖活动相关的API请求，如获取排名记录、兑换奖品等。
/// 提供统一的数据访问接口，返回类型安全的ApiResult。
class OrderRankingRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  OrderRankingRepository({
    required final ApiClient apiClient,
  }) : _apiClient = apiClient;

  /// 获取订单排名记录
  ///
  /// [areaId] - 区域ID，用于筛选指定区域的排名
  /// [platType] - 平台类型
  ///
  /// 返回包含排名记录的[ApiResult]对象
  Future<ApiResult<OrderRankingModel>> getLuckyHistory(
      final int areaId, final int platType) async {
    return await _apiClient.get(
      Api.orderRanking.luckyHistory,
      params: {
        'area_id': areaId,
        'plat_type': platType,
      },
      fromJson: (final response, final data) =>
          OrderRankingModel.fromJson(data),
    );
  }

  /// 获取订单排名页面数据
  ///
  /// [areaId] - 区域ID
  /// [platType] - 平台类型
  /// [prizeId] - 奖品ID（可选）
  ///
  /// 返回包含活动页面数据的[ApiResult]对象
  Future<ApiResult<Map<String, dynamic>>> getActivityPage({
    required final int areaId,
    required final int platType,
    final int? prizeId,
  }) async {
    // 构建参数
    final Map<String, dynamic> params = {
      'area_id': areaId,
      'plat_type': platType,
    };

    // 如果提供了奖品ID，则添加到参数中
    if (prizeId != null) {
      params['prize_id'] = prizeId;
    }

    return await _apiClient.get(
      Api.orderRanking.activityPage,
      params: params,
      fromJson: (final response, final data) => data as Map<String, dynamic>,
    );
  }

  /// 提交活动评价
  ///
  /// [lotteryActivityId] - 抽奖活动ID
  /// [type] - 评价类型
  /// [content] - 评价内容
  ///
  /// 返回表示操作是否成功的[ApiResult]对象
  Future<ApiResult<bool>> submitActivityComment(
    final int lotteryActivityId,
    final int type,
    final String content,
  ) async {
    return await _apiClient.post(
      Api.orderRanking.comment,
      data: {
        'lottery_activity_id': lotteryActivityId,
        'type': type,
        'content': content,
      },
      fromJson: (final response, final data) => true,
    );
  }

  /// 分享活动
  ///
  /// [activityId] - 活动ID
  /// [type] - 分享类型，默认为6表示分享
  ///
  /// 返回表示操作是否成功的[ApiResult]对象
  Future<ApiResult<bool>> shareActivity(final int activityId,
      [final int type = 6]) async {
    return await _apiClient.post(
      Api.orderRanking.favoriteShareView,
      data: {
        'activity_id': activityId,
        'valentine_item_ids': [activityId],
        'type': type, // 分享类型，默认为6表示分享
      },
      fromJson: (final response, final data) => true,
    );
  }

  /// 兑换奖品
  ///
  /// [chanceId] - 抽奖机会ID
  ///
  /// 返回表示操作是否成功的[ApiResult]对象
  Future<ApiResult<bool>> exchangePrize(final int chanceId) async {
    return await _apiClient.post(
      Api.orderRanking.exchange,
      data: {
        'chance_id': chanceId,
      },
      fromJson: (final response, final data) => true,
    );
  }
}

/// 提供OrderRankingRepository实例的Provider
///
/// 使用Riverpod的@riverpod注解自动生成Provider代码
@riverpod
OrderRankingRepository orderRankingRepository(final Ref ref) {
  return OrderRankingRepository(
    apiClient: ApiClient(),
  );
}
