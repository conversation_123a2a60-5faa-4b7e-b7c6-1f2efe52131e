// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collect_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$collectRepositoryHash() => r'be463497702c8e4de7f5aa6485114874391615bc';

/// 收藏仓库提供者
///
/// 使用Riverpod的@riverpod注解自动生成Provider代码，
/// 提供全局共享的CollectRepository实例
///
/// Copied from [collectRepository].
@ProviderFor(collectRepository)
final collectRepositoryProvider =
    AutoDisposeProvider<CollectRepository>.internal(
  collectRepository,
  name: r'collectRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CollectRepositoryRef = AutoDisposeProviderRef<CollectRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
