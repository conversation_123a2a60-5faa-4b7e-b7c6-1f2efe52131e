import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/add_shop/license_type.dart';

part 'add_shop_repository.g.dart';

/// 商家入驻仓库提供者
///
/// 通过Riverpod依赖注入提供AddShopRepository实例
/// 自动使用全局ApiClient实例
@riverpod
AddShopRepository addShopRepository(final Ref ref) {
  final apiClient = ref.watch(apiClientProvider);
  return AddShopRepository(apiClient: apiClient);
}

/// 商家入驻仓库
///
/// 负责处理与商家入驻相关的API请求，包括获取证件类型、城市区域信息，
/// 以及提交商家入驻申请。提供统一的数据访问接口，返回类型安全的ApiResult。
class AddShopRepository {
  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  const AddShopRepository({
    required final ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  /// 获取证件类型列表
  ///
  /// 从服务器获取可用的证件类型列表
  ///
  /// 返回包含证件类型列表的[ApiResult]对象
  Future<ApiResult<List<LicenseType>>> getLicenseTypes() async {
    return await _apiClient.get(
      Api.addShop.licenseList,
      fromJson: (final response, final data) {
        try {
          final Map<String, dynamic> responseData =
              data as Map<String, dynamic>;

          // 检查license字段是否存在
          if (!responseData.containsKey('license')) {
            return <LicenseType>[];
          }

          final List<dynamic> licenseData =
              responseData['license'] as List<dynamic>;

          return licenseData
              .map((final item) => LicenseType(
                    id: item['id'] as int,
                    name: item['name'] as String,
                  ))
              .toList();
        } catch (e) {
          // 发生错误时返回空列表
          return <LicenseType>[];
        }
      },
    );
  }

  /// 获取城市区域列表
  ///
  /// 从服务器获取可用的城市区域列表
  ///
  /// 返回包含城市区域信息的[ApiResult]对象
  Future<ApiResult<List<Map<String, dynamic>>>> getCityAreaList() async {
    return await _apiClient.get(
      Api.address.getCityArea,
      fromJson: (final response, final data) {
        try {
          final List<dynamic> listData = data as List<dynamic>;
          return listData.cast<Map<String, dynamic>>();
        } catch (e) {
          // 发生错误时返回空列表
          return <Map<String, dynamic>>[];
        }
      },
    );
  }

  /// 提交商家入驻申请
  ///
  /// [shopName] - 商店名称
  /// [phone] - 联系电话
  /// [cityId] - 城市ID
  /// [areaId] - 区域ID
  ///
  /// 返回表示操作结果的[ApiResult]对象
  Future<ApiResult<dynamic>> submit({
    required final String shopName,
    required final String phone,
    required final int cityId,
    required final int areaId,
  }) async {
    return await _apiClient.post(
      Api.addShop.create,
      data: {
        'store_name': shopName,
        'phone': phone,
        'city_id': cityId,
        'area_id': areaId,
      },
      fromJson: (final response, final data) => data,
    );
  }
}
