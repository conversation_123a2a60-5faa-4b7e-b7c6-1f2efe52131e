import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/auth/auth_model.dart';

part 'auth_repository.g.dart';

/// 认证仓库
///
/// 负责处理与用户认证相关的API请求，包括发送短信验证码和短信验证码登录。
/// 提供统一的数据访问接口，返回类型安全的ApiResult。
class AuthRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ///
  /// [apiClient] - API客户端实例，通过依赖注入提供
  AuthRepository({required final ApiClient apiClient}) : _apiClient = apiClient;

  /// 发送短信验证码
  ///
  /// [phone] - 用户手机号
  ///
  /// 返回发送结果，成功时data为null
  Future<ApiResult<void>> sendSmsCode(final String phone) async {
    return await _apiClient.post(
      Api.auth.sendSmsCode,
      data: {"mobile": phone},
      fromJson: (final response, final data) => data,
    );
  }

  /// 短信验证码登录
  ///
  /// [phone] - 用户手机号
  /// [code] - 短信验证码
  ///
  /// 返回登录结果，包含用户令牌等认证信息
  Future<ApiResult<AuthData>> smsLogin(
      final String phone, final String code) async {
    final data = {
      "mobile": phone,
      "verify": code,
      "client_id": "gDw6hUnOkYXIs5NTyMju",
      "client_secret": "SgxOFzp4sLUrqITpYF4HSYgu5oIlfATu",
      "grant_type": "verify"
    };

    return await _apiClient.post(
      Api.auth.login,
      data: data,
      fromJson: (final response, final data) {
        return AuthData.fromJson(data);
      },
    );
  }


  /// 注册极光regId
  ///
  /// 返回登录结果，包含用户令牌等认证信息
  Future<ApiResult<void>> registerJPush(final String regId, final String lang) async {
    if (regId.isEmpty) {
      return ApiResult(status: 0, msg: 'regId is empty');
    }
    final data = {
      "rid": regId,
      "platform": Platform.isIOS ? "ios" : "android",
      "lang": lang
    };

    return await _apiClient.post(
      Api.auth.register,
      data: data,
      fromJson: (final response, final data) {},
    );
  }

  /// 注銷极光regId
  ///
  /// 返回登录结果，包含用户令牌等认证信息
  Future<ApiResult<void>> unRegisterJPush(final String regId) async {
    if (regId.isEmpty) {
      return ApiResult(status: 0, msg: 'regId is empty');
    }
    final data = {
      "rid": regId,
    };
    return await _apiClient.post(
      Api.auth.unregister,
      data: data,
      fromJson: (final response, final data) {},
    );
  }


}

/// 提供AuthRepository实例的Provider
///
/// 使用Riverpod的@riverpod注解自动生成Provider代码
@riverpod
AuthRepository authRepository(Ref ref) {
  return AuthRepository(apiClient: ref.watch(apiClientProvider));
}
