// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'points_repository_impl.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pointsRepositoryHash() => r'e1e4dd345a4717daaa8a9c2e8528dbd46c974fed';

/// 积分仓库提供者
///
/// Copied from [pointsRepository].
@ProviderFor(pointsRepository)
final pointsRepositoryProvider = AutoDisposeProvider<PointsRepository>.internal(
  pointsRepository,
  name: r'pointsRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pointsRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PointsRepositoryRef = AutoDisposeProviderRef<PointsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
