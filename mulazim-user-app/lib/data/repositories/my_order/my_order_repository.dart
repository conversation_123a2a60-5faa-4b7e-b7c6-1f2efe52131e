import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/my_order/my_order_model.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/data/models/my_order/shipper_position_model.dart';

/// 订单相关数据仓库
class MyOrderRepository {
  final ApiClient _apiClient;

  MyOrderRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取我的订单列表
  ///
  /// [params] - 请求参数，包含:
  /// - type: 订单类型，1:今日订单，2:所有订单
  /// - page: 页码
  /// - limit: 每页数量
  ///
  /// 返回订单列表数据
  Future<ApiResult<MyOrderData>> getMyOrder(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.get(
      Api.home.myOrder,
      params: params,
      fromJson: (final response, final data) {
        return MyOrderData.fromJson(data);
      },
    );
  }

  /// 取消订单 (unsubscribe - 用户主动取消，state_id == 3时使用)
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 返回取消订单操作结果
  Future<ApiResult> cancelOrder(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.post(
      Api.home.cancelOrder,
      data: params,
      fromJson: (final response, final data) => null,
    );
  }

  /// 关闭订单 (close - 商家可关闭，can_close == 1时使用)
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 返回关闭订单操作结果
  Future<ApiResult> closeOrder(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.post(
      Api.home.closeOrder,
      data: params,
      fromJson: (final response, final data) => null,
    );
  }

  /// 删除订单
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 返回删除订单操作结果
  Future<ApiResult> delOrder(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.post(
      Api.home.delOrder,
      data: params,
      fromJson: (final response, final data) => null,
    );
  }

  /// 获取订单详情
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 返回订单详细信息
  Future<ApiResult<OrderDetailData>> getOrderDetail(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.get(
      Api.home.myOrderDetail,
      params: params,
      fromJson: (final response, final data) {
        // Map<String, dynamic> myResponse = {"restaurant_id":2121,"shipper_id":2394,"restaurant_name":"ياردەمچى كەسىپ","restaurant_address":"مۇلازىم","restaurant_phone":"2854886","restaurant_state":1,"restaurant_lng":87.626074,"is_commented":false,"restaurant_logo":"https://file.discount.almas.biz/upload/restaurant/logo/202303/08/2aa4f0b6a36300eb67dd942b9d9bf3d7.png@400w_300h","restaurant_lat":43.768118,"id":11787590,"order_id":"yardamqi000000002505141905460668","name":"ئېلشات","mobile":"18599131337","category_id":1,"order_address":"يەنئەن يولى رۈيدا A بىنا 1232-ئىشخانا","pay_type":6,"pay_type_name":"ۋاكالەتچى توردا تۆلىگەن","pay_platform":1,"pay_platform_label":"lakala","pay_type_list":[],"description":"(چوكا قوشۇق 2 كىشلىك)","booking_time":"19:39","timezone":8,"booking_time_cst":"05-14 19:39","booking_date_time_cst":"05-14 19:39","created_at":"2025-05-14 19:05","expired":0,"expired_time":0,"work_wechat_state":1,"work_wechat_qrcode":"https://file.discount.almas.biz/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png@400w_300h","service_phone":"0991-2854886","admin_name":"aسىناق","admin_phone":"18129290467","is_score":0,"state":4,"shipment":2,"total_discount_amount":0,"original_shipment":2,"reduce_shipment":0,"lunch_box_fee":2,"city_id":1,"city_name":"ئۈرۈمچى","area_id":1,"area_name":"تىيانشان رايونى","street_id":93,"street_name":"يەنئەن يولى","building_id":1979,"building_name":"رۈيدا A بىنا","delivery_type":1,"self_take_number":null,"order_detail":{"original_price":14,"price":14,"foods":[{"id":18566646,"food_id":51972,"name":"3 كىلومىتىر ئىچى نەرسە يەتكۈزۇش","img":"https://file.discount.almas.biz/upload/restaurantfoods/201906/02/f914d04a85c5148afc79f793ab16e5f0.jpg@400w_300h","original_price":4,"price":4,"number":1,"lunch_box_id":55,"lunch_box_count":1,"lunch_box_fee":2,"multi_discount_id":0,"multi_discount_detail_id":0},{"id":18566647,"food_id":51974,"name":"5كىلومىتىر ئىچى نەرسە يەتكۈزۇش","img":"https://file.discount.almas.biz/upload/restaurantfoods/201906/02/33445740f92af4cf51c9aae26057f8d0.jpg@400w_300h","original_price":10,"price":10,"number":1,"lunch_box_id":0,"lunch_box_count":0,"lunch_box_fee":0,"multi_discount_id":0,"multi_discount_detail_id":0}]},"actual_paid":18,"order_state_log":[{"order_state":4,"name":"زاكاز قوبۇل قىلىندى","icon":"https://file.discount.almas.biz/images/order-state-icons/received.png","color":"139d59","fail_reason":null,"created_at":"2025-05-14 19:06"},{"order_state":3,"name":"زاكاز قوبۇللىنىۋاتىدۇ","icon":"https://file.discount.almas.biz/images/order-state-icons/recive_success.png","color":"b3b3b3","fail_reason":null,"created_at":"2025-05-14 19:06"},{"order_state":2,"name":"زاكاز مۇقىملاندى","icon":"https://file.discount.almas.biz/images/order-state-icons/confirm_success.png","color":"b3b3b3","fail_reason":null,"created_at":"2025-05-14 19:06"},{"order_state":1,"name":"پۇل تۆلەشنى ساقلاۋاتىدۇ","icon":"https://file.discount.almas.biz/images/order-state-icons/new_success.png","color":"b3b3b3","fail_reason":null,"created_at":"2025-05-14 19:05"}],"msg_count":0,"shipper":{"shipper_lng":"0.0","shipper_lat":"0.0","building_lng":87.62642,"building_lat":43.768194,"res_lng":87.626074,"res_lat":43.768118,"res_logo":"https://file.discount.almas.biz/upload/restaurant/logo/202303/08/2aa4f0b6a36300eb67dd942b9d9bf3d7.png@400w_300h","state":4},"marketing":[],"coupon":null,"other_marketing":[],"shipment_steps":[],"delayed":{"id":"ynYmEO","original_booking_time":"2025-05-14 19:39:33","new_booking_time":"2025-05-14 19:47:33","agree_state":1,"reason_delay":"يول بەك توساقكەن","delayed_duration":8},"order_state_log_new":{"items":[{"title":"زاكاز مۇقىملاندى","time":"05-14 19:06"},{"title":"قوبۇل قىلىندى","time":"05-14 19:06"},{"title":"تەقسىملىنىۋاتىدۇ","time":""},{"title":"تاماملاندى","time":""}],"step":2},"order_detail_ranking_activity_title":"","part_refund_id":0,"part_refund_type":0,"part_refund_amount":0};
        return OrderDetailData.fromJson(data);
      },
    );
  }

  /// 同意要求
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 同意要求操作结果
  Future<ApiResult> confirmDelay(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.post(
      Api.home.prolongConfirm,
      data: params,
      fromJson: (final response, final data) => null,
    );
  }

  /// 获取配送员位置信息
  ///
  /// [params] - 请求参数，包含订单ID等信息
  ///
  /// 返回配送员实时位置信息
  Future<ApiResult<ShipperPositionData>> getShipperPositionInfo(
    final Map<String, dynamic> params,
  ) async {
    return await _apiClient.get(
      Api.home.shipperPositionInfo,
      params: params,
      fromJson: (final response, final data) {
        return ShipperPositionData.fromJson(data);
      },
    );
  }
}
