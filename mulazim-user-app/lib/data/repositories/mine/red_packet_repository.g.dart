// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'red_packet_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$redPacketRepositoryHash() =>
    r'1c61e18e4f0d350386131b4a1df5e07c3acbd021';

/// 红包仓库
///
/// Copied from [redPacketRepository].
@ProviderFor(redPacketRepository)
final redPacketRepositoryProvider =
    AutoDisposeProvider<RedPacketRepository>.internal(
  redPacketRepository,
  name: r'redPacketRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$redPacketRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RedPacketRepositoryRef = AutoDisposeProviderRef<RedPacketRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
