import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/user/red_packet_model.dart';

part 'red_packet_repository.g.dart';

/// 红包仓库
@riverpod
RedPacketRepository redPacketRepository(final Ref ref) {
  final apiClient = ref.watch(apiClientProvider);
  return RedPacketRepository(apiClient);
}

/// 红包仓库实现
class RedPacketRepository {
  /// 构造函数
  const RedPacketRepository(this._apiClient);

  /// API客户端
  final ApiClient _apiClient;

  /// 获取红包列表
  ///
  /// 返回用户红包列表数据
  Future<ApiResult<RedPacketListModel>> getRedPacketList() async {
    return await _apiClient.get(
      Api.mine.redPacketList,
      params: {'limit': 100},
      fromJson: (final response, final data) {
        return RedPacketListModel.fromJson(data);
      },
    );
  }
}
