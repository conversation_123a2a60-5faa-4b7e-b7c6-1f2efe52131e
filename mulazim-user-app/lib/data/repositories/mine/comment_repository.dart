import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/mine/comment_list_model.dart';
import 'dart:io';

import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';
// import 'package:flutter/foundation.dart';

part 'comment_repository.g.dart';

/// 评论仓库
class CommentRepository {
  final ApiClient _apiClient;

  /// 构造函数
  CommentRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取评论列表
  ///
  /// [page] - 页码
  /// [limit] - 每页条数
  ///
  /// 返回评论列表数据
  Future<ApiResult<CommentListData>> getCommentList(
    final int page,
    final int limit,
  ) async {
    return await _apiClient.get(
      Api.comment.list,
      params: {
        'limit': limit,
        'page': page,
      },
      fromJson: (final response, final data) {
        return CommentListData.fromJson(data);
      },
    );
  }

  /// 删除评论
  ///
  /// [commentId] - 评论ID
  ///
  /// 返回删除结果，true表示删除成功
  Future<ApiResult<bool>> deleteComment(final int commentId) async {
    return await _apiClient.post(
      Api.comment.delete,
      data: {'id': commentId},
      fromJson: (final response, final data) {
        return true; // 如果请求成功，则返回true
      },
    );
  }

  /// 获取订单评价详情
  ///
  /// [orderId] - 订单ID
  ///
  /// 返回订单评价详情数据
  Future<OrderEvaluateModel?> getOrderEvaluateDetail(final int orderId) async {
    try {
      final result = await _apiClient.get(
        Api.comment.orderDetail,
        params: {
          'order_id': orderId,
        },
        fromJson: (final response, final data) =>
            OrderEvaluateModel.fromJson(data),
      );

      if (result.success && result.data != null) {
        return result.data;
      }
      return null;
    } catch (e) {
      throw Exception('获取订单评价详情失败: $e');
    }
  }

  /// 创建评论
  ///
  /// [params] - 评论参数
  ///
  /// 返回创建结果，true表示创建成功
  Future<ApiResult> createComment(final Map<String, dynamic> params) async {
    try {
      final result = await _apiClient.post(
        Api.comment.create,
        data: params,
        fromJson: (final response, final data) => data,
      );
      return result;
    } catch (e) {
      throw Exception('创建评论失败: $e');
    }
  }

  /// 上传评论图片
  ///
  /// [imageFile] - 图片文件
  ///
  /// 返回图片URL
  Future<String?> uploadCommentImage(final File imageFile) async {
    try {
      // 使用评论专用的上传接口
      final result = await _apiClient.upload(
        Api.comment.uploadImage,
        filePath: imageFile.path,
        name: 'image',
        fromJson: (final response, final data) => data,
      );

      if (result.success && result.data != null) {
        // 根据小程序的响应格式: res.data.data.image
        if (result.data is Map<String, dynamic> &&
            result.data['image'] != null) {
          return result.data['image'];
        }
      }
      return null;
    } catch (e) {
      throw Exception('上传图片失败: $e');
    }
  }

  /// 删除评论图片
  ///
  /// [imageUrl] - 图片URL
  ///
  /// 返回删除结果
  Future<void> deleteCommentImage(final String imageUrl) async {
    try {
      // 使用POST方式删除，与小程序一致
      await _apiClient.post(
        '${Api.comment.deleteImage}?image=$imageUrl',
        fromJson: (final response, final data) => true,
      );
    } catch (e) {
      throw Exception('删除图片失败: $e');
    }
  }
}

/// 评论仓库提供者
@riverpod
CommentRepository commentRepository(final Ref ref) {
  return CommentRepository(
    apiClient: ref.watch(apiClientProvider),
  );
}
