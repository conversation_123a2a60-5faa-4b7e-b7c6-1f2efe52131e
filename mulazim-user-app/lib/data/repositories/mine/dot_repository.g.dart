// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dot_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dotsRepositoryHash() => r'1dae0b8b7c0006a7bdc12c698fc8c7f2e91e27dd';

/// See also [dotsRepository].
@ProviderFor(dotsRepository)
final dotsRepositoryProvider = AutoDisposeProvider<DotsRepository>.internal(
  dotsRepository,
  name: r'dotsRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dotsRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DotsRepositoryRef = AutoDisposeProviderRef<DotsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
