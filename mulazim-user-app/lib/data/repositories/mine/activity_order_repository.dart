import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity_order/lottery_detail_model.dart';
import 'package:user_app/data/models/activity_order/lottery_order_model.dart';

part 'activity_order_repository.g.dart';

/// 活动订单仓库
@riverpod
ActivityOrderRepository activityOrderRepository(
  final Ref ref,
) {
  return ActivityOrderRepository(
    apiClient: ref.watch(apiClientProvider),
  );
}

/// 活动订单仓库实现
class ActivityOrderRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ActivityOrderRepository({
    required final ApiClient apiClient,
  }) : _apiClient = apiClient;

  /// 获取活动订单列表
  ///
  /// 返回抽奖活动订单列表数据
  Future<ApiResult<LotteryOrderListModel>> getOrderList() async {
    return await _apiClient.get(
      Api.lottery.orderList,
      fromJson: (final response, final data) {
        return LotteryOrderListModel.fromJson(data);
      },
    );
  }

  /// 获取抽奖详情数据
  ///
  /// [id] - 抽奖活动ID
  ///
  /// 返回指定ID的抽奖活动详情
  Future<ApiResult<LotteryDetailModel>> getLotteryData(final int id) async {
    return await _apiClient.get(
      '${Api.lottery.orderDetail}?chance_id=$id',
      fromJson: (final response, final data) {
        return LotteryDetailModel.fromJson(data);
      },
    );
  }
}
