import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/restaurant/commnent_list_model.dart';
import 'package:user_app/data/models/restaurant/food_detail_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_comment_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_home_model.dart';

/// 餐厅相关数据仓库
class RestaurantRepository {
  final ApiClient _apiClient;

  /// 构造函数
  RestaurantRepository({required final ApiClient apiClient})
      : _apiClient = apiClient;

  /// 获取餐厅首页数据
  ///
  /// [param] - 请求参数，包含餐厅ID等信息
  ///
  /// 返回餐厅详情页数据
  Future<ApiResult<RestaurantHomeData>> getRestaurantHome(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.restaurant.show,
      params: param,
      fromJson: (final response, final data) =>
          RestaurantHomeData.fromJson(data),
    );
  }

  /// 获取餐厅食品列表
  ///
  /// [param] - 请求参数，包含餐厅ID、分类ID等信息
  ///
  /// 返回食品列表数据
  Future<ApiResult<FoodsData>> getFoodsList(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.foods.list,
      params: param,
      fromJson: (final response, final data) {
        return FoodsData.fromJson(data);
      },
    );
  }

  // 获取餐厅首页数据
  Future<ApiResult<FoodDetailData>> getFoodDetail(
      final Map<String, dynamic> param) async {
    try {
      return await _apiClient.get(
        Api.restaurant.foodsShow,
        params: param,
        fromJson: (final response, final data) {
          return FoodDetailData.fromJson(data);
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // 美食收藏
  Future<ApiResult> addFoodCollect(final Map<String, dynamic> param) async {
    try {
      return await _apiClient.post(
        Api.restaurant.addFoodCollect,
        data: param,
        fromJson: (final response, final data) {
          return response;
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // 取消收藏
  Future<ApiResult> removeFoodCollect(final Map<String, dynamic> param) async {
    try {
      return await _apiClient.post(
        Api.restaurant.removeFoodCollect,
        data: param,
        fromJson: (final response, final data) {
          return response;
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// 获取套餐列表
  ///
  /// [param] - 请求参数，包含建筑ID、分页等信息
  ///
  /// 返回套餐列表数据
  Future<ApiResult<List<ComboFoodsModel>>> getComboList(
    final Map<String, dynamic> param,
  ) async {
    return await _apiClient.get(
      Api.restaurant.comboList,
      params: param,
      fromJson: (final response, final data) => (data as List)
          .map((final e) => ComboFoodsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Future<ApiResult<RestaurantCommentListData>> getRestaurantCommentList(
      final Map<String, dynamic> param) async {
    try {
      final result = await _apiClient.get(
        Api.restaurant.restaurantCommentList,
        params: param,
        fromJson: (final response, final data) {
          return RestaurantCommentListData.fromJson(data);
        },
      );

      if (result.data != null && result.data!.type != null) {
        final typeInfo = result.data!.type!
            .map((final t) => "${t.name}(${t.count}):${t.type}")
            .join(", ");
      }

      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取餐厅评论列表数据
  ///
  /// [param] - 请求参数，包含餐厅ID、页码等信息
  ///
  /// 返回评论列表数据
  Future<ApiResult<CommentListData>> getCommentList(
      final Map<String, dynamic> param) async {
    return await _apiClient.get(
      Api.comment.list,
      params: param,
      fromJson: (final response, final data) {
        return CommentListData.fromJson(data);
      },
    );
  }
}
