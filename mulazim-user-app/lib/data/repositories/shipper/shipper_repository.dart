import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/network/api.dart';
import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/shipper/shipper_detail_model.dart';

/// 配送员数据仓库
class ShipperRepository {
  final ApiClient _apiClient;

  /// 构造函数
  ShipperRepository(this._apiClient);

  /// 获取配送员详情
  ///
  /// [shipperId] - 配送员ID
  /// [orderId] - 订单ID (可选)
  ///
  /// 返回配送员详情数据
  Future<ApiResult<ShipperDetailModel>> getShipperDetail(
    final int shipperId, {
    final int? orderId,
  }) async {
    return await _apiClient.get(
      Api.shipper.detail,
      params: {
        'shipper_id': shipperId,
        if (orderId != null) 'order_id': orderId
      },
      fromJson: (final response, final data) => ShipperDetailModel.fromJson(data),
    );
  }

  /// 获取配送员打赏历史
  ///
  /// [shipperId] - 配送员ID
  /// [limit] - 每页数量
  /// [page] - 页码
  ///
  /// 返回打赏历史数据
  Future<ApiResult<dynamic>> getShipperTipsHistory(
    final int shipperId, {
    final int limit = 30,
    final int page = 1,
  }) async {
    return await _apiClient.get(
      Api.shipper.tipsHistory,
      params: {
        'shipper_id': shipperId,
        'limit': limit,
        'page': page,
      },
      fromJson: (final response, final data) => data,
    );
  }

  /// 获取配送员打赏微信支付参数
  ///
  /// [shipperId] - 配送员ID
  /// [amount] - 打赏金额
  /// [orderId] - 订单ID (可选)
  ///
  /// 返回微信支付参数
  Future<ApiResult<dynamic>> getShipperTipsWechatParams(
    final int shipperId,
    final double amount, {
    final int? orderId,
  }) async {
    return await _apiClient.post(
      Api.shipper.tips,
      data: {
        'shipper_id': shipperId,
        'amount': amount,
        if (orderId != null) 'order_id': orderId,
      },
      fromJson: (final response, final data) => data,
    );
  }
}

/// 配送员仓库提供者
final shipperRepositoryProvider = Provider<ShipperRepository>((final ref) {
  return ShipperRepository(ref.watch(apiClientProvider));
});
