import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 带扇区边缘的卡片
/// 可以在卡片的边缘添加扇区切口效果
class SectorEdgeCard extends StatelessWidget {
  /// 构造函数
  const SectorEdgeCard({
    required this.child,
    this.backgroundColor = Colors.white,
    this.borderColor,
    this.borderWidth = 1.0,
    this.cornerRadius = 8.0,
    this.sectorRadius = 40.0,
    this.sectorOffset = 0.0,
    this.alignment = SectorAlignment.right,
    this.showCenterPoint = false,
    this.centerPointColor,
    this.centerPointSize = 4.0,
    super.key,
  });

  /// 子组件
  final Widget child;

  /// 背景颜色
  final Color backgroundColor;

  /// 边框颜色
  final Color? borderColor;

  /// 边框宽度
  final double borderWidth;

  /// 卡片圆角半径
  final double cornerRadius;

  /// 扇区半径
  final double sectorRadius;

  /// 扇区中心点偏移量
  final double sectorOffset;

  /// 扇区位置
  final SectorAlignment alignment;

  /// 是否显示扇区中心点（用于调试和定位）
  final bool showCenterPoint;

  /// 中心点颜色
  final Color? centerPointColor;

  /// 中心点大小
  final double centerPointSize;

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: [
        // 裁剪区域内容
        ClipPath(
          clipper: _SectorEdgeClipper(
            cornerRadius: cornerRadius,
            sectorRadius: sectorRadius,
            sectorOffset: sectorOffset,
            alignment: alignment,
          ),
          child: CustomPaint(
            painter: _SectorEdgePainter(
              backgroundColor: backgroundColor,
              borderColor: borderColor,
              borderWidth: borderWidth,
              cornerRadius: cornerRadius,
              sectorRadius: sectorRadius,
              sectorOffset: sectorOffset,
              alignment: alignment,
              showCenterPoint: false, // 这里不显示中心点
              centerPointColor: centerPointColor ?? borderColor ?? Colors.red,
              centerPointSize: centerPointSize,
            ),
            child: Container(
              color: backgroundColor,
              child: child,
            ),
          ),
        ),

        // 单独绘制中心点，确保不会被裁剪
        if (showCenterPoint)
          CustomPaint(
            painter: _CenterPointPainter(
              alignment: alignment,
              sectorOffset: sectorOffset,
              centerPointColor: centerPointColor ?? borderColor ?? Colors.red,
              centerPointSize: centerPointSize,
            ),
            size: Size.infinite,
          ),
      ],
    );
  }
}

/// 扇区位置枚举
enum SectorAlignment {
  /// 左侧
  left,

  /// 右侧
  right,

  /// 顶部
  top,

  /// 底部
  bottom
}

/// 扇区边缘裁剪器
class _SectorEdgeClipper extends CustomClipper<Path> {
  _SectorEdgeClipper({
    required this.cornerRadius,
    required this.sectorRadius,
    required this.sectorOffset,
    required this.alignment,
  });

  final double cornerRadius;
  final double sectorRadius;
  final double sectorOffset;
  final SectorAlignment alignment;

  @override
  Path getClip(final Size size) {
    final Path path = Path();
    final double width = size.width;
    final double height = size.height;
    final double realRadius = sectorRadius * 2;

    // 路径起始点（从左上角开始）
    path.moveTo(cornerRadius, 0);

    if (alignment == SectorAlignment.top) {
      // 绘制顶部扇区
      path.lineTo(width / 2 - realRadius / 2, 0);

      // 顶部扇区
      path.arcTo(
        Rect.fromCircle(
          center: Offset(width / 2 + sectorOffset, 0),
          radius: realRadius,
        ),
        math.pi, // 从左边开始
        -math.pi, // 顺时针180度，到右边
        false,
      );

      path.lineTo(width - cornerRadius, 0);
      path.quadraticBezierTo(width, 0, width, cornerRadius);
    } else {
      // 无顶部扇区，普通顶边
      path.lineTo(width - cornerRadius, 0);
      path.quadraticBezierTo(width, 0, width, cornerRadius);
    }

    if (alignment == SectorAlignment.right) {
      // 绘制右侧扇区
      path.lineTo(width, height / 2 - realRadius / 2);

      // 右侧扇区
      path.arcTo(
        Rect.fromCircle(
          center: Offset(width, height / 2 + sectorOffset),
          radius: realRadius,
        ),
        -math.pi / 2, // 从上方开始
        -math.pi, // 顺时针180度，到下方
        false,
      );

      path.lineTo(width, height - cornerRadius);
      path.quadraticBezierTo(width, height, width - cornerRadius, height);
    } else {
      // 无右侧扇区，普通右边
      path.lineTo(width, height - cornerRadius);
      path.quadraticBezierTo(width, height, width - cornerRadius, height);
    }

    if (alignment == SectorAlignment.bottom) {
      // 绘制底部扇区
      path.lineTo(width / 2 + realRadius / 2, height);

      // 底部扇区
      path.arcTo(
        Rect.fromCircle(
          center: Offset(width / 2 + sectorOffset, height),
          radius: realRadius,
        ),
        0, // 从右边开始
        -math.pi, // 顺时针180度，到左边
        false,
      );

      path.lineTo(cornerRadius, height);
      path.quadraticBezierTo(0, height, 0, height - cornerRadius);
    } else {
      // 无底部扇区，普通底边
      path.lineTo(cornerRadius, height);
      path.quadraticBezierTo(0, height, 0, height - cornerRadius);
    }

    if (alignment == SectorAlignment.left) {
      // 绘制左侧扇区
      path.lineTo(0, height / 2 + realRadius / 2);

      // 左侧扇区
      path.arcTo(
        Rect.fromCircle(
          center: Offset(0, height / 2 + sectorOffset),
          radius: realRadius,
        ),
        math.pi / 2, // 从下方开始
        -math.pi, // 顺时针180度，到上方
        false,
      );

      path.lineTo(0, cornerRadius);
      path.quadraticBezierTo(0, 0, cornerRadius, 0);
    } else {
      // 无左侧扇区，普通左边
      path.lineTo(0, cornerRadius);
      path.quadraticBezierTo(0, 0, cornerRadius, 0);
    }

    return path;
  }

  @override
  bool shouldReclip(final CustomClipper<Path> oldClipper) =>
      oldClipper is _SectorEdgeClipper &&
      (oldClipper.cornerRadius != cornerRadius ||
          oldClipper.sectorRadius != sectorRadius ||
          oldClipper.sectorOffset != sectorOffset ||
          oldClipper.alignment != alignment);
}

/// 扇区边缘绘制器
class _SectorEdgePainter extends CustomPainter {
  _SectorEdgePainter({
    required this.backgroundColor,
    required this.borderColor,
    required this.borderWidth,
    required this.cornerRadius,
    required this.sectorRadius,
    required this.sectorOffset,
    required this.alignment,
    required this.showCenterPoint,
    required this.centerPointColor,
    required this.centerPointSize,
  });

  final Color backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final double cornerRadius;
  final double sectorRadius;
  final double sectorOffset;
  final SectorAlignment alignment;
  final bool showCenterPoint;
  final Color centerPointColor;
  final double centerPointSize;

  @override
  void paint(final Canvas canvas, final Size size) {
    if ((borderColor == null || borderWidth <= 0) && !showCenterPoint) return;

    final double width = size.width;
    final double height = size.height;
    final double realRadius = sectorRadius * 2;

    // 根据对齐方式确定扇区中心点位置
    Offset centerPoint;
    switch (alignment) {
      case SectorAlignment.left:
        centerPoint = Offset(0, height / 2 + sectorOffset);
        break;
      case SectorAlignment.right:
        centerPoint = Offset(width, height / 2 + sectorOffset);
        break;
      case SectorAlignment.top:
        centerPoint = Offset(width / 2 + sectorOffset, 0);
        break;
      case SectorAlignment.bottom:
        centerPoint = Offset(width / 2 + sectorOffset, height);
        break;
    }

    // 绘制边框
    if (borderColor != null && borderWidth > 0) {
      final Path path = Path();

      // 路径起始点（从左上角开始）
      path.moveTo(cornerRadius, 0);

      if (alignment == SectorAlignment.top) {
        // 绘制顶部扇区
        path.lineTo(width / 2 - realRadius / 2, 0);

        // 顶部扇区
        path.arcTo(
          Rect.fromCircle(
            center: Offset(width / 2 + sectorOffset, 0),
            radius: realRadius,
          ),
          math.pi, // 从左边开始
          -math.pi, // 顺时针180度，到右边
          false,
        );

        path.lineTo(width - cornerRadius, 0);
        path.quadraticBezierTo(width, 0, width, cornerRadius);
      } else {
        // 无顶部扇区，普通顶边
        path.lineTo(width - cornerRadius, 0);
        path.quadraticBezierTo(width, 0, width, cornerRadius);
      }

      if (alignment == SectorAlignment.right) {
        // 绘制右侧扇区
        path.lineTo(width, height / 2 - realRadius / 2);

        // 右侧扇区
        path.arcTo(
          Rect.fromCircle(
            center: Offset(width, height / 2 + sectorOffset),
            radius: realRadius,
          ),
          -math.pi / 2, // 从上方开始
          -math.pi, // 顺时针180度，到下方
          false,
        );

        path.lineTo(width, height - cornerRadius);
        path.quadraticBezierTo(width, height, width - cornerRadius, height);
      } else {
        // 无右侧扇区，普通右边
        path.lineTo(width, height - cornerRadius);
        path.quadraticBezierTo(width, height, width - cornerRadius, height);
      }

      if (alignment == SectorAlignment.bottom) {
        // 绘制底部扇区
        path.lineTo(width / 2 + realRadius / 2, height);

        // 底部扇区
        path.arcTo(
          Rect.fromCircle(
            center: Offset(width / 2 + sectorOffset, height),
            radius: realRadius,
          ),
          0, // 从右边开始
          -math.pi, // 顺时针180度，到左边
          false,
        );

        path.lineTo(cornerRadius, height);
        path.quadraticBezierTo(0, height, 0, height - cornerRadius);
      } else {
        // 无底部扇区，普通底边
        path.lineTo(cornerRadius, height);
        path.quadraticBezierTo(0, height, 0, height - cornerRadius);
      }

      if (alignment == SectorAlignment.left) {
        // 绘制左侧扇区
        path.lineTo(0, height / 2 + realRadius / 2);

        // 左侧扇区
        path.arcTo(
          Rect.fromCircle(
            center: Offset(0, height / 2 + sectorOffset),
            radius: realRadius,
          ),
          math.pi / 2, // 从下方开始
          -math.pi, // 顺时针180度，到上方
          false,
        );

        path.lineTo(0, cornerRadius);
        path.quadraticBezierTo(0, 0, cornerRadius, 0);
      } else {
        // 无左侧扇区，普通左边
        path.lineTo(0, cornerRadius);
        path.quadraticBezierTo(0, 0, cornerRadius, 0);
      }

      // 绘制边框
      final Paint borderPaint = Paint()
        ..color = borderColor!
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;

      canvas.drawPath(path, borderPaint);
    }

    // 绘制中心点（如果需要）
    if (showCenterPoint) {
      final Paint centerPointPaint = Paint()
        ..color = centerPointColor
        ..style = PaintingStyle.fill;

      // 绘制中心点
      canvas.drawCircle(centerPoint, centerPointSize, centerPointPaint);

      // 可选：绘制十字线以更清晰地标记中心点位置
      final Paint crossPaint = Paint()
        ..color = centerPointColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      // 水平线
      canvas.drawLine(
        Offset(centerPoint.dx - centerPointSize * 2, centerPoint.dy),
        Offset(centerPoint.dx + centerPointSize * 2, centerPoint.dy),
        crossPaint,
      );

      // 垂直线
      canvas.drawLine(
        Offset(centerPoint.dx, centerPoint.dy - centerPointSize * 2),
        Offset(centerPoint.dx, centerPoint.dy + centerPointSize * 2),
        crossPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant final CustomPainter oldDelegate) =>
      oldDelegate is _SectorEdgePainter &&
      (oldDelegate.backgroundColor != backgroundColor ||
          oldDelegate.borderColor != borderColor ||
          oldDelegate.borderWidth != borderWidth ||
          oldDelegate.cornerRadius != cornerRadius ||
          oldDelegate.sectorRadius != sectorRadius ||
          oldDelegate.sectorOffset != sectorOffset ||
          oldDelegate.alignment != alignment ||
          oldDelegate.showCenterPoint != showCenterPoint ||
          oldDelegate.centerPointColor != centerPointColor ||
          oldDelegate.centerPointSize != centerPointSize);
}

/// 专门用于绘制中心点的绘制器
class _CenterPointPainter extends CustomPainter {
  _CenterPointPainter({
    required this.alignment,
    required this.sectorOffset,
    required this.centerPointColor,
    required this.centerPointSize,
  });

  final SectorAlignment alignment;
  final double sectorOffset;
  final Color centerPointColor;
  final double centerPointSize;

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;

    // 根据对齐方式确定扇区中心点位置
    Offset centerPoint;
    switch (alignment) {
      case SectorAlignment.left:
        centerPoint = Offset(0, height / 2 + sectorOffset);
        break;
      case SectorAlignment.right:
        centerPoint = Offset(width, height / 2 + sectorOffset);
        break;
      case SectorAlignment.top:
        centerPoint = Offset(width / 2 + sectorOffset, 0);
        break;
      case SectorAlignment.bottom:
        centerPoint = Offset(width / 2 + sectorOffset, height);
        break;
    }

    final Paint centerPointPaint = Paint()
      ..color = centerPointColor
      ..style = PaintingStyle.fill;

    // 绘制中心点
    canvas.drawCircle(centerPoint, centerPointSize, centerPointPaint);

    // 绘制十字线以更清晰地标记中心点位置
    final Paint crossPaint = Paint()
      ..color = centerPointColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // 水平线
    canvas.drawLine(
      Offset(centerPoint.dx - centerPointSize * 2, centerPoint.dy),
      Offset(centerPoint.dx + centerPointSize * 2, centerPoint.dy),
      crossPaint,
    );

    // 垂直线
    canvas.drawLine(
      Offset(centerPoint.dx, centerPoint.dy - centerPointSize * 2),
      Offset(centerPoint.dx, centerPoint.dy + centerPointSize * 2),
      crossPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) =>
      oldDelegate is _CenterPointPainter &&
      (oldDelegate.alignment != alignment ||
          oldDelegate.sectorOffset != sectorOffset ||
          oldDelegate.centerPointColor != centerPointColor ||
          oldDelegate.centerPointSize != centerPointSize);
}
