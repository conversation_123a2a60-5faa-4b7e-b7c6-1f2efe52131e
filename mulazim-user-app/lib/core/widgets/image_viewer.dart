import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

/// 图片查看器
class ImageViewer extends StatefulWidget {
  /// 构造函数
  const ImageViewer({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
    this.heroTagPrefix = 'image_viewer',
  });

  /// 图片URL列表
  final List<String> imageUrls;

  /// 初始索引
  final int initialIndex;

  /// Hero标签前缀
  final String heroTagPrefix;

  @override
  State<ImageViewer> createState() => _ImageViewerState();
}

class _ImageViewerState extends State<ImageViewer> {
  late int _currentIndex;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 图片画廊
          PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: _buildItem,
            itemCount: widget.imageUrls.length,
            pageController: _pageController,
            onPageChanged: _onPageChanged,
            scrollDirection: Axis.horizontal,
            enableRotation: false,
          ),

          // 关闭按钮
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.h,
            right: 10.w,
            child: IconButton(
              icon: Icon(
                Icons.close,
                color: Colors.white,
                size: 24.r,
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),

          // 图片索引指示器
          if (widget.imageUrls.length > 1)
            Positioned(
              top: MediaQuery.of(context).padding.top + 15.h,
              left: 0,
              right: 0,
              child: Center(
                child: Text(
                  '${_currentIndex + 1}/${widget.imageUrls.length}',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建图片项
  PhotoViewGalleryPageOptions _buildItem(
    final BuildContext context,
    final int index,
  ) {
    final String imageUrl = widget.imageUrls[index];
    return PhotoViewGalleryPageOptions(
      imageProvider: imageUrl.startsWith('http')
          ? CachedNetworkImageProvider(imageUrl)
          : FileImage(File(imageUrl)),
      initialScale: PhotoViewComputedScale.contained,
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,
      heroAttributes: PhotoViewHeroAttributes(
        tag: '${widget.heroTagPrefix}_$index',
      ),
      onTapDown: (final context, final details, final controllerValue) {
        Navigator.of(context).pop();
      },
    );
  }

  /// 页面切换回调
  void _onPageChanged(final int index) {
    setState(() {
      _currentIndex = index;
    });
  }
}

/// 打开图片查看器
Future<void> showImageViewer(
  final BuildContext context, {
  required final List<String> imageUrls,
  final int initialIndex = 0,
  final String heroTagPrefix = 'image_viewer',
}) {
  return Navigator.of(context).push(
    MaterialPageRoute(
      builder: (final context) => ImageViewer(
        imageUrls: imageUrls,
        initialIndex: initialIndex,
        heroTagPrefix: heroTagPrefix,
      ),
    ),
  );
}
