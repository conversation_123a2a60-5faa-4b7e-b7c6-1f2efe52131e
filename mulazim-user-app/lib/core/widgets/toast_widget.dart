import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Toast 提示组件 - 对微信小程序toast组件的封装
class ToastWidget {
  /// 显示toast提示 - 使用更高的层级以在Dialog之上显示
  static void show(final String message, {final int duration = 1500}) {
    BotToast.showText(
      text: message,
      duration: Duration(milliseconds: duration),
      contentColor: Colors.black.withOpacity(0.8),
      align: Alignment.center,
      // 设置更高的层级，确保在Dialog之上显示
      clickClose: false,
      crossPage: true,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 10.h,
      ),
      borderRadius: BorderRadius.circular(5.r),
      textStyle: TextStyle(
        fontSize: 14.sp,
        color: Colors.white,
      ),
    );
  }

  /// 显示成功toast - 使用更高的层级
  static void showSuccess(final String message, {final int duration = 1500}) {
    BotToast.showText(
      text: message,
      duration: Duration(milliseconds: duration),
      align: Alignment.center,
      contentColor: const Color(0xFF07C160),
      clickClose: false,
      crossPage: true,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 10.h,
      ),
      borderRadius: BorderRadius.circular(5.r),
      textStyle: TextStyle(
        fontSize: 14.sp,
        color: Colors.white,
      ),
    );
  }

  /// 显示错误toast - 使用更高的层级
  static void showError(final String message, {final int duration = 1500}) {
    BotToast.showText(
      text: message,
      duration: Duration(milliseconds: duration),
      contentColor: const Color(0xFFFA5151),
      align: Alignment.center,
      clickClose: false,
      crossPage: true,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 15.w,
        vertical: 10.h,
      ),
      borderRadius: BorderRadius.circular(5.r),
      textStyle: TextStyle(
        fontSize: 14.sp,
        color: Colors.white,
      ),
    );
  }

  /// 通用的BotToast显示方法 - 支持HTML标签和事件穿透
  static void _showBotToast(
    final String message,
    final Color backgroundColor, {
    final int duration = 1500,
  }) {
    // 检查消息是否包含HTML标签
    final bool hasHtmlTags = message.contains('<') && message.contains('>');

    BotToast.showCustomText(
      duration: Duration(milliseconds: duration),
      toastBuilder: (void Function() cancelFunc) {
        return IgnorePointer(
          ignoring: true, // 确保事件穿透
          child: Material(
            color: Colors.transparent,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              padding: EdgeInsets.symmetric(
                horizontal: 10.w,
                vertical: 10.h,
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(5.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: hasHtmlTags
                  ? Html(
                      data: message,
                      style: {
                        "body": Style(
                          fontSize: FontSize(16.sp),
                          color: Colors.white,
                          textAlign: TextAlign.center,
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                          fontWeight: FontWeight.bold,
                        ),
                        "p": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                        "span": Style(
                          fontSize: FontSize(16.sp),
                          color: Colors.red,
                          textAlign: TextAlign.center,
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                          fontWeight: FontWeight.w600,
                        ),
                        "div": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                      },
                    )
                  : Text(
                      message,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        );
      },
      align: Alignment.center,
      clickClose: false,
      crossPage: true,
      ignoreContentClick: true,
    );
  }

  /// 显示富文本toast - 支持HTML标签和事件穿透
  static void showRich(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    _showBotToast(
      message,
      Colors.black.withOpacity(0.8),
      duration: duration,
    );
  }

  /// 显示成功富文本toast - 支持HTML标签和事件穿透
  static void showSuccessRich(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    _showBotToast(
      message,
      const Color(0xFF07C160),
      duration: duration,
    );
  }

  /// 显示错误富文本toast - 支持HTML标签和事件穿透
  static void showErrorRich(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    _showBotToast(
      message,
      const Color(0xFFFA5151),
      duration: duration,
    );
  }

  /// 智能显示toast - 优先使用富文本方式
  static void smartShow(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    // 检查是否包含HTML标签，如果包含则使用富文本显示
    if (message.contains('<') && message.contains('>')) {
      showRich(message, duration: duration, context: context);
    } else {
      show(message, duration: duration);
    }
  }

  /// 智能显示成功toast - 优先使用富文本方式
  static void smartShowSuccess(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    // 检查是否包含HTML标签，如果包含则使用富文本显示
    if (message.contains('<') && message.contains('>')) {
      showSuccessRich(message, duration: duration, context: context);
    } else {
      showSuccess(message, duration: duration);
    }
  }

  /// 智能显示错误toast - 优先使用富文本方式
  static void smartShowError(
    final String message, {
    final int duration = 1500,
    final BuildContext? context,
  }) {
    // 检查是否包含HTML标签，如果包含则使用富文本显示
    if (message.contains('<') && message.contains('>')) {
      showErrorRich(message, duration: duration, context: context);
    } else {
      showError(message, duration: duration);
    }
  }
}
