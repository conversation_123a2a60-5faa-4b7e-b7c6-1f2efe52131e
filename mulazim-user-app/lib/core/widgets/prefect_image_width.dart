import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 完美图片宽度
class PrefectImageWidth extends StatelessWidget {
  /// 图片适配
  BoxFit? fit = BoxFit.cover;

  /// 构造函数
  PrefectImageWidth({
    super.key,
    required this.imageUrl,
    required this.width,
    this.fit,
  });

  ///图片地址
  String imageUrl;

  ///图片宽度
  double width;

  ///主要布局
  @override
  Widget build(final BuildContext context) {
    return Image(
      image:
          CachedNetworkImageProvider(imageUrl, maxWidth: (width * 4).toInt()),
      fit: BoxFit.fill,
      width: width,
      errorBuilder: (final context, final error, final stackTrace) {
        return Image.asset(
          'assets/images/empty.png',
          fit: fit,
          width: width,
        );
      },
      loadingBuilder: (final context, final child, final loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          width: width,
          alignment: Alignment.center,
          child: CircularProgressIndicator(
            color: AppColors.baseGreenColor,
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null,
          ),
        );
      },
    );
  }
}
