import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 支持RTL模式的折角卡片
class FoldedCornerCard extends StatelessWidget {
  /// 构造函数
  const FoldedCornerCard({
    required this.child,
    this.backgroundColor = Colors.white,
    this.foldColor,
    this.foldSize = 20.0,
    this.borderWidth = 1.0,
    this.borderColor,
    this.isRtl = false,
    super.key,
  });

  /// 子组件
  final Widget child;

  /// 背景颜色
  final Color backgroundColor;

  /// 切口颜色
  final Color? foldColor;

  /// 边框颜色
  final Color? borderColor;

  /// 切口半径
  final double foldSize;

  /// 边框宽度
  final double borderWidth;

  /// 是否为RTL模式(从右到左)
  final bool isRtl;

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: [
        // 裁剪后的内容
        ClipPath(
          clipper: _RtlCircleCornerClipper(
            foldSize: foldSize,
            isRtl: isRtl,
          ),
          child: child,
        ),

        // 圆形切口的边框
        if (borderWidth > 0 && (borderColor != null || foldColor != null))
          Positioned.fill(
            child: IgnorePointer(
              child: CustomPaint(
                painter: _RtlCircleBorderPainter(
                  foldSize: foldSize,
                  borderWidth: borderWidth,
                  borderColor: borderColor ?? foldColor ?? Colors.grey,
                  isRtl: isRtl,
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// 支持RTL的圆形切口裁剪器
class _RtlCircleCornerClipper extends CustomClipper<Path> {
  _RtlCircleCornerClipper({
    required this.foldSize,
    required this.isRtl,
  });

  final double foldSize;
  final bool isRtl;

  @override
  Path getClip(Size size) {
    final Path path = Path();

    // 创建基本矩形路径（代表整个卡片）
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    if (isRtl) {
      // RTL模式 - 右上角圆形切口
      path.addOval(
          Rect.fromCircle(center: Offset(size.width, 0), radius: foldSize));

      // RTL模式 - 右下角圆形切口
      path.addOval(Rect.fromCircle(
          center: Offset(size.width, size.height), radius: foldSize));
    } else {
      // LTR模式 - 左上角圆形切口
      path.addOval(Rect.fromCircle(center: Offset(0, 0), radius: foldSize));

      // LTR模式 - 左下角圆形切口
      path.addOval(
          Rect.fromCircle(center: Offset(0, size.height), radius: foldSize));
    }

    // 使用填充规则 evenOdd，让圆形区域变成"洞"
    path.fillType = PathFillType.evenOdd;

    return path;
  }

  @override
  bool shouldReclip(_RtlCircleCornerClipper oldClipper) =>
      oldClipper.foldSize != foldSize || oldClipper.isRtl != isRtl;
}

/// 支持RTL的圆形边框绘制器
class _RtlCircleBorderPainter extends CustomPainter {
  _RtlCircleBorderPainter({
    required this.foldSize,
    required this.borderWidth,
    required this.borderColor,
    required this.isRtl,
  });

  final double foldSize;
  final double borderWidth;
  final Color borderColor;
  final bool isRtl;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    // 保存画布状态
    canvas.save();

    if (isRtl) {
      // RTL模式 - 右上角圆形边框
      _drawIntersectionArc(
          canvas, Offset(size.width, 0), size, foldSize, borderPaint,
          isRtl: true);

      // RTL模式 - 右下角圆形边框
      _drawIntersectionArc(
          canvas, Offset(size.width, size.height), size, foldSize, borderPaint,
          isRtl: true);
    } else {
      // LTR模式 - 左上角圆形边框
      _drawIntersectionArc(canvas, Offset.zero, size, foldSize, borderPaint,
          isRtl: false);

      // LTR模式 - 左下角圆形边框
      _drawIntersectionArc(
          canvas, Offset(0, size.height), size, foldSize, borderPaint,
          isRtl: false);
    }

    // 恢复画布状态
    canvas.restore();
  }

  void _drawIntersectionArc(
      Canvas canvas, Offset center, Size size, double radius, Paint paint,
      {required bool isRtl}) {
    // 根据圆心位置和RTL模式确定角度范围
    double startAngle, sweepAngle;

    if (isRtl) {
      // RTL模式
      if (center.dy.abs() < 0.1) {
        // 右上角
        startAngle = math.pi / 2; // 从下方开始
        sweepAngle = math.pi / 2; // 90度，到左侧
      } else {
        // 右下角
        startAngle = math.pi; // 从左侧开始
        sweepAngle = math.pi / 2; // 90度，到上方
      }
    } else {
      // LTR模式
      if (center.dy.abs() < 0.1) {
        // 左上角
        startAngle = 0; // 从右侧开始
        sweepAngle = math.pi / 2; // 90度，到下方
      } else {
        // 左下角
        startAngle = -math.pi / 2; // 从上方开始
        sweepAngle = math.pi / 2; // 90度，到右侧
      }
    }

    // 绘制部分圆弧
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant _RtlCircleBorderPainter oldDelegate) {
    return oldDelegate.foldSize != foldSize ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.isRtl != isRtl;
  }
}
