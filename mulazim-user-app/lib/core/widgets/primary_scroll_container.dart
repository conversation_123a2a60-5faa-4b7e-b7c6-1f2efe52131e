import 'package:flutter/cupertino.dart';

/// 主滚动容器组件
///
/// 用于管理TabBarView中各个Tab页面的滚动状态，解决NestedScrollView中
/// 多个Tab页面滚动同步的问题。通过ScrollControllerWrapper代理模式
/// 实现每个Tab页面的独立滚动控制。
class PrimaryScrollContainer extends StatefulWidget {
  /// 子组件
  final Widget child;

  /// 初始显示状态，默认为false，避免初始化时的滚动同步问题
  final bool initialShow;

  /// 构造函数
  ///
  /// @param key 全局键，用于获取PrimaryScrollContainerState
  /// @param child 要包装的子组件
  /// @param initialShow 初始显示状态
  PrimaryScrollContainer(
    final GlobalKey<PrimaryScrollContainerState> key,
    this.child, {
    this.initialShow = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return PrimaryScrollContainerState();
  }
}

/// PrimaryScrollContainer的状态管理类
///
/// 负责管理ScrollControllerWrapper的生命周期和状态变化
class PrimaryScrollContainerState extends State<PrimaryScrollContainer> {
  /// 滚动控制器包装器，用于代理真实的ScrollController
  late ScrollControllerWrapper _scrollController;

  /// 获取滚动控制器
  ///
  /// 从InheritedWidget中获取PrimaryScrollController，并将其控制器
  /// 设置到ScrollControllerWrapper中，实现代理模式
  get scrollController {
    final PrimaryScrollController? primaryScrollController =
        context.dependOnInheritedWidgetOfExactType<PrimaryScrollController>();
    if (primaryScrollController != null &&
        primaryScrollController.controller != null) {
      _scrollController.inner = primaryScrollController.controller!;
    }
    return _scrollController;
  }

  @override
  void initState() {
    print('initstate');
    // 初始化滚动控制器包装器，设置初始显示状态
    _scrollController =
        ScrollControllerWrapper(initialShow: widget.initialShow);
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    // 构建PrimaryScrollControllerWrapper，将滚动控制器传递给子组件
    return PrimaryScrollControllerWrapper(
      child: widget.child,
      scrollController: scrollController,
    );
  }

  /// 页面切换时的回调方法
  ///
  /// @param show 是否显示当前页面
  void onPageChange(final bool show) {
    _scrollController.onAttachChange(show);
  }
}

/// PrimaryScrollController的包装器
///
/// 实现PrimaryScrollController接口，通过InheritedWidget模式
/// 将滚动控制器传递给子组件
class PrimaryScrollControllerWrapper extends InheritedWidget
    implements PrimaryScrollController {
  /// 滚动控制器
  final ScrollController scrollController;

  /// 构造函数
  const PrimaryScrollControllerWrapper({
    super.key,
    required final Widget child,
    required this.scrollController,
  }) : super(child: child);

  @override
  Type get runtimeType => PrimaryScrollController;

  @override
  ScrollController get controller => scrollController;

  /// 自动继承滚动行为的平台列表
  ///
  /// 定义在哪些平台上自动继承滚动行为，这里支持所有主要平台
  @override
  Set<TargetPlatform> get automaticallyInheritForPlatforms => {
        TargetPlatform.android,
        TargetPlatform.iOS,
        TargetPlatform.macOS,
        TargetPlatform.windows,
        TargetPlatform.linux
      };

  /// 滚动方向
  ///
  /// 定义滚动方向，默认为垂直滚动
  @override
  Axis? get scrollDirection => Axis.vertical;

  @override
  bool updateShouldNotify(final PrimaryScrollControllerWrapper oldWidget) =>
      controller != oldWidget.controller;
}

/// 滚动控制器包装器
///
/// 实现ScrollController接口，通过代理模式控制真实的ScrollController。
/// 主要用于解决NestedScrollView中多个Tab页面滚动同步的问题。
/// 当Tab切换时，可以控制是否将滚动位置附加到真实的ScrollController上。
class ScrollControllerWrapper implements ScrollController {
  /// 静态计数器，用于生成唯一的实例标识
  static int a = 1;

  /// 内部真实的ScrollController
  late ScrollController inner;

  /// 实例标识码
  int code = a++;

  /// 被拦截的滚动位置，当showing为false时，新的滚动位置会被拦截
  ScrollPosition? interceptedAttachPosition;

  /// 最后附加的滚动位置
  ScrollPosition? lastPosition;

  /// 是否显示当前页面，控制是否允许滚动位置附加
  bool showing;

  /// 构造函数
  ///
  /// @param initialShow 初始显示状态，默认为false避免初始化时的滚动同步
  ScrollControllerWrapper({final bool initialShow = false}) : showing = initialShow;

  @override
  void addListener(final VoidCallback listener) => inner.addListener(listener);

  /// 动画滚动到指定位置
  @override
  Future<void> animateTo(
    final double offset, {
    final Duration? duration,
    final Curve? curve,
  }) =>
      inner.animateTo(offset,
          duration: duration ?? const Duration(milliseconds: 300),
          curve: curve ?? Curves.ease);

  /// 附加滚动位置
  ///
  /// 当showing为true时，正常附加滚动位置；
  /// 当showing为false时，将滚动位置保存到interceptedAttachPosition中
  @override
  void attach(final ScrollPosition position) {
    print('{$code}:attach start {$showing}');
    if (position == interceptedAttachPosition) print("attach by inner");
    position.hasListeners;
    print('{$code}:attach end {$showing}');
    if (inner.positions.contains(position)) return;
    if (showing) {
      // 显示状态：正常附加滚动位置
      inner.attach(position);
      lastPosition = position;
    } else {
      // 隐藏状态：拦截滚动位置
      interceptedAttachPosition = position;
    }
  }

  /// 分离滚动位置
  ///
  /// @param position 要分离的滚动位置
  /// @param fake 是否为假分离（用于内部状态管理）
  @override
  void detach(final ScrollPosition position, {final bool fake = false}) {
    assert(() {
      print('{$code}:detach start {$showing}');
      return true;
    }.call());
    if (fake) print("detach is innner");
    if (inner.positions.contains(position)) {
      inner.detach(position);
    }
    if (position == interceptedAttachPosition && !fake) {
      print('{$code}:set null {$showing}');
      interceptedAttachPosition = null;
    }
    if (position == lastPosition && !fake) {
      print('{$code}:set null {$showing}');
      lastPosition = null;
    }
    if (fake) {
      interceptedAttachPosition = position;
    }
    assert(() {
      print('{$code}:detach end {$showing}');
      return true;
    }.call());
  }

  /// 页面显示状态变化时的回调
  ///
  /// @param b 是否显示页面
  void onAttachChange(final bool b) {
    print('{$code}:change{$b}');
    showing = b;
    if (!showing) {
      // 隐藏页面时：假分离最后的滚动位置
      if (lastPosition != null) detach(lastPosition!, fake: true);
    } else {
      // 显示页面时：附加被拦截的滚动位置
      if (interceptedAttachPosition != null) attach(interceptedAttachPosition!);
    }
  }

  @override
  ScrollPosition createScrollPosition(final ScrollPhysics physics,
          final ScrollContext context, final ScrollPosition? oldPosition) =>
      inner.createScrollPosition(physics, context, oldPosition);

  @override
  void debugFillDescription(final List<String> description) =>
      inner.debugFillDescription(description);

  @override
  String get debugLabel => inner.debugLabel ?? 'ScrollControllerWrapper';

  @override
  void dispose() => inner.dispose();

  @override
  bool get hasClients => inner.hasClients;

  @override
  bool get hasListeners => inner.hasListeners;

  @override
  double get initialScrollOffset => inner.initialScrollOffset;

  @override
  void jumpTo(final double value) => inner.jumpTo(value);

  @override
  bool get keepScrollOffset => inner.keepScrollOffset;

  @override
  void notifyListeners() => inner.notifyListeners();

  @override
  double get offset => inner.offset;

  @override
  ScrollPosition get position => inner.position;

  @override
  Iterable<ScrollPosition> get positions => inner.positions;

  @override
  void removeListener(final VoidCallback listener) => inner.removeListener(listener);

  @override
  int get hashCode => inner.hashCode;

  @override
  bool operator ==(final Object other) {
    return hashCode == other.hashCode;
  }

  @override
  void Function(ScrollPosition)? get onAttach => inner.onAttach;

  @override
  void Function(ScrollPosition)? get onDetach => inner.onDetach;
}
