import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 自定义应用栏
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题
  final String title;

  /// 背景色
  final Color? backgroundColor;

  /// 标题颜色
  final Color? titleColor;

  /// 返回按钮回调
  final VoidCallback? onBackPressed;

  /// 右侧操作按钮
  final List<Widget>? actions;

  /// 构造函数
  const CustomAppBar({
    super.key,
    required this.title,
    this.backgroundColor,
    this.titleColor,
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(final BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? AppColors.primary,
      centerTitle: true,
      scrolledUnderElevation: 0, //可以避免背景穿透
      // leading: context.canPop()
      //     ? IconButton(
      //         icon: Icon(
      //           Icons.arrow_back_ios,
      //           color: titleColor ?? Colors.white,
      //           size: 20.r,
      //         ),
      //         onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      //       )
      //     : null,
      title: Text(
        title,
        style: TextStyle(
          fontSize: 20.sp,
          color: titleColor ?? Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
