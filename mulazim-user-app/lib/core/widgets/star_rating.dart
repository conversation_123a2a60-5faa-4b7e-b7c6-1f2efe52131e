import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';

/// 星级评分组件
class StarRating extends StatelessWidget {
  /// 评分，满分为5
  final double rating;

  /// 星星大小
  final double size;

  /// 是否可以点击
  final bool isClickable;

  /// 点击回调
  final Function(double)? onRatingChanged;

  /// 是否隐藏提示文字
  final bool hideTip;

  /// 星星间隔 (对应微信小程序 8rpx)
  final double gap;

  /// 星星选中颜色
  final Color? selectedColor;

  /// 星星未选中颜色
  final Color? unselectedColor;

  /// 提示文字颜色
  final Color? tipColor;

  /// 星星评价提示文字
  static const List<String> stars = ['特差', '差', '一般', '满意', '特别满意'];
  static const List<String> starsUg = ['بەك ناچار', 'ناچار', 'نورمال', 'ياخشى', 'بەك ياخشى'];

  /// 构造函数
  const StarRating({
    super.key,
    required this.rating,
    this.size = 24.0,
    this.isClickable = false,
    this.onRatingChanged,
    this.hideTip = true,
    this.gap = 4.0, // 默认间距为4.w (8rpx/2)
    this.selectedColor,
    this.unselectedColor,
    this.tipColor,
  });

  @override
  Widget build(final BuildContext context) {
    final isRtl = Localizations.localeOf(context).languageCode == 'en';
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...List.generate(5, (final index) {
          final starWidget = _buildStar(index);

          // 如果可点击，包装一个GestureDetector
          if (isClickable) {
            return GestureDetector(
              onTap: () {
                if (onRatingChanged != null) {
                  onRatingChanged!(index + 1.0);
                }
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: gap.w / 2),
                child: starWidget,
              ),
            );
          }

          return Padding(
            padding: EdgeInsets.symmetric(horizontal: gap.w / 2),
            child: starWidget,
          );
        }),

        // 如果不隐藏提示，并且评分有效，显示评分描述
        if (!hideTip && rating > 0 && rating <= 5)
          Padding(
            padding: EdgeInsets.only(
              left: isRtl ? 0 : 8.w,
              right: isRtl ? 8.w : 0,
            ),
            child: Text(
              isRtl ? starsUg[rating.ceil() - 1] : stars[rating.ceil() - 1],
              style: TextStyle(
                fontSize: 14.sp, // 28rpx / 2
                color: tipColor ?? AppColors.textSecondaryColor,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建单个星星
  Widget _buildStar(int index) {
    // 确定应该显示的图片路径
    final String imagePath;

    if (index < rating) {
      // 选中的星星
      imagePath = 'assets/images/evaluate/stars_selected.png';
    } else {
      // 未选中的星星
      imagePath = 'assets/images/evaluate/stars.png';
    }

    return Image.asset(
      imagePath,
      width: size.w,
      height: size.h,
      fit: BoxFit.contain,
    );
  }
}
