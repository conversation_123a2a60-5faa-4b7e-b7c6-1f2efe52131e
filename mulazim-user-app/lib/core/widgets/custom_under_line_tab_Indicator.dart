import 'package:flutter/material.dart';

/// 自定义下划线Tab指示器
/// 继承自UnderlineTabIndicator，主要修改了下划线的端点样式为圆角
/// 用于TabBar的indicator属性，实现自定义样式的选中标识
class CustomUnderlineTabIndicator extends Decoration {
  /// 创建一个下划线样式的选中标签指示器
  /// 
  /// [borderSide] 定义下划线的样式（颜色和宽度）
  /// [insets] 定义下划线相对于标签边界的内边距
  const CustomUnderlineTabIndicator({
    this.borderSide = const BorderSide(width: 2.0, color: Colors.white),
    this.insets = EdgeInsets.zero,
  })  : assert(borderSide != null),
        assert(insets != null);

  /// 下划线的样式配置
  /// 包含颜色和宽度等属性
  final BorderSide borderSide;

  /// 下划线的内边距配置
  /// 用于调整下划线相对于标签的位置
  /// 可以通过[TabBar.indicatorSize]属性来控制指示器的范围：
  /// - [TabBarIndicatorSize.label]: 与文本等宽
  /// - [TabBarIndicatorSize.tab]: 与整个标签等宽
  final EdgeInsetsGeometry insets;

  /// 处理装饰的渐变效果（从其他装饰过渡到当前装饰）
  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is UnderlineTabIndicator) {
      return UnderlineTabIndicator(
        borderSide: BorderSide.lerp(a.borderSide, borderSide, t),
        insets: EdgeInsetsGeometry.lerp(a.insets, insets, t)!,
      );
    }
    return super.lerpFrom(a, t);
  }

  /// 处理装饰的渐变效果（从当前装饰过渡到其他装饰）
  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is UnderlineTabIndicator) {
      return UnderlineTabIndicator(
        borderSide: BorderSide.lerp(borderSide, b.borderSide, t),
        insets: EdgeInsetsGeometry.lerp(insets, b.insets, t)!,
      );
    }
    return super.lerpTo(b, t);
  }

  /// 创建用于绘制下划线的画笔
  @override
  _UnderlinePainter createBoxPainter([VoidCallback? onChanged]) {
    return _UnderlinePainter(this, onChanged);
  }

  /// 计算指示器的矩形区域
  /// [rect] 标签的边界矩形
  /// [textDirection] 文本方向
  Rect _indicatorRectFor(Rect rect, TextDirection textDirection) {
    assert(rect != null);
    assert(textDirection != null);
    final Rect indicator = insets.resolve(textDirection).deflateRect(rect);
    return Rect.fromLTWH(
      indicator.left,
      indicator.bottom - borderSide.width,
      indicator.width,
      borderSide.width,
    );
  }

  /// 获取裁剪路径
  @override
  Path getClipPath(Rect rect, TextDirection textDirection) {
    return Path()..addRect(_indicatorRectFor(rect, textDirection));
  }
}

/// 下划线绘制器
/// 负责实际绘制下划线指示器
class _UnderlinePainter extends BoxPainter {
  _UnderlinePainter(this.decoration, VoidCallback? onChanged)
      : assert(decoration != null),
        super(onChanged);

  final CustomUnderlineTabIndicator? decoration;

  BorderSide? get borderSide => decoration?.borderSide;

  EdgeInsetsGeometry? get insets => decoration?.insets;

  /// 实际的绘制方法
  /// 在这里实现了圆角下划线的绘制
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration? configuration) {
    assert(configuration != null);
    assert(configuration!.size != null);
    final Rect rect = offset & configuration!.size!;
    final TextDirection textDirection = configuration.textDirection!;
    final Rect indicator = decoration!._indicatorRectFor(rect, textDirection)
        .deflate(decoration!.borderSide.width / 2.0);
    final Paint paint = decoration!.borderSide.toPaint();
    paint.strokeWidth = 5;
    paint.strokeCap = StrokeCap.round; // 设置线条端点为圆角
    canvas.drawLine(indicator.bottomLeft, indicator.bottomRight, paint);
  }
}