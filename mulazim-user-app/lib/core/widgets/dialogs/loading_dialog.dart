import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:bot_toast/bot_toast.dart';
import 'dart:async';

/// 加载对话框
/// 提供一个至少显示0.4秒的加载动画对话框
class LoadingDialog {
  /// 单例实例
  static final LoadingDialog _instance = LoadingDialog._internal();

  /// 工厂构造函数
  factory LoadingDialog() => _instance;

  /// 私有构造函数
  LoadingDialog._internal();

  /// BotToast取消函数
  CancelFunc? _cancelFunc;

  /// 对话框显示时间
  DateTime? _showTime;

  /// 是否正在显示
  bool _isVisible = false;
  static const Duration minDuration = Duration(milliseconds: 400);

  /// 显示加载对话框
  /// [message] 显示的文本消息(可选)
  Future<void> show({
    final String? message,
  }) async {
    try {
      // 如果对话框已经在显示，直接返回
      if (_isVisible) return;

      // 记录显示时间和状态
      _showTime = DateTime.now();
      _isVisible = true;

      // 使用BotToast显示对话框
      _cancelFunc = BotToast.showCustomLoading(
        clickClose: false,
        backButtonBehavior: BackButtonBehavior.none,
        toastBuilder: (final CancelFunc cancelFunc) {
          return _LoadingDialogWidget(message: message);
        },
      );
    } catch (e) {
      // 如果显示失败，重置状态
      _isVisible = false;
      _showTime = null;
      _cancelFunc = null;
      print('LoadingDialog.show() 错误: $e');
    }
  }

  /// 隐藏加载对话框
  /// 确保至少显示0.4秒
  Future<void> hide() async {
    try {
      // 如果对话框未显示，直接返回
      if (!_isVisible || _cancelFunc == null || _showTime == null) return;

      // 计算已显示的时间
      final Duration elapsed = DateTime.now().difference(_showTime!);

      // 如果显示时间不足，等待剩余时间
      if (elapsed < minDuration) {
        await Future.delayed(minDuration - elapsed);
      }

      // 关闭对话框
      _cancelFunc?.call();

      // 重置状态
      _cancelFunc = null;
      _showTime = null;
      _isVisible = false;
    } catch (e) {
      // 如果隐藏失败，强制重置状态
      _cancelFunc = null;
      _showTime = null;
      _isVisible = false;
      print('LoadingDialog.hide() 错误: $e');
    }
  }

  /// 强制隐藏加载对话框（不等待最小显示时间）
  void forceHide() {
    try {
      if (_cancelFunc != null) {
        _cancelFunc?.call();
      }
      // 重置状态
      _cancelFunc = null;
      _showTime = null;
      _isVisible = false;
    } catch (e) {
      // 强制重置状态
      _cancelFunc = null;
      _showTime = null;
      _isVisible = false;
      print('LoadingDialog.forceHide() 错误: $e');
    }
  }

  /// 检查对话框是否正在显示
  bool get isVisible => _isVisible;
}

/// 加载对话框小部件
class _LoadingDialogWidget extends StatefulWidget {
  /// 消息文本
  final String? message;

  const _LoadingDialogWidget({this.message});

  @override
  State<_LoadingDialogWidget> createState() => _LoadingDialogWidgetState();
}

class _LoadingDialogWidgetState extends State<_LoadingDialogWidget> {
  /// 是否显示关闭按钮
  bool _showCloseButton = false;

  /// 定时器
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // 5秒后显示关闭按钮
    _timer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _showCloseButton = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final Widget imageWidget = CachedNetworkImage(
      imageUrl: 'https://acdn.mulazim.com/wechat_mini/loading.gif',
      width: 120.w,
      height: 120.w,
      fit: BoxFit.fill,
      placeholder: (final context, final url) {
        return Image.asset(
          'assets/images/app/loading.gif',
          width: 120.w,
          height: 120.w,
          errorBuilder: (context, error, stackTrace) {
            // 如果本地图片也加载失败，显示默认的CircularProgressIndicator
            return SizedBox(
              width: 120.w,
              height: 120.w,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        );
      },
      errorWidget: (final context, final url, final error) {
        return Image.asset(
          'assets/images/app/loading.gif',
          width: 120.w,
          height: 120.w,
          errorBuilder: (context, error, stackTrace) {
            // 如果本地图片也加载失败，显示默认的CircularProgressIndicator
            return SizedBox(
              width: 120.w,
              height: 120.w,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        );
      },
    );

    return Material(
      color: Colors.transparent,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(10.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  imageWidget,
                  // 如果有消息文本，显示消息
                  if (widget.message != null && widget.message!.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Text(
                        widget.message!,
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
              Positioned(
                top: 0.h,
                right: 0.w,
                child: // 5秒后显示关闭按钮（带动画效果）
                    AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: _showCloseButton
                      ? Padding(
                          key: const ValueKey('close_button'),
                          padding: EdgeInsets.only(top: 0.h),
                          child: InkWell(
                            onTap: () {
                              // 点击关闭按钮时强制关闭对话框
                              LoadingDialog().forceHide();
                            },
                            child: Container(
                              width: 25.w,
                              height: 25.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20.r),
                                border: Border.all(
                                  color: Colors.grey.shade600,
                                  width: 1.w,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.close,
                                    size: 16.sp,
                                    color: Colors.grey.shade600,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
