import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 通用确认对话框
///
/// 用于显示需要用户确认的信息，包含确认和取消两个按钮
class LoginConfirmDialog extends StatelessWidget {
  /// 确认回调
  final VoidCallback? onConfirm;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 构造函数
  const LoginConfirmDialog({
    super.key,
    this.onConfirm,
    this.onCancel,
  });

  /// 显示确认对话框
  ///
  /// 返回Future<bool?>，true表示用户点击了确认，false表示用户点击了取消，null表示对话框被关闭
  static Future<bool?> show(
    final BuildContext context, {
    final VoidCallback? onConfirm,
    final VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (final context) => LoginConfirmDialog(
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    // 默认文本样式
    final defaultContentStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColors.dialogTextColor,
      fontFamily: AppConstants.mainFont,
      height: 1.3,
    );

    // 默认标题样式
    final defaultTitleStyle = TextStyle(
      fontSize: 23.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.dialogTextColor,
      fontFamily: AppConstants.mainFont,
    );

    // 创建操作按钮组件
    final actionsWidget = IntrinsicHeight(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 确认按钮
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                if (onConfirm != null) {
                  onConfirm!();
                }
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero,
                ),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                S.current.please_login,
                style: TextStyle(
                  fontSize: 19.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          // 垂直分割线
          VerticalDivider(
            width: 1,
            thickness: 0.5,
            color: AppColors.dialogDividerColor,
          ),
          // 取消按钮
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
                if (onCancel != null) {
                  onCancel!();
                }
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero,
                ),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                S.current.dialog_text_no,
                style: TextStyle(
                  fontSize: 19.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dialogCancelColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // 默认实现
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 40.w),
      contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 20.h),
          Transform.scale(
            scale: 1.5,
            child: Image.asset(
              "assets/images/basic/mulazim-login-tag.png",
              height: 150.h,
              fit: BoxFit.cover,
            ),
          ),
          Text(
            S.current.is_login_text,
            style: defaultContentStyle,
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 20.h),
          // 使用Padding代替Container并设置负的padding来实现分割线延伸到边缘
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 0),
            child: Divider(
              height: 0,
              thickness: 0.5,
              color: AppColors.dialogDividerColor,
              endIndent: 0,
              indent: 0,
            ),
          ),
          // 底部操作按钮区域
          actionsWidget,
        ],
      ),
    );
  }
}
