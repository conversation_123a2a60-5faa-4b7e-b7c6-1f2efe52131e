import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 通用确认对话框
///
/// 用于显示需要用户确认的信息，包含确认和取消两个按钮
class ConfirmDialog extends StatelessWidget {
  /// 对话框标题
  final String? title;

  /// 对话框内容，可以是字符串或Widget
  final dynamic content;

  /// 确认按钮文本，默认为"确定"
  final String? confirmText;

  /// 取消按钮文本，默认为"取消"
  final String? cancelText;

  /// 确认按钮颜色
  final Color? confirmColor;

  /// 取消按钮颜色
  final Color? cancelColor;

  /// 对话框内容文本样式，仅当content为String时生效
  final TextStyle? contentStyle;

  /// 对话框标题文本样式
  final TextStyle? titleStyle;

  /// 确认回调
  final VoidCallback? onConfirm;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 构造函数
  const ConfirmDialog({
    super.key,
    this.title,
    required this.content,
    this.confirmText,
    this.cancelText,
    this.confirmColor,
    this.cancelColor,
    this.contentStyle,
    this.titleStyle,
    this.onConfirm,
    this.onCancel,
  });

  /// 显示确认对话框
  ///
  /// 返回Future<bool?>，true表示用户点击了确认，false表示用户点击了取消，null表示对话框被关闭
  static Future<bool?> show(
    final BuildContext context, {
    final String? title,
    required final dynamic content,
    final String? confirmText,
    final String? cancelText,
    final Color? confirmColor,
    final Color? cancelColor,
    final TextStyle? contentStyle,
    final TextStyle? titleStyle,
    final VoidCallback? onConfirm,
    final VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (final context) => ConfirmDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        confirmColor: confirmColor,
        cancelColor: cancelColor,
        contentStyle: contentStyle,
        titleStyle: titleStyle,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }

  @override
  Widget build(final BuildContext context) {
    // 默认文本样式
    final defaultContentStyle = TextStyle(
      fontSize: 18.sp,
      color: AppColors.dialogTextColor,
      fontFamily: AppConstants.mainFont,
      height: 1.3,
    );

    // 默认标题样式
    final defaultTitleStyle = TextStyle(
      fontSize: 23.sp,
      fontWeight: FontWeight.bold,
      color: AppColors.dialogTextColor,
      fontFamily: AppConstants.mainFont,
    );

    // 创建内容组件
    Widget contentWidget;
    if (content is String) {
      contentWidget = Text(
        content as String,
        style: contentStyle ?? defaultContentStyle,
        textAlign: TextAlign.center,
      );
    } else if (content is Widget) {
      contentWidget = content as Widget;
    } else {
      contentWidget = Text(
        content.toString(),
        style: contentStyle ?? defaultContentStyle,
        textAlign: TextAlign.center,
      );
    }

    // 创建操作按钮组件
    final actionsWidget = IntrinsicHeight(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 确认按钮
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  if (onConfirm != null) {
                    onConfirm!();
                  }
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  confirmText ?? S.current.dialog_text_yes,
                  style: TextStyle(
                    fontSize: 19.sp,
                    color: confirmColor ?? AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          // 垂直分割线
          VerticalDivider(
            width: 1,
            thickness: 0.5,
            color: AppColors.dialogDividerColor,
          ),

          // 取消按钮
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                  if (onCancel != null) {
                    onCancel!();
                  }
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  cancelText ?? S.current.dialog_text_no,
                  style: TextStyle(
                    fontSize: 19.sp,
                    fontWeight: FontWeight.bold,
                    color: cancelColor ?? AppColors.dialogCancelColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // 默认实现
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
      contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null) ...[
            SizedBox(height: 20.h),
            Text(
              title!,
              style: titleStyle ?? defaultTitleStyle,
              textAlign: TextAlign.center,
            ),
          ],
          SizedBox(height: 20.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            child: contentWidget,
          ),
          SizedBox(height: 20.h),
          // 使用Padding代替Container并设置负的padding来实现分割线延伸到边缘
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 0),
            child: Divider(
              height: 0,
              thickness: 0.5,
              color: AppColors.dialogDividerColor,
              endIndent: 0,
              indent: 0,
            ),
          ),
          // 底部操作按钮区域
          actionsWidget,
        ],
      ),
    );
  }
}
