import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  SharedPreferences? _prefs;  // 改为可空类型
  bool _initialized = false;  // 添加初始化标志

  factory StorageService() => _instance;

  StorageService._internal();

  Future<void> init() async {
    if (_initialized) return;  // 如果已经初始化过，直接返回
    _prefs = await SharedPreferences.getInstance();
    _initialized = true;
  }

  // 写入字符串
  Future<void> write(String key, String value) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.setString(key, value);
  }

  // 写入整数
  Future<void> writeInt(String key, int value) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.setInt(key, value);
  }

  // 写入布尔值
  Future<void> writeBool(String key, bool value) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.setBool(key, value);
  }

  // 写入双精度浮点数
  Future<void> writeDouble(String key, double value) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.setDouble(key, value);
  }

  // 写入字符串列表
  Future<void> writeStringList(String key, List<String> value) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.setStringList(key, value);
  }



  // 保存JSON数据
  Future<void> writeJsonData(String key, Map<String, dynamic> data) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = json.encode(data);
    await prefs.setString(key, jsonString);
  }

  // 获取JSON数据
  Future<Map<String, dynamic>?> readJsonData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(key);
    if (jsonString != null) {
      return json.decode(jsonString) as Map<String, dynamic>;
    }
    return null;
  }


  // 读取字符串
  String? read(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.getString(key);
  }

  // 读取整数
  int? readInt(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.getInt(key);
  }

  // 读取布尔值
  bool? readBool(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.getBool(key);
  }

  // 读取双精度浮点数
  double? readDouble(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.getDouble(key);
  }

  // 读取字符串列表
  List<String>? readStringList(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.getStringList(key);
  }

  // 移除指定键的数据
  Future<void> remove(String key) async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.remove(key);
  }

  // 清除所有数据
  Future<void> clear() async {
    if (_prefs == null) throw StateError('缓存未初始化');
    await _prefs!.clear();
  }

  // 检查是否包含指定键
  bool containsKey(String key) {
    if (_prefs == null) throw StateError('缓存未初始化');
    return _prefs!.containsKey(key);
  }
}

// 2. 在 Provider 中使用
// @riverpod
// class SettingsNotifier extends _$SettingsNotifier {
//   late final StorageService _storage;

//   @override
//   Future<String?> build() async {
//     _storage = StorageService();
//     return _storage.getString('theme');
//   }

//   Future<void> setTheme(String theme) async {
//     await _storage.setString('theme', theme);
//     state = AsyncValue.data(theme);
//   }
// }