import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:user_app/core/network/result/api_error_code.dart';
import 'package:user_app/generated/l10n.dart';

class ApiResult<T> {
  // 响应数据
  final T? data;

  // 响应消息
  final String msg;

  // 响应状态码
  final int status;

  // 响应时间戳
  final String time;

  // 响应语言
  final String lang;

  // 构造函数
  ApiResult({
    this.data,
    required this.msg,
    required this.status,
    this.time = '',
    this.lang = 'ug',
  });

  // 是否成功
  bool get success => status == ApiErrorCode.SUCCESS;

  // 是否是网络错误
  bool get isNetworkError => status == ApiErrorCode.NETWORK_ERROR;

  // 是否是超时错误
  bool get isTimeoutError => status == ApiErrorCode.TIMEOUT_ERROR;

  /// 创建成功结果
  factory ApiResult.success(final T data,
      {final String msg = '', final String time = '', final String lang = 'ug'}) {
    return ApiResult(
      data: data,
      msg: msg,
      status: ApiErrorCode.SUCCESS,
      time: time.isEmpty ? DateTime.now().toString() : time,
      lang: lang,
    );
  }

  /// 创建错误结果（统一的错误工厂方法）
  factory ApiResult.error({
    required final String msg,
    final int status = ApiErrorCode.BUSINESS_ERROR,
    final String time = '',
    final String lang = 'ug',
  }) {
    return ApiResult(
      data: null,
      msg: msg,
      status: status,
      time: time.isEmpty ? DateTime.now().toString() : time,
      lang: lang,
    );
  }

  /// 从异常创建失败结果
  factory ApiResult.fromException(final dynamic exception) {
    if (exception is DioError) {
      return _handleDioError(exception);
    } else if (exception is SocketException) {
      return ApiResult.error(
        msg: S.current.no_network_please_check,
        status: ApiErrorCode.NETWORK_ERROR,
      );
    } else if (exception is TimeoutException) {
      return ApiResult.error(
        msg: S.current.connection_timeout,
        status: ApiErrorCode.TIMEOUT_ERROR,
      );
    } else if (exception is FormatException) {
      return ApiResult.error(
        msg: '${S.current.err_msg}: ${exception.message}',
        status: ApiErrorCode.PARSING_ERROR,
      );
    } else {
      return ApiResult.error(
        msg: exception.toString(),
      );
    }
  }

  /// 处理Dio错误
  static ApiResult<T> _handleDioError<T>(final DioError error) {
    switch (error.type) {
      case DioErrorType.connectTimeout:
      case DioErrorType.sendTimeout:
      case DioErrorType.receiveTimeout:
        return ApiResult.error(
          msg: S.current.connection_timeout,
          status: ApiErrorCode.TIMEOUT_ERROR,
        );
      case DioErrorType.response:
        final statusCode = error.response?.statusCode;
        final responseData = error.response?.data;
        if (responseData is Map<String, dynamic>) {
          if (responseData.containsKey('status')) {
            return ApiResult(
              data: null,
              msg: responseData['msg'] ?? S.current.network_err_response,
              status: responseData['status'] ??
                  statusCode ??
                  ApiErrorCode.BUSINESS_ERROR,
              time: responseData['time'] ?? '',
              lang: responseData['lang'] ?? 'ug',
            );
          }
        }
        if (statusCode != null) {
          return ApiResult.error(
            msg: _getErrorMessage(statusCode),
            status: statusCode,
          );
        } else {
          return ApiResult.error(
            msg: S.current.network_err_response,
          );
        }
      case DioErrorType.cancel:
        return ApiResult.error(
          msg: S.current.request_canceled,
        );
      case DioErrorType.other:
        if (error.error is SocketException) {
          return ApiResult.error(
            msg: S.current.no_network_please_check,
            status: ApiErrorCode.NETWORK_ERROR,
          );
        }
        return ApiResult.error(
          msg: S.current.net_work_error,
        );
    }
  }

  // 简化的错误消息获取方法
  static String _getErrorMessage(final int statusCode) {
    switch (statusCode) {
      case ApiErrorCode.UNAUTHORIZED:
        return S.current.network_err_not_auth;
      case ApiErrorCode.NOT_FOUND:
        return S.current.network_err_cat_not_find;
      case ApiErrorCode.SERVER_ERROR:
        return S.current.network_err_server_error;
      default:
        return '${S.current.err_msg} ($statusCode)';
    }
  }

  /// 获取错误信息
  String getErrorMessage() {
    return msg;
  }

  /// 获取状态码
  int getStatusCode() {
    return status;
  }
}
