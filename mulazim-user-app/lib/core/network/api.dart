/// API 路径
class Api {
  static final restaurant = _Restaurant(); // 餐厅模块

  static final user = _User(); // 用户模块
  static final order = _Order(); // 订单模块
  static final foods = _Foods(); // 餐厅美食列表
  static final comment = _Comment(); // 餐厅评论列表
  static final auth = _Auth(); // 认证模块
  static final address = _Address(); // 地址模块
  static final activity = _Activity(); // 活动模块
  static final web = _Web();

  static final collect = _Collect(); // 收藏模块

  static final mine = _Mine(); // 我的模块

  static final shipper = _Shipper(); // 配送员模块

  /// 帮助相关接口
  static final help = _Help();

  /// 商家入驻相关
  static final addShop = _AddShopPaths();

  static final lottery = _Lottery();

  static final home = _Home();

  static final chat = _Chat();

  /// 订单排名相关接口
  static final orderRanking = _OrderRanking();

  /// 海报相关接口
  static final poster = _Poster();

  /// 支付相关接口
  static final payment = _Payment();
}

class _Home {
  String get homeInfo => '/v1/home/<USER>'; // 首页所有信息
  String get homeNotice => '/v1/home/<USER>'; // 首页通知
  String get selfTake => '/v1/restaurant/self-take'; // 自取页面
  String get restaurantHome => '/v1/restaurant/home'; // 快捷页面
  String get discountFoods => '/v1/preferential/discount-foods'; // 优惠页面
  String get myOrder => '/v1/order/list-new'; // 我的订单
  String get myOrderDetail => '/v1/order/order-detail'; // 我的订单详情
  String get prolongConfirm =>
      '/v1/sms-pay/order-detail-prolong-confirm'; // 同意配送員的延期時間要求
  String get shipperPositionInfo => '/v1/order/shipping-position-info'; // 配送员地址
  String get cancelOrder => '/v1/order/unsubscribe'; // 取消订单
  String get closeOrder => '/v1/order/close'; // 取消订单
  String get delOrder => '/v1/order/del-history-order'; // 删除订单
  String get agentPayInfo => '/v1/order/order-detail-another-new'; // 取消订单
  String get partRefundInfo => '/v1/order/part-refund-detail'; // 部分退款
  String get terminalInfo => '/v1/terminal/show'; // 应用版本
  String get dots => '/v1/home/<USER>'; // 红点信息
  /// 聊天模块
}

//https://smart.mulazim.com/ug/v1/order/order-detail?order_id=13286921

class _Collect {
  String get storeCollect => '/v1/user/store-collect'; // 餐厅收藏列表
  String get foodCollect => '/v1/user/food-collects'; // 美食收藏列表
}

class _Auth {
  String get sendSmsCode => '/v1/oauth/send-code';
  String get login => '/v1/oauth/login';
  String get register => '/smart/v2/push/register';
  String get unregister => '/smart/v2/push/unregister';
}

// 餐厅模块
class _Restaurant {
  String get show => '/v1/restaurant/show'; // 餐厅详情
  String get foodsShow => '/v1/foods/show'; // 餐厅详情
  String get restaurantCommentList =>
      '/v1/comment/restaurant-comment-list'; // 餐厅详情
  String get addFoodCollect => '/v1/user/add-food-collect'; // 收藏
  String get removeFoodCollect => '/v1/user/remove-food-collect'; // 取消收藏
  String get comboList => '/smart/v2/restaurant/combo-list';
}

class _Foods {
  String get list => '/v1/restaurant/food-list'; // 餐厅美食列表
  String get specData => '/smart/v2/restaurant/multi-foods/spec'; // 获取美食规格数据
}

class _Comment {
  String get list => '/v1/comment/list'; // 我的评论列表
  String get restaurantCommentList =>
      '/v1/comment/restaurant-comment-list'; // 餐厅评论列表
  String get delete => '/v1/comment/delete'; // 删除评论
  String get create => '/v1/comment/create'; // 创建评论
  String get uploadImage => '/v1/comment/upload-comment-image'; // 上传评论图片
  String get deleteImage =>
      '/v1/comment/delete-uploaded-comment-image'; // 删除评论图片
  String get orderDetail => '/v1/comment/order-detail'; // 获取订单评价详情
}

// 用户模块
class _User {
  String get login => 'api/v1/login'; // 登录
  String get register => 'api/v1/register'; // 注册
  String get logout => 'api/v1/logout'; // 退出登录
  String get bindWechatInfo => '/v1/user/bind-wechat-info'; // 绑定微信信息
  String get uploadImage => '/upload/image'; // 上传图片
  String get pointsLog => '/v1/user/points-log'; // 积分记录
}

// 订单模块
class _Order {
  String get list => '/v1/order/list'; // 订单列表
  String get takeTimeList => '/v1/order/take-time-list'; // 获取订单提交页面数据
  /// 创建订单
  String get createOrder => '/v1/order/create-async';

  /// 查询订单
  String get queryOrder => '/v1/order/query-async';

  /// 查询付款结果
  String get queryPayResult => '/v1/order/query';

  /// 检查是否可以现金支付
  String get canCashPay => '/v1/payment/can-cash-pay';
}

// 地址模块
class _Address {
  String get history => '/v1/user/history-address'; // 历史地址列表
  String get addAddress => '/v1/user/add-address'; // 添加
  String get listByLocation => '/v1/building/list-by-location'; // 获取地址列表按定位数据
  String get listByInput => '/v1/search/address-by-input-words'; // 获取地址列表按关键词
  String get getCityArea => '/v1/city/city-area'; // 获取各城市
  String get getStreetList => '/v1/street/list/'; // 获取对应街头
  String get getParkList => '/v1/building/list-by-street/'; // 获取对应小区
  String get deleteAddress => '/v1/user/delete-address'; // 删除地址
  String get updateAddress => '/v1/user/update-address'; // 更新地址
}

//活动模块
class _Activity {
  String get secKillFoods => '/v1/seckill/foods'; // 秒杀数据
  String get specialFoods => '/v1/seckill/special-price-foods'; // 特价活动
  String get discountFoods => '/v1/home/<USER>'; // 专区活动
  String get rankingInfo => '/v1/ranking-new/info'; // 排名信息
  String get rankingUserAddress => '/v1/ranking-new/user-address'; // 排名用户地址
  String get rankingConfirmAddress =>
      '/v1/ranking-new/confirm-address'; // 排行榜确认地址
  String get rankingHistory => '/v1/ranking-new/history'; // 排行榜历史
}

/// web模块
class _Web {
  /// 获取web
  String get about => '/v1/about/detail';
}

/// 帮助相关接口
class _Help {
  /// 获取网页列表
  String get webUrlList => '/v1/help/web-url-list';

  /// 获取代理资质
  String get agentLicense => '/v1/license-business/agent-license';

  /// 获取帮助列表
  String get helpList => '/v1/help/list';

  /// 获取帮助详情
  String get helpDetail => '/v1/help/detail';
}

/// mine
class _Mine {
  /// 获取优惠券列表
  String get mineCourtesyList => '/v1/coupon/mine';

  /// 领取优惠券
  String get courtesyTake => '/v1/coupon/take';

  /// 红包列表
  String get redPacketList => '/v1/user/red-packet-list';
}

class _AddShopPaths {
  /// 商家入驻
  String get addShop => '/v1/restaurant/add-shop';
  String get licenseList => '/v1/license-business/list';
  String get create => '/v1/store-apply/create';
}

class _Lottery {
  final String orderList = '/v1/lottery/order/chance-list'; // 订单列表，小程序中使用
  final String orderDetail = '/v1/lottery/order/detail'; // 获取抽奖详情，小程序中使用
}

// 配送员模块
class _Shipper {
  String get detail => '/v1/comment/shipper-detail'; // 配送员详情
  String get tips => '/v1/comment/shipper-tips-wechat-param'; // 配送员打赏参数
  String get tipsHistory => '/v1/comment/shipper-tips'; // 配送员打赏历史
}

/// 聊天相关
class _Chat {
  /// 获取聊天列表
  String get chatList => '/client/v2/chat/list';

  /// 获取聊天室详情
  String get chatDetail => '/client/v2/chat/detail';

  /// 发送聊天消息
  String get sendChat => '/client/v2/chat/send';

  /// 上传图片
  String get uploadImage => '/upload/image';
}

/// 订单排名相关接口
class _OrderRanking {
  /// 获取订单排名记录
  String get luckyHistory => '/smart/v2/activity/order-ranking/lucky-history';

  /// 提交活动评价
  String get comment => '/smart/v2/activity/order-ranking/comment';

  /// 分享活动
  String get favoriteShareView =>
      '/smart/v2/activity/valentine/favorite-share-view';

  /// 兑换奖品
  String get exchange => '/smart/v2/activity/order-ranking/exchange';

  /// 获取订单排名活动页面数据
  String get activityPage => '/smart/v2/activity/order-ranking/activity-page';
}

/// 海报相关接口
class _Poster {
  /// 获取海报列表
  String get list => '/v1/image/res-poster-list';

  /// 生成海报
  String get generate => '/v1/image/res-poster';

  /// 查询海报状态
  String get query => '/v1/image/res-poster-query';
}

/// 支付相关接口
class _Payment {
  /// 检查是否可以现金支付
  String get canCashPay => '/v1/payment/can-cash-pay';

  /// 发起支付
  String get pay => '/v1/payment/pay';
}
