import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/location_util.dart';
import 'package:user_app/main.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/features/auth/providers/auth_provider.dart';
import 'package:user_app/core/network/result/api_error_code.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

class AuthInterceptor extends Interceptor {
  /// 获取本地存储库实例
  final localStorageRepository = globalContainer.read(localStorageRepositoryProvider);

  /// 防重复导航标志
  static bool _isNavigatingToLogin = false;
  static String? _deviceModel;

  @override
  void onRequest(
      final RequestOptions options, final RequestInterceptorHandler handler) {
    // 获取语言设置
    String lang = globalContainer.read(languageProvider);
    options.baseUrl = '${options.baseUrl}/$lang';

    // 获取token
    final token = localStorageRepository.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    final regId = localStorageRepository.getRegId();
    options.headers['uuid'] = regId;
    // 获取用户信息
    final userInfo = localStorageRepository.getUserInfo();

    // 获取位置信息
    final locationInfo = localStorageRepository.getLocationInfo();
    final locationModel = LocationUtil.getLatestLocationData();

    // 设置area、city、langId等字段
    options.headers['area'] = locationInfo?.areaId ?? 1; // 默认区域ID
    options.headers['city'] = locationInfo?.cityId ?? 1; // 默认城市ID

    // 根据语言设置langId
    options.headers['langId'] = lang == 'zh' ? 2 : 1; // 中文为2，维语为1

    // 设置位置信息
    if (locationInfo != null &&
        locationInfo.lat != null &&
        locationInfo.lng != null) {
      options.headers['lat'] = locationInfo.lat;
      options.headers['lng'] = locationInfo.lng;
    } else if (locationModel != null) {
      // 如果本地存储没有位置信息，使用provider中的位置信息
      options.headers['lat'] = locationModel['latitude'] ?? 43.795411;
      options.headers['lng'] = locationModel['longitude'] ?? 87.633788;
    } else {
      // 使用默认位置（乌鲁木齐）
      options.headers['lat'] = 43.795411;
      options.headers['lng'] = 87.633788;
    }

    // 设置用户ID
    if (userInfo != null) {
      options.headers['user-id'] = userInfo.id;
    }
    if (_deviceModel == null) {
      Future.microtask(() async {
        _deviceModel = await getDeviceModel();
        options.headers['openid'] = _deviceModel;
      });
    }
    options.headers['openid'] = _deviceModel;
    options.headers['sdk-version'] = "flutter/${Platform.operatingSystem}";
    final currentPatchNumber =
        globalContainer.read(localStorageRepositoryProvider).getCurrentPatchNumber();
    options.headers['mini-version'] = '${options.headers['mini-version']}${currentPatchNumber != 0 ? '+$currentPatchNumber' : ''}';
    handler.next(options);
  }

  @override
  void onResponse(
      final Response response, final ResponseInterceptorHandler handler) {
    if (response.statusCode == 200 &&
        response.data['status'] == ApiErrorCode.UNAUTHORIZED) {
      // 退出登录
      globalContainer.read(authProvider.notifier).logout();
      _navigateToLoginSafely();
    }

    // 继续处理响应
    handler.next(response);
  }

  @override
  void onError(final DioError err, final ErrorInterceptorHandler handler) {
    // 使用 ApiResult.fromException 处理错误
    final apiResult = ApiResult.fromException(err);

    // 如果是授权错误，进行处理
    if (apiResult.status == ApiErrorCode.UNAUTHORIZED) {
      globalContainer.read(authProvider.notifier).logout();
      _navigateToLoginSafely();
      BotToast.showText(text: apiResult.msg);
    }

    // 继续传递错误
    handler.next(err);
  }

  /// 安全地导航到登录页面，避免重复导航
  void _navigateToLoginSafely() {
    // 如果已经在导航过程中，直接返回
    if (_isNavigatingToLogin) {
      return;
    }

    final context = AppContext().currentContext;
    if (context == null) {
      return;
    }

    // 设置导航标志
    _isNavigatingToLogin = true;

    try {
      // 导航到登录页面
      context.push('/login').then((_) {
        // 导航完成后重置标志
        _isNavigatingToLogin = false;
      }).catchError((error) {
        // 导航失败时也要重置标志
        _isNavigatingToLogin = false;
      });
    } catch (e) {
      // 异常情况下重置标志
      _isNavigatingToLogin = false;
    }
  }

  Future<String> getDeviceModel() async {
    //判断是否已同意隐私协议
    final isAgreePrivacyPolicy = localStorageRepository.getIsAgreePrivacyPolicy();
    if (!isAgreePrivacyPolicy) {
      return 'unknown';
    }

    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
       return androidInfo.brand ?? androidInfo.model ?? 'unknown';
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.utsname.machine ?? iosInfo.model ?? 'unknown';
    } else {
      return 'unknown';
    }
  }
}
