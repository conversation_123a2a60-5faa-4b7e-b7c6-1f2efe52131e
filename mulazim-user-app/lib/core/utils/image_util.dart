import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:user_app/core/utils/platform_channel_utils.dart';

/// 压缩图片到指定大小的参数类
class _CompressionParams {
  final Uint8List imageBytes;
  final int maxSize;
  final int initialQuality;
  final int minQuality;

  _CompressionParams({
    required this.imageBytes,
    required this.maxSize,
    required this.initialQuality,
    required this.minQuality,
  });
}

/// 图片工具类
/// 提供图片下载、缓存等功能
class ImageUtil {
  static final Dio _dio = Dio();

  /// 从URL下载图片到临时目录
  /// [imageUrl] 图片URL
  /// 返回临时文件路径和图片数据
  static Future<Map<String, dynamic>> downloadImageToCache(
      final String imageUrl) async {
    try {
      // 下载图片为字节数据
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      final imageData = Uint8List.fromList(response.data);

      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = "${DateTime.now().millisecondsSinceEpoch}.png";
      final tempFile = File('${tempDir.path}/$fileName');

      // 写入图片数据到临时文件
      await tempFile.writeAsBytes(imageData);

      return {
        'file': tempFile,
        'data': imageData,
        'path': tempFile.path,
      };
    } catch (e) {
      print("下载图片失败: $e");
      rethrow;
    }
  }

  /// 将字节数据保存到临时文件
  /// [imageData] 图片字节数据
  /// 返回临时文件路径
  static Future<File> saveBytesToCache(final Uint8List imageData) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final fileName = "${DateTime.now().millisecondsSinceEpoch}.png";
      final tempFile = File('${tempDir.path}/$fileName');

      // 写入图片数据到临时文件
      await tempFile.writeAsBytes(imageData);
      return tempFile;
    } catch (e) {
      print("保存图片到缓存失败: $e");
      rethrow;
    }
  }

  /// 根据url获取图片
  /// [imageUrl] 图片URL
  /// 返回图片字节数据
  static Future<Uint8List> getImageFromUrl(final String imageUrl) async {
    final response = await _dio.get(
      imageUrl,
      options: Options(responseType: ResponseType.bytes),
    );
    return Uint8List.fromList(response.data);
  }

  /// 清除临时文件
  /// [file] 临时文件
  static Future<void> deleteTempFile(final File file) async {
    try {
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print("删除临时文件失败: $e");
    }
  }

  /// 获取文件扩展名
  /// [url] 文件URL
  /// 返回文件扩展名（如 png, jpg 等）
  static String getFileExtension(final String url) {
    try {
      final Uri uri = Uri.parse(url);
      final String path = uri.path;
      final int dotIndex = path.lastIndexOf('.');

      if (dotIndex != -1 && dotIndex < path.length - 1) {
        return path.substring(dotIndex + 1).toLowerCase();
      }

      return 'png'; // 默认扩展名
    } catch (e) {
      return 'png'; // 默认扩展名
    }
  }

  /// 生成唯一文件名
  /// [prefix] 文件名前缀
  /// [extension] 文件扩展名
  /// 返回唯一文件名
  static String generateUniqueFileName({
    final String prefix = 'img',
    final String extension = 'png',
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (100 + DateTime.now().microsecond % 900).toString();
    return '$prefix-$timestamp-$random.$extension';
  }

  /// 压缩图片
  /// [imageData] 图片字节数据
  /// [maxSize] 最大大小（字节）
  /// [aspectRatio] 目标宽高比（宽/高），默认为5/4
  /// 返回压缩后的图片字节数据
  static Future<Uint8List> compressImage(
    final Uint8List imageData,
    final int maxSize, {
    final double aspectRatio = 5 / 4,
    final int initialQuality = 90,
    final int minQuality = 40,
  }) async {
    // 使用compute隔离解码图片，避免阻塞UI线程
    final decodedImage = await compute(_decodeImage, imageData);
    if (decodedImage == null) {
      throw Exception('无法解码图片');
    }

    // 裁剪图片为指定比例
    final croppedImage = _cropToAspectRatio(decodedImage, aspectRatio);

    // 使用compute隔离压缩图片，避免阻塞UI线程
    final compressionParams = _CompressionParams(
      imageBytes: img.encodeJpg(croppedImage),
      maxSize: maxSize,
      initialQuality: initialQuality,
      minQuality: minQuality,
    );

    return await compute(_compressToMaxSizeIsolate, compressionParams);
  }

  /// 解码图片（在隔离线程中运行）
  static img.Image? _decodeImage(final Uint8List data) {
    return img.decodeImage(data);
  }

  /// 裁剪图片为指定宽高比
  static img.Image _cropToAspectRatio(
      final img.Image image, final double targetRatio) {
    final currentRatio = image.width / image.height;

    // 如果当前比例与目标比例相差不大，直接返回
    if ((currentRatio - targetRatio).abs() < 0.01) {
      return image;
    }

    int cropWidth, cropHeight;
    int cropX = 0, cropY = 0;

    // 如果原图更宽，需要裁剪宽度
    if (currentRatio > targetRatio) {
      cropHeight = image.height;
      cropWidth = (cropHeight * targetRatio).round();
      // 从中间裁剪
      cropX = ((image.width - cropWidth) / 2).round();
    }
    // 如果原图更高，需要裁剪高度
    else {
      cropWidth = image.width;
      cropHeight = (cropWidth / targetRatio).round();
      // 从顶部裁剪
      cropY = 0;
    }

    return img.copyCrop(
      image,
      x: cropX,
      y: cropY,
      width: cropWidth,
      height: cropHeight,
    );
  }

  /// 在隔离线程中压缩图片（接收序列化参数）
  static Uint8List _compressToMaxSizeIsolate(_CompressionParams params) {
    // 解码图片
    final image = img.decodeJpg(params.imageBytes);
    if (image == null) {
      return params.imageBytes; // 如果解码失败，返回原图
    }

    // 开始的质量
    int quality = params.initialQuality;

    // 首先尝试使用初始质量
    Uint8List result =
        Uint8List.fromList(img.encodeJpg(image, quality: quality));

    // 如果初始压缩后仍然超过大小限制，继续降低质量
    while (result.length > params.maxSize && quality > params.minQuality) {
      quality -= 10; // 每次降低10%质量
      result = Uint8List.fromList(img.encodeJpg(image, quality: quality));
    }

    // 如果降低到最低质量后仍然太大，则缩小图片尺寸
    if (result.length > params.maxSize) {
      // 计算需要的缩放比例
      double scale = 0.9; // 初始缩放比例
      img.Image resizedImage = image;

      while (result.length > params.maxSize && scale > 0.3) {
        final newWidth = (image.width * scale).round();
        final newHeight = (image.height * scale).round();

        resizedImage = img.copyResize(
          image,
          width: newWidth,
          height: newHeight,
          interpolation: img.Interpolation.average,
        );

        result =
            Uint8List.fromList(img.encodeJpg(resizedImage, quality: quality));
        scale -= 0.1; // 每次缩小10%
      }
    }

    return result;
  }

  /// 原始方法，保留但不再直接使用
  @Deprecated('使用_compressToMaxSizeIsolate替代')
  static Future<Uint8List> _compressToMaxSize(
    final img.Image image,
    final int maxSize,
    final int initialQuality,
    final int minQuality,
  ) async {
    // 开始的质量
    int quality = initialQuality;

    // 首先尝试使用初始质量
    Uint8List result =
        Uint8List.fromList(img.encodeJpg(image, quality: quality));

    // 如果初始压缩后仍然超过大小限制，继续降低质量
    while (result.length > maxSize && quality > minQuality) {
      quality -= 10; // 每次降低10%质量
      result = Uint8List.fromList(img.encodeJpg(image, quality: quality));
    }

    // 如果降低到最低质量后仍然太大，则缩小图片尺寸
    if (result.length > maxSize) {
      // 计算需要的缩放比例
      double scale = 0.9; // 初始缩放比例
      img.Image resizedImage = image;

      while (result.length > maxSize && scale > 0.3) {
        final newWidth = (image.width * scale).round();
        final newHeight = (image.height * scale).round();

        resizedImage = img.copyResize(
          image,
          width: newWidth,
          height: newHeight,
          interpolation: img.Interpolation.average,
        );

        result =
            Uint8List.fromList(img.encodeJpg(resizedImage, quality: quality));
        scale -= 0.1; // 每次缩小10%
      }
    }

    return result;
  }

  /// 创建缩略图（特定于微信分享的5:4比例）
  static Future<Uint8List> createWeChatThumbnail(
    final Uint8List imageData,
  ) async {
    // 微信缩略图最大32KB
    return await compressImage(
      imageData,
      32 * 1024,
      aspectRatio: 5 / 4,
      initialQuality: 100,
    );
  }

  /// 保存图片到相册并刷新媒体库
  /// [file] 图片文件
  /// 返回是否成功
  static Future<bool> refreshMediaStore(final File file) async {
    try {
      return await PlatformChannelUtils.scanMediaFile(file.path);
    } catch (e) {
      print("刷新媒体库失败: $e");
      return false;
    }
  }
}
