import 'dart:ui';

/// 文件: format_util.dart
/// 描述: 格式化相关的工具函数

/// 格式化工具类
/// 提供数字、金额等数据的格式化功能
class FormatUtil {
  /// 格式化金额
  /// 将分为单位的金额转换为元为单位，并进行格式化处理
  ///
  /// [amount] 以分为单位的金额数值
  /// 返回格式化后的金额字符串
  ///
  /// 处理规则：
  /// 1. 将分转换为元（除以100）
  /// 2. 保留两位小数
  /// 3. 去除末尾多余的0
  /// 4. 如果小数部分为0，则去除小数点
  ///
  /// 示例：
  /// ```dart
  /// formatAmount(12340) // 返回 "123.4"
  /// formatAmount(100000) // 返回 "1000"
  /// formatAmount(12345) // 返回 "123.45"
  /// ```
  /// 格式化工具类
  /// 格式化金额，将分转换为元

  static String formatAmount(final num amount) {
    // 将分转换为元并保留两位小数
    double yuan = amount / 100.0;
    String formattedAmount = yuan.toStringAsFixed(2);

    // 去除金额末尾的零
    if (formattedAmount.contains(".")) {
      while (formattedAmount.endsWith("0")) {
        formattedAmount =
            formattedAmount.substring(0, formattedAmount.length - 1);
      }
      if (formattedAmount.endsWith(".")) {
        formattedAmount =
            formattedAmount.substring(0, formattedAmount.length - 1);
      }
    }
    return formattedAmount;
  }

  // 颜色转换函数
  static Color parseColor(final String colorString) {
    // 移除 # 号
    String hexColor = colorString.replaceAll('#', '');
    // 补全透明度
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    // 检查长度是否合法
    if (hexColor.length != 8) {
      throw FormatException('Invalid color string: $colorString');
    }
    // 转换为整数
    int hexValue = int.parse(hexColor, radix: 16);
    return Color(hexValue);
  }

  /// 格式化价格
  static String formatPrice(final num price) {
    String formattedAmount = price.toStringAsFixed(2);

    if (formattedAmount.contains(".")) {
      while (formattedAmount.endsWith("0")) {
        formattedAmount =
            formattedAmount.substring(0, formattedAmount.length - 1);
      }
      if (formattedAmount.endsWith(".")) {
        formattedAmount =
            formattedAmount.substring(0, formattedAmount.length - 1);
      }
    }
    return formattedAmount;
  }

  /// 格式化距离
  ///
  /// [distance] 距离
  /// 返回格式化后的距离字符串
  ///
  /// 处理规则：
  /// 1. 将距离转换为公里（除以1000）
  /// 2. 保留两位小数
  static String formatDistance(final num distance) {
    // 将分转换为元并保留两位小数
    String formattedDistance = (distance / 1000.0).toStringAsFixed(2);

    // 去除金额末尾的零
    if (formattedDistance.contains(".")) {
      while (formattedDistance.endsWith("0")) {
        formattedDistance =
            formattedDistance.substring(0, formattedDistance.length - 1);
      }
      if (formattedDistance.endsWith(".")) {
        formattedDistance =
            formattedDistance.substring(0, formattedDistance.length - 1);
      }
    }
    return formattedDistance;
  }

  /// 将秒数转换为小时:分钟:秒格式
  ///
  /// [seconds] 总秒数
  /// 返回格式化后的时间Map，包含小时、分钟、秒
  static Map<String, String> formatSecondToHMS(final int seconds) {
    // 计算小时、分钟和秒
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int remainingSeconds = seconds % 60;

    // 格式化为两位数
    String hourStr = hours.toString().padLeft(2, '0');
    String minuteStr = minutes.toString().padLeft(2, '0');
    String secondStr = remainingSeconds.toString().padLeft(2, '0');

    return {'hour': hourStr, 'minute': minuteStr, 'second': secondStr};
  }
}
