import 'dart:developer';
import 'dart:async';
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/utils/permission_helper.dart';
import 'package:user_app/core/utils/app_lifecycle_manager.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'event_bus.dart';

/// 位置服务工具类
/// 用于处理定位权限申请和位置信息获取
/// 使用高德地图定位SDK
/// 全局单例，生命周期与App一致
class LocationUtil {
  // 单例实例 - 确保全局唯一
  static final LocationUtil _instance = LocationUtil._();

  // 高德定位实例
  static AMapFlutterLocation? _location;

  // 权限状态
  PermissionStatus? permissionStatus;

  // 确认状态
  String confirmState = '';

  // 位置流控制器和位置流
  static StreamController<Map<String, dynamic>>? _locationStreamController;
  static Stream<Map<String, dynamic>>? _locationStream;

  // 原始位置监听
  static StreamSubscription? _originalLocationSubscription;

  // 是否已初始化
  bool _isInitialized = false;

  // 存储最新的位置数据
  static Map<String, dynamic>? _latestLocationData;

  // 私有构造函数
  LocationUtil._();

  /// 获取LocationUtil实例
  /// [context] 用于显示权限请求对话框
  ///
  /// 初始化高德地图SDK并检查定位权限，只在第一次调用时初始化
  static Future<LocationUtil> getInstance({BuildContext? context}) async {
    // 如果未初始化且提供了上下文，则执行初始化
    if (!_instance._isInitialized && context != null) {
      await _instance._init(context: context);
    }
    return _instance;
  }

  /// 初始化位置服务
  ///
  /// 1. 配置高德地图SDK
  /// 2. 检查定位权限
  /// 3. 如果没有权限，显示权限请求对话框
  /// 4. 获取位置信息
  Future<void> _init({required BuildContext context}) async {
    // 防止重复初始化
    if (_isInitialized) {
      log("LocationUtil已经初始化，跳过重复初始化");
      return;
    }

    log("开始初始化LocationUtil");
    final isAgreePrivacyPolicy = globalContainer
        .read(localStorageRepositoryProvider)
        .getIsAgreePrivacyPolicy();
    // 配置高德地图SDK - 先设置隐私合规
    AMapFlutterLocation.updatePrivacyShow(isAgreePrivacyPolicy, true);
    AMapFlutterLocation.updatePrivacyAgree(isAgreePrivacyPolicy);
    AMapFlutterLocation.setApiKey(Config.amapAndroidKey, Config.amapIOSKey);

    // 在显示选择器之前，请求位置
    final permissionResults = await PermissionHelper.requestPermission(
      permission: Permission.location,
      explanation: S.current.location_permission_explanation,
      title: S.current.location_permission_title,
    );

    // 检查是否获得了所有权限
    if (!permissionResults) {
      BotToast.showText(
        text: S.current.permissions_required
            .replaceAll('%s', S.current.location_permission_title),
      );
      return;
    }

    // 启动位置服务
    _startLocationService();

    // 标记为已初始化
    _isInitialized = true;
    log("LocationUtil初始化完成");
  }

  /// 检查定位权限状态
  ///
  /// 返回true表示已获得权限
  /// 返回false表示未获得权限或权限受限
  Future<bool> checkLocationPermission() async {
    PermissionStatus status = await Permission.location.status;
    return switch (status) {
      PermissionStatus.denied ||
      PermissionStatus.restricted ||
      PermissionStatus.limited ||
      PermissionStatus.permanentlyDenied =>
        false,
      PermissionStatus.provisional || PermissionStatus.granted => true,
    };
  }

  /// 创建可广播的位置流
  static void _createBroadcastLocationStream() {
    // 如果已有流控制器，先销毁
    _locationStreamController?.close();

    // 创建新的广播流控制器
    _locationStreamController =
        StreamController<Map<String, dynamic>>.broadcast();
    _locationStream = _locationStreamController!.stream;

    // 初始化位置实例
    if (_location == null) {
      final isAgreePrivacyPolicy = globalContainer
          .read(localStorageRepositoryProvider)
          .getIsAgreePrivacyPolicy();
      // 确保隐私合规设置在创建实例前完成
      AMapFlutterLocation.updatePrivacyShow(isAgreePrivacyPolicy, true);
      AMapFlutterLocation.updatePrivacyAgree(isAgreePrivacyPolicy);
      _location = AMapFlutterLocation();
    }

    // 取消之前的订阅
    _originalLocationSubscription?.cancel();

    // 监听原始位置流并转发到广播流
    _originalLocationSubscription =
        _location!.onLocationChanged().listen((event) {
      // 存储最新的位置数据
      _latestLocationData = event;

      // 将位置事件添加到广播流
      if (!(_locationStreamController?.isClosed ?? true)) {
        _locationStreamController?.add(event);
      }

      // 同时通过事件总线广播
      eventBus.fire(EventLocationMap(event));
    });

    // 开始定位
    _location!.startLocation();
  }

  /// 启动位置服务
  static void _startLocationService() {
    try {
      // 检查应用是否在前台，只有在前台时才启动位置服务
      final lifecycleManager = AppLifecycleManager();
      if (!lifecycleManager.isInForeground) {
        log("应用在后台，跳过位置服务启动 - 符合隐私合规要求");
        return;
      }

      // 确保位置流已初始化
      if (_locationStream == null) {
        _createBroadcastLocationStream();
      }
    } catch (e) {
      log("启动位置服务异常 $e");
    }
  }

  /// 获取位置流 - 慎用，避免在UI中直接监听导致频繁重建
  static Stream<Map<String, dynamic>>? getLocationStream() {
    if (_locationStream == null) {
      _createBroadcastLocationStream();
    }
    return _locationStream;
  }

  /// 获取最新的位置数据
  /// 不会触发监听器，不会导致页面重建
  static Map<String, dynamic>? getLatestLocationData() {
    return _latestLocationData;
  }

  /// 获取一次位置信息
  /// 优先返回缓存的最新位置，如果没有则等待新的位置
  /// 返回位置信息Map
  static Future<Map<String, dynamic>?> getLocationOncePlatform() async {
    try {
      // 检查应用是否在前台，后台时不允许获取位置信息
      final lifecycleManager = AppLifecycleManager();
      if (!lifecycleManager.isInForeground) {
        log("应用在后台，拒绝获取位置信息 - 符合隐私合规要求");
        return _latestLocationData; // 返回缓存的位置数据
      }

      // 标记位置服务恢复（用户主动请求位置）
      if (lifecycleManager.isLocationPausedInBackground) {
        lifecycleManager.markLocationServiceResumed();
      }

      // 如果已经有最新的位置数据，直接返回
      if (_latestLocationData != null && _latestLocationData!['latitude'] != null && _latestLocationData!['longitude'] != null) {
        return _latestLocationData;
      }

      // 确保位置流已初始化
      if (_locationStream == null) {
        _createBroadcastLocationStream();
      }

      // 创建一个Completer来处理异步操作
      final completer = Completer<Map<String, dynamic>?>();

      // 监听广播流，只取一次值
      StreamSubscription? subscription;
      subscription = _locationStream!.listen((event) {
        // 获取到位置信息后，取消订阅并返回结果
        subscription?.cancel();
        completer.complete(event);
      });

      // 设置超时，5秒后如果还没有获取到位置，返回null
      Future.delayed(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          subscription?.cancel();
          completer.complete(null);
        }
      });

      return await completer.future;
    } catch (e) {
      log("获取位置信息异常 $e");
      return null;
    }
  }

  /// 重新获取位置信息（强制刷新）
  static Future<Map<String, dynamic>?> refreshLocation() async {
    try {
      // 检查应用是否在前台，后台时不允许刷新位置信息
      final lifecycleManager = AppLifecycleManager();
      if (!lifecycleManager.isInForeground) {
        log("应用在后台，拒绝刷新位置信息 - 符合隐私合规要求");
        return _latestLocationData; // 返回缓存的位置数据
      }

      // 标记位置服务恢复（用户主动请求位置刷新）
      if (lifecycleManager.isLocationPausedInBackground) {
        lifecycleManager.markLocationServiceResumed();
      }

      // 确保位置流已初始化
      if (_locationStream == null) {
        _createBroadcastLocationStream();
      } else if (_location != null) {
        // 如果已经初始化，则重新启动定位以获取新位置
        _location!.startLocation();
      }

      // 创建一个Completer来处理异步操作
      final completer = Completer<Map<String, dynamic>?>();

      // 监听广播流，只取一次值
      StreamSubscription? subscription;
      subscription = _locationStream!.listen((event) {
        // 获取到位置信息后，取消订阅并返回结果
        subscription?.cancel();
        completer.complete(event);
      });

      // 设置超时，5秒后如果还没有获取到位置，返回缓存的位置或null
      Future.delayed(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          subscription?.cancel();
          completer.complete(_latestLocationData);
        }
      });

      return await completer.future;
    } catch (e) {
      log("刷新位置信息异常 $e");
      return _latestLocationData;
    }
  }

  /// 停止定位服务 - 通常在应用退出时调用
  static void stopLocation() {
    try {
      _originalLocationSubscription?.cancel();
      _originalLocationSubscription = null;

      _locationStreamController?.close();
      _locationStreamController = null;
      _locationStream = null;

      if (_location != null) {
        _location!.stopLocation();
        _location = null;
      }
      _instance._isInitialized = false;

      // 保留最后一次位置数据不清除
    } catch (e) {
      log("停止定位服务异常 $e");
    }
  }

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
}
