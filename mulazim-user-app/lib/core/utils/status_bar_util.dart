import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 状态栏工具类
/// 提供统一的状态栏样式控制方法
class StatusBarUtil {
  /// 设置状态栏为浅色模式（深色背景，白色内容）
  /// 适用于有深色AppBar或深色背景的页面
  static void setLightMode() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Android
        statusBarBrightness: Brightness.dark, // iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 设置状态栏为深色模式（浅色背景，深色内容）
  /// 适用于白色背景的页面
  static void setDarkMode() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // Android
        statusBarBrightness: Brightness.light, // iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 设置自定义状态栏样式
  /// [backgroundColor] 状态栏背景色
  /// [isLightContent] 是否为浅色内容（白色图标和文字）
  static void setCustomMode({
    Color backgroundColor = Colors.transparent,
    bool isLightContent = true,
  }) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: backgroundColor,
        statusBarIconBrightness:
            isLightContent ? Brightness.light : Brightness.dark, // Android
        statusBarBrightness:
            isLightContent ? Brightness.dark : Brightness.light, // iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 根据背景颜色自动设置状态栏样式
  /// [backgroundColor] 背景颜色
  static void setAutoMode(Color backgroundColor) {
    // 计算背景颜色的亮度
    final brightness = ThemeData.estimateBrightnessForColor(backgroundColor);
    final isLightContent = brightness == Brightness.dark;

    setCustomMode(
      backgroundColor: Colors.transparent,
      isLightContent: isLightContent,
    );
  }

  /// 隐藏状态栏（iOS和Android）
  static void hide() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.bottom],
    );
  }

  /// 显示状态栏（iOS和Android）
  static void show() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );
  }

  /// 设置沉浸式状态栏（透明状态栏，内容延伸到状态栏区域）
  static void setImmersive({bool isLightContent = true}) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            isLightContent ? Brightness.light : Brightness.dark,
        statusBarBrightness:
            isLightContent ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 获取状态栏高度
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 检查是否为iOS平台
  static bool get isIOS => Platform.isIOS;

  /// 检查是否为Android平台
  static bool get isAndroid => Platform.isAndroid;
} 