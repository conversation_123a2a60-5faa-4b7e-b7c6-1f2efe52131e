/// Utility class for precise decimal arithmetic.
class DecimalUtils {
  /// Default precision for rounding.
  static const int defaultPrecision = 2;

  /// Rounds a number to a specified number of decimal places.
  ///
  /// Args:
  ///   value: The number to round.
  ///   precision: The number of decimal places to round to. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   The rounded number.
  static double round(num value, {int precision = defaultPrecision}) {
    if (value.isNaN || value.isInfinite) {
      return value.toDouble();
    }
    return double.parse(value.toStringAsFixed(precision));
  }

  /// Adds two numbers with specified precision.
  ///
  /// Args:
  ///   a: The first number.
  ///   b: The second number.
  ///   precision: The number of decimal places for the result. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   The sum of the two numbers, rounded to the specified precision.
  static double add(num a, num b, {int precision = defaultPrecision}) {
    final double result = a.toDouble() + b.toDouble();
    return round(result, precision: precision);
  }

  /// Subtracts one number from another with specified precision.
  ///
  /// Args:
  ///   a: The number to subtract from.
  ///   b: The number to subtract.
  ///   precision: The number of decimal places for the result. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   The difference between the two numbers, rounded to the specified precision.
  static double subtract(num a, num b, {int precision = defaultPrecision}) {
    final double result = a.toDouble() - b.toDouble();
    return round(result, precision: precision);
  }

  /// Multiplies two numbers with specified precision.
  ///
  /// Args:
  ///   a: The first number.
  ///   b: The second number.
  ///   precision: The number of decimal places for the result. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   The product of the two numbers, rounded to the specified precision.
  static double multiply(num a, num b, {int precision = defaultPrecision}) {
    final double result = a.toDouble() * b.toDouble();
    return round(result, precision: precision);
  }

  /// Divides one number by another with specified precision.
  ///
  /// Args:
  ///   a: The dividend.
  ///   b: The divisor.
  ///   precision: The number of decimal places for the result. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   The quotient of the two numbers, rounded to the specified precision.
  ///   Returns `double.nan` if the divisor is zero.
  static double divide(num a, num b, {int precision = defaultPrecision}) {
    if (b == 0) {
      return double.nan; // Or throw an exception, depending on desired behavior
    }
    final double result = a.toDouble() / b.toDouble();
    return round(result, precision: precision);
  }

  /// Formats a number as a string with a fixed number of decimal places.
  ///
  /// This is useful for display purposes where you want to ensure a consistent
  /// number of decimal places, e.g., "7.40" instead of "7.4".
  ///
  /// Args:
  ///   value: The number to format.
  ///   precision: The number of decimal places. Defaults to [defaultPrecision].
  ///
  /// Returns:
  ///   A string representation of the number with the specified precision.
  static String format(num value, {int precision = defaultPrecision}) {
    return value.toStringAsFixed(precision);
  }
}
