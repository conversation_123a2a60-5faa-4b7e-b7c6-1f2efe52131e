import 'package:flutter/services.dart';

/// 平台通道工具类
/// 提供Flutter与原生平台通信的工具方法
class PlatformChannelUtils {
  /// 媒体通道名称
  static const String mediaChannel = 'app.channel.mulazim.media';

  /// 媒体通道
  static final MethodChannel _mediaChannel = MethodChannel(mediaChannel);

  /// 扫描文件添加到媒体库
  /// [filePath] 文件路径
  /// 返回是否成功
  static Future<bool> scanMediaFile(String filePath) async {
    try {
      final result = await _mediaChannel.invokeMethod<bool>(
        'scanFile',
        {'path': filePath},
      );
      return result ?? false;
    } on PlatformException catch (e) {
      print('扫描媒体文件失败: ${e.message}');
      return false;
    }
  }
}
