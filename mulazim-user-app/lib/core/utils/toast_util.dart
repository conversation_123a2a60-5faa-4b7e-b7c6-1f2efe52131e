import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';

/// Toast工具类
/// Toast显示位置枚举
enum ToastGravity {
  CENTER,
  BOTTOM,
}

/// Toast显示时长枚举
enum Toast {
  LENGTH_SHORT,
  LENGTH_LONG,
}

/// Toast工具类
class ToastUtil {
  /// 显示toast消息
  static void show(
    final String message, {
    final ToastGravity gravity = ToastGravity.CENTER,
    final Toast length = Toast.LENGTH_SHORT,
  }) {
    BotToast.showText(
      text: message,
      duration: length == Toast.LENGTH_LONG
          ? const Duration(seconds: 3)
          : const Duration(seconds: 2),
      align: gravity == ToastGravity.BOTTOM
          ? Alignment.bottomCenter
          : Alignment.center,
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 16.0,
      ),
      contentColor: Colors.black87,
    );
  }

  /// 显示中央toast消息
  static void showCenter(final String message) {
    show(message, gravity: ToastGravity.CENTER);
  }

  /// 显示底部toast消息
  static void showBottom(final String message) {
    show(message, gravity: ToastGravity.BOTTOM);
  }

  /// 显示长时间toast消息
  static void showLong(final String message) {
    show(message, length: Toast.LENGTH_LONG);
  }

  /// 取消所有toast
  static void cancel() {
    LoadingDialog().hide();
  }
}
