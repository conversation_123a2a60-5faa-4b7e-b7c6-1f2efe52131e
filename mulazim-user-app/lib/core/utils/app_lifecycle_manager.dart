import 'package:flutter/material.dart';
import 'dart:developer' as dev;
import 'package:user_app/core/utils/location_util.dart';

/// 应用生命周期管理器
/// 用于处理应用前台/后台切换时的隐私合规问题
/// 特别是在后台运行时停止收集个人信息（位置、设备信息等）
class AppLifecycleManager with WidgetsBindingObserver {
  // 单例实例
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();

  factory AppLifecycleManager() => _instance;
  AppLifecycleManager._internal();

  /// 是否已初始化
  bool _isInitialized = false;

  /// 应用是否在前台
  bool _isInForeground = true;

  /// 后台开始时间
  DateTime? _backgroundStartTime;

  /// 位置服务是否在后台被暂停
  bool _isLocationPausedInBackground = false;

  /// 初始化生命周期管理器
  void initialize() {
    if (_isInitialized) {
      dev.log("AppLifecycleManager 已经初始化，跳过重复初始化");
      return;
    }

    dev.log("初始化 AppLifecycleManager");
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
  }

  /// 销毁生命周期管理器
  void dispose() {
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _isInitialized = false;
      dev.log("AppLifecycleManager 已销毁");
    }
  }

  /// 应用生命周期状态变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    dev.log("应用生命周期状态变化: ${state.name}");

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// 处理应用恢复到前台
  void _handleAppResumed() {
    dev.log("应用恢复到前台");

    if (!_isInForeground) {
      _isInForeground = true;

      // 计算后台运行时间
      if (_backgroundStartTime != null) {
        final backgroundDuration =
            DateTime.now().difference(_backgroundStartTime!);
        dev.log("应用在后台运行了 ${backgroundDuration.inSeconds} 秒");
      }

      // 恢复位置服务（如果之前被暂停）
      if (_isLocationPausedInBackground) {
        _resumeLocationService();
      }

      _backgroundStartTime = null;
    }
  }

  /// 处理应用暂停（进入后台）
  void _handleAppPaused() {
    dev.log("应用进入后台");

    if (_isInForeground) {
      _isInForeground = false;
      _backgroundStartTime = DateTime.now();

      // 暂停位置服务以符合隐私合规要求
      _pauseLocationService();
    }
  }

  /// 处理应用非活跃状态
  void _handleAppInactive() {
    dev.log("应用变为非活跃状态");
    // 通常是临时状态，如接听电话、通知栏下拉等
    // 不需要特殊处理，保持当前状态
  }

  /// 处理应用隐藏状态
  void _handleAppHidden() {
    dev.log("应用被隐藏");
    // 类似于暂停状态的处理
    if (_isInForeground) {
      _isInForeground = false;
      _backgroundStartTime = DateTime.now();
      _pauseLocationService();
    }
  }

  /// 处理应用分离状态
  void _handleAppDetached() {
    dev.log("应用分离");
    // 应用即将终止，确保停止所有服务
    _pauseLocationService();
  }

  /// 暂停位置服务
  /// 在应用进入后台时调用，符合隐私合规要求
  void _pauseLocationService() {
    try {
      dev.log("暂停位置服务 - 符合后台隐私合规要求");

      // 停止LocationUtil的位置收集
      LocationUtil.stopLocation();
      _isLocationPausedInBackground = true;

      dev.log("位置服务已暂停");
    } catch (e) {
      dev.log("暂停位置服务时发生错误: $e");
    }
  }

  /// 恢复位置服务
  /// 在应用恢复到前台时调用
  void _resumeLocationService() {
    try {
      dev.log("恢复位置服务 - 应用回到前台");

      // 注意：不自动重启位置服务
      // 只有在用户主动操作需要位置信息时才重新启动
      // 这样可以确保符合隐私合规要求
      _isLocationPausedInBackground = false;

      dev.log("位置服务状态已重置，等待用户主动请求位置信息");
    } catch (e) {
      dev.log("恢复位置服务时发生错误: $e");
    }
  }

  /// 获取当前是否在前台
  bool get isInForeground => _isInForeground;

  /// 获取后台开始时间
  DateTime? get backgroundStartTime => _backgroundStartTime;

  /// 获取位置服务是否在后台被暂停
  bool get isLocationPausedInBackground => _isLocationPausedInBackground;

  /// 手动标记位置服务恢复（用于用户主动请求位置时）
  void markLocationServiceResumed() {
    _isLocationPausedInBackground = false;
    dev.log("位置服务手动标记为已恢复");
  }
}
