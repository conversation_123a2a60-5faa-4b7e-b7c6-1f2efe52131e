import 'dart:async';
import 'dart:io';
import 'dart:developer' as dev;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 网络连接状态检查工具类
class ConnectionStatus {
  // 连接状态单例实例
  static final ConnectionStatus _instance = ConnectionStatus._internal();
  factory ConnectionStatus() => _instance;
  ConnectionStatus._internal();

  // 连接类型记录
  bool _hasConnection = true;
  ConnectivityResult _connectionType = ConnectivityResult.none;

  // 连接状态监听器
  final StreamController<bool> _connectionChangeController =
      StreamController.broadcast();

  // 获取连接状态流
  Stream<bool> get connectionChange => _connectionChangeController.stream;

  // 获取当前连接状态
  bool get hasConnection => _hasConnection;

  // 获取当前连接类型
  ConnectivityResult get connectionType => _connectionType;

  // 初始化并开始监听连接状态
  Future<void> initialize() async {
    try {
      // 获取连接监听流
      Connectivity()
          .onConnectivityChanged
          .listen((final dynamic connectivityResult) {
        // 处理不同版本的connectivity_plus可能返回的不同类型
        if (connectivityResult is List && connectivityResult.isNotEmpty) {
          // 如果是List类型，取第一个元素作为连接类型
          final result = connectivityResult.first;
          if (result is ConnectivityResult) {
            _connectionChange(result);
          } else {
            dev.log("无效的连接结果类型: $result");
            _connectionChange(ConnectivityResult.none);
          }
        } else if (connectivityResult is ConnectivityResult) {
          // 如果直接是ConnectivityResult类型
          _connectionChange(connectivityResult);
        } else {
          dev.log("未知的连接状态类型: $connectivityResult");
          _connectionChange(ConnectivityResult.none);
        }
      });
      await _checkConnection();
    } catch (e) {
      dev.log("网络连接监听器初始化失败: $e");
      _hasConnection = false;
    }
  }

  // 当连接状态变化时的处理
  void _connectionChange(final ConnectivityResult result) async {
    // 先更新连接类型
    _connectionType = result;

    // 检查是否真的有网络连接
    bool previousConnection = _hasConnection;
    bool hasConnection = await _hasRealConnection();

    // 只有当连接状态有变化时才通知监听器
    if (previousConnection != hasConnection) {
      _hasConnection = hasConnection;
      _connectionChangeController.add(hasConnection);
      dev.log(
          "网络连接状态变化: ${hasConnection ? '已连接' : '已断开'}, 类型: $_connectionType");
    }
  }

  // 检查连接状态
  Future<void> _checkConnection() async {
    try {
      // 获取当前连接状态
      final dynamic connectivityResult =
          await Connectivity().checkConnectivity();

      // 处理不同类型的返回结果
      if (connectivityResult is List && connectivityResult.isNotEmpty) {
        // 如果是List类型，取第一个元素
        final result = connectivityResult.first;
        if (result is ConnectivityResult) {
          _connectionType = result;
        } else {
          dev.log("无效的连接结果类型: $result");
          _connectionType = ConnectivityResult.none;
        }
      } else if (connectivityResult is ConnectivityResult) {
        // 直接是ConnectivityResult类型
        _connectionType = connectivityResult;
      } else {
        dev.log("检查连接状态: 未知类型 $connectivityResult");
        _connectionType = ConnectivityResult.none;
      }

      _hasConnection = await _hasRealConnection();
      dev.log(
          "当前网络连接状态: ${_hasConnection ? '已连接' : '已断开'}, 类型: $_connectionType");
    } catch (e) {
      dev.log("检查网络状态失败: $e");
      _connectionType = ConnectivityResult.none;
      _hasConnection = false;
    }
  }

  // 通过尝试连接可靠的服务器来检验是否真的有网络连接
  Future<bool> _hasRealConnection() async {
    if (_connectionType == ConnectivityResult.none) {
      return false;
    }

    try {
      // 尝试连接百度，检验是否能真正连接到互联网
      final result = await InternetAddress.lookup('baidu.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      dev.log("网络连接检查失败: 无法连接到服务器");
      return false;
    } catch (e) {
      dev.log("网络连接检查异常: $e");
      return false;
    }
  }

  // 手动检查当前连接状态，返回是否有网络连接
  Future<bool> checkConnection() async {
    await _checkConnection();
    return _hasConnection;
  }

  // 释放资源
  void dispose() {
    _connectionChangeController.close();
  }
}

/// 提供网络连接状态的Provider
final connectionStatusProvider = Provider<ConnectionStatus>((final ref) {
  final connectionStatus = ConnectionStatus();

  // 当Provider被销毁时关闭连接状态监听器
  ref.onDispose(() {
    connectionStatus.dispose();
  });

  return connectionStatus;
});

/// 网络连接状态的StateNotifier
class ConnectionNotifier extends StateNotifier<bool> {
  final ConnectionStatus _connectionStatus;
  StreamSubscription? _subscription;

  ConnectionNotifier(this._connectionStatus)
      : super(_connectionStatus.hasConnection) {
    // 初始化并监听连接变化
    _initialize();
  }

  Future<void> _initialize() async {
    await _connectionStatus.initialize();
    state = _connectionStatus.hasConnection;

    // 监听连接状态变化
    _subscription =
        _connectionStatus.connectionChange.listen((final hasConnection) {
      state = hasConnection;
    });
  }

  // 手动检查连接状态
  Future<bool> checkConnection() async {
    final hasConnection = await _connectionStatus.checkConnection();
    state = hasConnection;
    return hasConnection;
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}

/// 提供网络连接状态的StateNotifierProvider
final connectionStateProvider =
    StateNotifierProvider<ConnectionNotifier, bool>((final ref) {
  final connectionStatus = ref.watch(connectionStatusProvider);
  return ConnectionNotifier(connectionStatus);
});
