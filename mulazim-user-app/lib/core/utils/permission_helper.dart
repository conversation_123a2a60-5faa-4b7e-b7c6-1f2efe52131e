import 'package:bot_toast/bot_toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/permision_request.dart';
import 'package:user_app/core/utils/location_util.dart';
import 'package:user_app/core/widgets/dialogs/permission_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'dart:io' show Platform;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

/// 权限助手工具类
/// 提供友好的权限请求功能，同时显示权限用途说明
class PermissionHelper {
  /// 常用权限的说明文本
  static Map<Permission, String> _getDefaultExplanations() {
    return {
      Permission.camera: S.current.camera_permission_explanation,
      Permission.photos: S.current.photos_permission_explanation,
      Permission.storage: S.current.storage_permission_explanation,
      Permission.location: S.current.location_permission_explanation,
      Permission.microphone: S.current.microphone_permission_explanation,
      Permission.notification: S.current.notification_permission_explanation,
      // 注意：以下权限在permission_handler中可能没有直接对应，这里仅为完整性添加
      // 实际使用时需要根据具体需求进行适配
    };
  }

  /// iOS平台特有的权限检查方法
  ///
  /// [permission] 需要检查的权限
  /// [explanation] 自定义权限说明
  /// [title] 权限请求标题
  ///
  /// 返回权限是否被授予
  static Future<bool> _checkIOSPermission({
    required final Permission permission,
    final String? explanation,
    final String? title,
  }) async {
    final context = AppContext().currentContext;
    if (context == null) {
      return false;
    }
    // 检查mediaLibrary权限特殊情况
    if ((permission == Permission.photos || permission == Permission.storage) &&
        await Permission.mediaLibrary.status.isPermanentlyDenied) {
      await openPermisionSetting(
          context: context,
          permission: permission,
          explanation: explanation,
          title: title);
      return false;
    }
    final status = await permission.status;
    if (status.isDenied) {
      final result = await permission.request();
      if (result.isDenied) {
        BotToast.showText(text: S.current.unable_to_get_permission);
        return false;
      } else {
        return result.isGranted;
      }
    }

    if (status.isPermanentlyDenied) {
      await openPermisionSetting(
          context: context,
          permission: permission,
          explanation: explanation,
          title: title);
      return false;
    }

    return status.isGranted;
  }

  /// 请求单个权限
  ///
  /// [permission] 需要请求的权限
  /// [explanation] 权限用途说明，如果为null则使用默认说明
  /// [title] 对话框标题
  ///
  /// 返回权限是否被授予
  static Future<bool> requestPermission({
    required final Permission permission,
    final String? explanation,
    final String title = '权限请求',
  }) async {
    // iOS平台特殊处理
    if (Platform.isIOS) {
      return await _checkIOSPermission(
        permission: permission,
        explanation: explanation,
        title: title,
      );
    }
    // 先检查权限状态
    final status = await permission.status;

    // 如果权限已经被授予，直接返回true，无需显示对话框
    if (status.isGranted) {
      return true;
    }
    // 获取默认说明
    final defaultExplanations = _getDefaultExplanations();

    // 创建权限与说明的映射
    final explanations = <Permission, String>{
      permission:
          explanation ?? defaultExplanations[permission] ?? '需要此权限才能继续操作'
    };

    // 显示权限对话框并等待结果
    final results = await PermissionDialog.show(
      permissions: [permission],
      explanations: explanations,
      title: title,
    );

    // 返回权限是否被授予
    return results[permission] ?? false;
  }

  // /// 请求多个权限
  // ///
  // /// [permissions] 需要请求的权限列表
  // /// [explanations] 权限用途说明映射，未指定的权限使用默认说明
  // /// [title] 对话框标题
  // ///
  // /// 返回每个权限的授予状态
  // static Future<Map<Permission, bool>> requestPermissions({
  //   required final List<Permission> permissions,
  //   final Map<Permission, String>? explanations,
  //   final String title = '权限请求',
  // }) async {
  //   print("开始请求权限: $permissions");
  //   // iOS平台特殊处理相机和相册权限

  //   // 获取所有权限的当前状态
  //   final Map<Permission, bool> results = {};
  //   final List<Permission> permissionsToRequest = [];

  //   if (Platform.isIOS) {
  //     for (final permission in permissions) {
  //       results[permission] = await _checkIOSPermission(
  //         permission: permission,
  //         explanation: explanations?[permission],
  //         title: title,
  //       );
  //     }
  //     return results;
  //   }
  //   // 检查每个权限的状态
  //   for (final permission in permissions) {
  //     final status = await permission.status;
  //     print("权限 $permission 状态: ${status.name}");

  //     if (status.isGranted) {
  //       // 如果权限已授予，直接记录结果
  //       print("权限 $permission 已授予");
  //       results[permission] = true;
  //     } else {
  //       // 如果权限未授予，加入待请求列表
  //       print("权限 $permission 未授予，需要请求");
  //       permissionsToRequest.add(permission);
  //     }
  //   }

  //   // 如果所有权限都已授予，直接返回结果
  //   if (permissionsToRequest.isEmpty) {
  //     print("所有权限都已授予，无需请求");
  //     return results;
  //   }

  //   print("待请求的权限: $permissionsToRequest");

  //   // 获取默认说明
  //   final defaultExplanations = _getDefaultExplanations();

  //   // 合并自定义说明和默认说明，但只针对需要请求的权限
  //   final mergedExplanations = <Permission, String>{};
  //   for (final permission in permissionsToRequest) {
  //     // 优先使用自定义说明，其次使用默认说明，最后使用默认文本
  //     mergedExplanations[permission] =
  //         (explanations != null && explanations.containsKey(permission))
  //             ? explanations[permission]!
  //             : defaultExplanations[permission] ?? '需要此权限才能继续操作';
  //   }

  //   // 如果有需要请求的权限，显示对话框
  //   if (permissionsToRequest.isNotEmpty) {
  //     print("显示权限对话框，请求 ${permissionsToRequest.length} 个权限");
  //     final dialogResults = await PermissionDialog.show(
  //       permissions: permissionsToRequest,
  //       explanations: mergedExplanations,
  //       title: title,
  //     );

  //     print("对话框结果: $dialogResults");

  //     // 合并结果
  //     results.addAll(dialogResults);
  //   }

  //   print("最终权限结果: $results");
  //   // 返回所有权限的结果
  //   return results;
  // }

  /// 请求相册权限
  ///
  /// [explanation] 自定义权限说明，如果为null则使用默认说明
  ///
  /// 返回权限是否被授予
  static Future<bool> requestPhotosPermission({
    final String? explanation,
  }) async {
    // iOS平台特殊处理
    if (Platform.isIOS) {
      return await _checkIOSPermission(
        permission: Permission.photos,
        explanation: explanation,
        title: S.current.photos_permission_title,
      );
    }
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final permission = androidInfo.version.sdkInt <= 32
        ? Permission.storage
        : Permission.photos;
    return await requestPermission(
      permission: permission,
      explanation: explanation,
      title: S.current.photos_permission_title,
    );
  }

  /// 请求相机权限
  ///
  /// [explanation] 自定义权限说明，如果为null则使用默认说明
  ///
  /// 返回权限是否被授予
  static Future<bool> requestCameraPermission({
    final String? explanation,
  }) async {
    // iOS平台特殊处理
    if (Platform.isIOS) {
      return await _checkIOSPermission(
        permission: Permission.camera,
        explanation: explanation,
        title: S.current.camera_permission_title,
      );
    }

    return await requestPermission(
      permission: Permission.camera,
      explanation: explanation,
      title: S.current.camera_permission_title,
    );
  }

  /// 请求存储权限
  ///
  /// [explanation] 自定义权限说明，如果为null则使用默认说明
  ///
  /// 返回权限是否被授予
  static Future<bool> requestStoragePermission({
    final String? explanation,
  }) async {
    return await requestPermission(
      permission: Permission.storage,
      explanation: explanation,
      title: S.current.storage_permission_title,
    );
  }

  /// 请求位置权限
  ///
  /// [explanation] 自定义权限说明，如果为null则使用默认说明
  ///
  /// 返回权限是否被授予且定位服务可用
  static Future<bool> requestLocationPermission({
    final String? explanation,
  }) async {
    // 先请求基本定位权限
    final permissionGranted = await requestPermission(
      permission: Permission.location,
      explanation: explanation,
      title: S.current.location_permission_title,
    );

    if (!permissionGranted) {
      return false;
    }

    // 权限已授予，检查GPS定位服务状态
    return await _checkLocationServiceStatus();
  }

  /// 检查定位服务状态
  /// 通过尝试获取位置来判断定位服务是否可用
  static Future<bool> _checkLocationServiceStatus() async {
    try {
      // 先检查权限状态
      final permissionStatus = await Permission.location.status;

      // 直接调用LocationUtil获取位置信息
      final locationData = await LocationUtil.getLocationOncePlatform();
      final issueType =
          _detectLocationIssueType(locationData, permissionStatus);

      print(
          "权限状态: ${permissionStatus.name}, 错误码: ${locationData?['errorCode']}, 问题类型: ${issueType.name}");

      // 添加详细的错误码说明
      if (locationData?['errorCode'] != null) {
        final errorCode = locationData!['errorCode'] as int;
        final errorMessage = _getErrorCodeMessage(errorCode);
        print("错误码含义: $errorMessage");
      }

      switch (issueType) {
        case LocationIssueType.none:
          return true; // 定位服务正常

        case LocationIssueType.serviceDisabled:
          // 定位服务关闭，显示提示对话框
          await _showLocationServiceDialog(locationData?['errorCode']);
          return false;

        case LocationIssueType.permissionDenied:
        case LocationIssueType.networkError:
        case LocationIssueType.unknown:
        default:
          // 其他问题，权限已授予但可能有其他问题
          return true;
      }
    } catch (e) {
      print("检查定位服务状态异常: $e");
      return true; // 出现异常时默认认为服务可用
    }
  }

  /// 检测定位问题类型
  static LocationIssueType _detectLocationIssueType(
      Map<String, dynamic>? locationData, PermissionStatus permissionStatus) {
    if (locationData == null) {
      return LocationIssueType.unknown;
    }

    final errorCode = locationData['errorCode'];
    if (errorCode == null) {
      return LocationIssueType.none; // 没有错误
    }

    // 根据高德地图定位错误码和权限状态综合判断问题类型
    switch (errorCode as int) {
      case 12:
        // 错误码12：缺少定位权限
        // 如果权限已授予，可能是其他定位相关问题
        if (permissionStatus.isGranted) {
          return LocationIssueType.serviceDisabled; // 可能是GPS服务问题
        } else {
          return LocationIssueType.permissionDenied; // 权限被拒绝
        }
      case 13: // 定位失败，GPS不可用且无WIFI/基站信息
      case 14: // GPS定位失败，GPS状态差
      case 18: // WIFI关闭且设置为飞行模式
      case 19: // 没插sim卡且WIFI关闭
        return LocationIssueType.serviceDisabled; // GPS/定位服务问题
      case 2: // 定位失败，网络不可用
      case 3: // 服务端定位失败
      case 4: // 网络连接失败或超时
      case 6: // 定位服务返回定位失败
      case 11: // 定位时的基站信息错误
        return LocationIssueType.networkError; // 网络相关错误
      case 0: // 定位成功
        return LocationIssueType.none;
      case 1: // 系统错误
      case 5: // 请求被恶意劫持
      case 7: // KEY鉴权失败
      case 8: // Android exception常规错误
      case 9: // 定位初始化异常
      case 10: // 定位客户端启动失败
      case 15: // 定位结果被模拟
      case 16: // 无可用地理围栏
      case 20: // 模糊定位异常，用户设置应用位置权限为"大致位置"时定位异常
        return LocationIssueType.unknown; // 其他错误
      default:
        return LocationIssueType.unknown; // 其他错误
    }
  }

  /// 获取高德地图定位错误码的含义
  static String _getErrorCodeMessage(int errorCode) {
    switch (errorCode) {
      case 0:
        return "定位成功";
      case 1:
        return "一些重要参数为空，如context";
      case 2:
        return "定位失败，由于仅扫描到单个wifi，且没有基站信息";
      case 3:
        return "获取到的请求参数为空，可能获取过程中出现异常";
      case 4:
        return "请求服务器过程中的异常，多为网络情况差，链路不通导致";
      case 5:
        return "请求被恶意劫持，定位结果解析失败";
      case 6:
        return "定位服务返回定位失败";
      case 7:
        return "KEY鉴权失败";
      case 8:
        return "Android exception常规错误";
      case 9:
        return "定位初始化时出现异常";
      case 10:
        return "定位客户端启动失败";
      case 11:
        return "定位时的基站信息错误";
      case 12:
        return "缺少定位权限";
      case 13:
        return "定位失败，由于未获得WIFI列表和基站信息，且GPS当前不可用";
      case 14:
        return "GPS定位失败，由于设备当前GPS状态差";
      case 15:
        return "定位结果被模拟导致定位失败";
      case 16:
        return "当前POI检索条件、行政区划检索条件下，无可用地理围栏";
      case 18:
        return "定位失败，由于手机WIFI功能被关闭同时设置为飞行模式";
      case 19:
        return "定位失败，由于手机没插sim卡且WIFI功能被关闭";
      case 20:
        return "模糊定位异常，用户设置应用位置权限为\"大致位置\"时定位异常";
      default:
        return "未知错误(错误码: $errorCode)";
    }
  }

  /// 显示定位服务关闭提示对话框
  static Future<void> _showLocationServiceDialog([int? errorCode]) async {
    final context = AppContext().currentContext;
    if (context == null) return;

    // 根据错误码生成具体的提示信息
    String message = "请在系统设置中开启定位服务";
    if (errorCode != null) {
      switch (errorCode) {
        case 13:
          message = "请开启设备的GPS、WIFI或插入SIM卡";
          break;
        case 14:
          message = "GPS信号较弱，请到开阔场所重试，或在系统设置中开启WIFI定位";
          break;
        case 18:
          message = "请关闭飞行模式并开启WIFI";
          break;
        case 19:
          message = "请插入SIM卡或开启WIFI";
          break;
        default:
          message = "请在系统设置中开启定位服务";
      }
    }

    // 获取当前语言方向
    final currentLang =
        ProviderScope.containerOf(context).read(languageProvider);
    final isRtl = currentLang == "ug";

    await showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        elevation: 8,
        child: Directionality(
          textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
          child: Container(
            padding: EdgeInsets.all(24.w),
            width: MediaQuery.of(context).size.width - 48.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题栏
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: AppColors.baseGreenColor,
                      size: 24.sp,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        S.current.location_permission_title,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          fontFamily: "UkijTuzTom",
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24.h),

                // 图标
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.r),
                  child: Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: AppColors.baseGreenColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/images/gps_tips.png',
                        width: 48.w,
                        height: 48.w,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果图片加载失败，显示图标
                          return Icon(
                            Icons.location_on,
                            color: AppColors.baseGreenColor,
                            size: 48.sp,
                          );
                        },
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 24.h),

                // 提示文本
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 16.sp,
                    height: 1.4,
                    color: Colors.black87,
                    fontFamily: "UkijTuzTom",
                  ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: 24.h),

                // 按钮行
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 取消按钮
                    Expanded(
                      flex: 1,
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: AppColors.baseGreenColor),
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          S.current.dialog_text_no,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.baseGreenColor,
                            fontFamily: "UkijTuzTom",
                          ),
                        ),
                      ),
                    ),

                    SizedBox(width: 16.w),

                    // 去设置按钮
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _openLocationSettings();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.baseGreenColor,
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          S.current.got_it,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white,
                            fontFamily: "UkijTuzTom",
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 打开系统位置设置
  static Future<void> _openLocationSettings() async {
    try {
      if (Platform.isIOS) {
        // iOS平台：尝试打开系统隐私与安全设置页面
        const iosSettingsUrl = 'app-settings:';
        if (await canLaunchUrl(Uri.parse(iosSettingsUrl))) {
          await launchUrl(
            Uri.parse(iosSettingsUrl),
            mode: LaunchMode.externalApplication,
          );
        } else {
          // 如果无法打开特定设置，使用应用设置
          await openAppSettings();
        }
      } else if (Platform.isAndroid) {
        // Android平台：使用原生方法打开系统位置设置
        const platform = MethodChannel('com.almas.dinner');
        try {
          await platform.invokeMethod('openLocationSettings');
          print("成功调用原生方法打开位置设置");
        } catch (e) {
          print("原生方法打开位置设置失败: $e，尝试备用方案");

          // 备用方案：使用url_launcher
          try {
            await launchUrl(
              Uri.parse(
                  'intent://android.settings.LOCATION_SOURCE_SETTINGS#Intent;package=com.android.settings;end'),
              mode: LaunchMode.externalApplication,
            );
          } catch (urlError) {
            print("URL方案也失败: $urlError，使用应用设置");
            await openAppSettings();
          }
        }
      } else {
        // 其他平台：打开应用设置
        await openAppSettings();
      }
    } catch (e) {
      print("打开位置设置失败: $e，使用应用设置作为备用方案");
      // 如果所有方法都失败，则打开应用设置页面作为备用方案
      try {
        await openAppSettings();
      } catch (settingsError) {
        print("打开应用设置也失败: $settingsError");
        BotToast.showText(text: "无法打开设置页面，请手动进入系统设置开启定位服务");
      }
    }
  }
}

/// 定位问题类型枚举
enum LocationIssueType {
  none, // 无问题
  permissionDenied, // 权限被拒绝
  serviceDisabled, // 定位服务关闭
  networkError, // 网络错误
  unknown, // 未知错误
}
