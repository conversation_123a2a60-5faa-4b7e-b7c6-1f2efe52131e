import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:user_app/routes/paths.dart';
// import 'package:shipper_flutter/service/navigation_service.dart';
// import 'package:shipper_flutter/service/tts_service.dart';
// import 'package:shipper_flutter/utils/storage.dart';
//
// import '../views/user/notify_detail.dart';
import 'aliyun_sls.dart';

/// 原生通信管理器
/// 用于处理来自原生平台的方法调用和消息通信
class NativeCommunicator {
  /// 与原生平台通信的方法通道
  static const MethodChannel _channel = MethodChannel("com.almas.dinner");
  
  /// 全局上下文，用于导航和UI更新
  static BuildContext? thisContext;

  /// 初始化方法通道
  /// [context] 用于存储全局上下文
  /// 设置方法通道处理器，接收原生平台的调用
  static void setupChannel(BuildContext context) {
    thisContext = context;
    _channel.setMethodCallHandler(_handleMethodCall);
  }

  /// 处理来自原生平台的方法调用
  /// [call] 包含方法名和参数的调用对象
  /// 
  /// 目前支持的方法：
  /// - callFlutterFunction: 处理各种原生回调
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'callFlutterFunction':
        return _flutterFunctionToBeCalled(call.arguments);
      default:
        throw PlatformException(
            code: 'Unimplemented',
            details: 'Method ${call.method} not implemented.');
    }
  }

  /// 处理原生平台传来的参数和指令
  /// [arguments] 原生平台传递的参数
  /// 
  /// 支持以下功能：
  /// - 空参数：导航到主页
  /// - soundString：处理TTS语音播放
  /// - lang：设置语言
  /// - clear_voice：清除语音
  /// - page：处理页面导航（聊天页面、通知详情等）
  static _flutterFunctionToBeCalled(dynamic arguments) {
    // 处理空参数情况
    if(arguments == null || arguments.toString().isEmpty) {
      // MainPageTabs.navigateToTab(thisContext!, MainPageTabs.home);
      thisContext!.go(AppPaths.mainPage);
    }
    
    // 处理页面导航
    else {
      if(Platform.isIOS) {
        // iOS平台页面导航处理
         if(arguments["page"] == "chat_page"){
          thisContext!.push(
            AppPaths.chatRoomPage,
            extra: {
              'order_id': (arguments["order_id"] ?? "0").toString(),
              'name': (arguments["restaurant_name"] ?? "").toString(),
            },
          );
        }
      } else {
        // Android平台页面导航处理
        Map<String, dynamic> jpushData = jsonDecode(
          arguments.toString().replaceAll("'", "\"")
        );

 
        /*
        {
          "page":"chat_page",
          "order_id":"9988",
          "restaurant_name":"测试餐厅名称"
        }
         */

        if(jpushData['page'] == "chat_page") {
          thisContext!.push(
            AppPaths.chatRoomPage,
            extra: {
              'order_id': (jpushData["order_id"] ?? "0").toString(),
              'name': (jpushData["restaurant_name"] ?? "").toString(),
            },
          );
        } else if(jpushData['page'] == "home_notify") {
          // NavigationService.push(const NotifyDetail());
        }
      }
    }    

    // 记录Socket连接状态日志
    Map<String, dynamic> socketData = jsonDecode(
      arguments.toString().replaceAll("'", "\"")
    );
    DateTime now = DateTime.now();
    String nowTimeStr = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    socketData.putIfAbsent('time', () => nowTimeStr);
    
    // 发送日志到阿里云SLS
    AliyunSls().sendLog(
      'socket链接状态', 
      {'param': socketData}, 
      socketData['state'] > 2 ? 'error' : 'success'
    );

    return "Received in Flutter: $arguments";
  }
}