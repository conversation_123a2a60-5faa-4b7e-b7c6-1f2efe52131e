// Copyright (c) 2025 Almas Inc All rights reserved.
// 修改时间: 2025/02/06 16:32
// 作者: Elshat
// 文件: environment_config.dart
// 描述: 环境配置文件

import 'package:flutter/foundation.dart';

/// 环境枚举
enum Environment {
  /// 开发环境
  development,

  /// 测试环境
  staging,

  /// 生产环境
  production,
}

/// 环境配置类
///
/// 提供应用程序不同环境的配置信息
class EnvironmentConfig {
  // 私有构造函数，防止实例化
  EnvironmentConfig._();

  /// 强制指定的环境（通过命令行参数设置）
  static Environment? _forcedEnvironment;

  /// 设置强制环境（用于命令行参数）
  static void setForcedEnvironment(String? env) {
    if (env != null) {
      switch (env.toLowerCase()) {
        case 'development':
        case 'dev':
          _forcedEnvironment = Environment.development;
          break;
        case 'staging':
        case 'test':
          _forcedEnvironment = Environment.staging;
          break;
        case 'production':
        case 'prod':
          _forcedEnvironment = Environment.production;
          break;
        default:
          _forcedEnvironment = null;
      }
    }
  }

  /// 当前环境
  ///
  /// 优先级：
  /// 1. 命令行参数指定的环境（_forcedEnvironment）
  /// 2. 自动根据Flutter的构建模式选择环境：
  ///    - 在release模式下使用production环境
  ///    - 在profile模式下使用staging环境
  ///    - 在debug模式下使用development环境
  /// 这样可以避免在发布生产版本时忘记修改环境设置
  static Environment get currentEnvironment {
    // 如果有强制指定的环境，优先使用
    if (_forcedEnvironment != null) {
      return _forcedEnvironment!;
    }

    // 直接根据Flutter的构建模式选择环境
    if (kReleaseMode) {
      return Environment.production;
    } else if (kProfileMode) {
      return Environment.staging;
    } else {
      return Environment.development;
    }
  }

  /// 是否为开发环境
  static bool get isDevelopment =>
      currentEnvironment == Environment.development;

  /// 是否为测试环境
  static bool get isStaging => currentEnvironment == Environment.staging;

  /// 是否为生产环境
  static bool get isProduction => currentEnvironment == Environment.production;

  /// 获取当前环境名称
  static String get environmentName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'development';
      case Environment.staging:
        return 'staging';
      case Environment.production:
        return 'production';
    }
  }

  /// 环境配置
  static final Map<Environment, Map<String, dynamic>> _configs = {
    Environment.development: {
       'apiBaseUrl': 'https://smart.mulazim.com/',
      'socketUrl': 'wss://chat-socket.mulazim.com',
      'connectTimeout': 30000, // 开发环境允许更长连接时间，便于调试
      'receiveTimeout': 15000, // 开发环境允许更长响应时间
      'enableLogging': true,
      'maxRetries': 2, // 开发环境减少重试次数，快速暴露问题
      'retryDelay': 1500, // 开发环境更快重试，提高调试效率
      'wechatPayType': 'preview',
    },
    Environment.staging: {
      'apiBaseUrl': 'https://smart.d.almas.biz/',
      'socketUrl': 'wss://chat-socket.d.almas.biz',
      'connectTimeout': 12000, // 测试环境适中的连接时间
      'receiveTimeout': 6000, // 测试环境适中的响应时间
      'enableLogging': true, // 测试环境开启日志便于问题排查
      'maxRetries': 3, // 测试环境标准重试次数
      'retryDelay': 1000, // 测试环境标准重试延迟
      'wechatPayType': 'preview',
    },
    Environment.production: {
      'apiBaseUrl': 'https://smart.mulazim.com/',
      'socketUrl': 'wss://chat-socket.mulazim.com',
      'connectTimeout': 30000, // 生产环境较短连接时间，快速失败
      'receiveTimeout': 15000, // 生产环境较短响应时间，提升用户体验
      'enableLogging': false,
      'maxRetries': 4, // 生产环境更多重试次数，提高成功率
      'retryDelay': 1500, // 生产环境较长重试延迟，避免服务器压力
      'wechatPayType': 'release',
    },
  };

  /// 获取当前环境配置
  static Map<String, dynamic> get config {
    final env = currentEnvironment;
    return _configs[env]!;
  }

  /// 获取API基础URL
  static String get apiBaseUrl => config['apiBaseUrl'] as String;

  /// 获取Socket URL
  static String get socketUrl => config['socketUrl'] as String;

  /// 获取连接超时时间（毫秒）
  static int get connectTimeout => config['connectTimeout'] as int;

  /// 获取接收超时时间（毫秒）
  static int get receiveTimeout => config['receiveTimeout'] as int;

  /// 是否启用日志
  static bool get enableLogging => config['enableLogging'] as bool;

  /// 获取最大重试次数
  static int get maxRetries => config['maxRetries'] as int;

  /// 获取重试延迟时间（毫秒）
  static int get retryDelay => config['retryDelay'] as int;

  /// 获取微信支付类型
  static String get wechatPayType => config['wechatPayType'] as String;

  /// 获取环境信息摘要，用于调试
  ///
  /// 返回一个包含当前环境名称和主要配置的字符串
  static String getEnvironmentSummary() {
    return '''
当前环境: ${environmentName.toUpperCase()}
API地址: $apiBaseUrl
Socket地址: $socketUrl
日志启用: $enableLogging
构建模式: ${kReleaseMode ? "RELEASE" : kProfileMode ? "PROFILE" : "DEBUG"}
''';
  }
}
