/// Copyright (c) 2025 Almas Inc All rights reserved.
/// 修改时间: 2025/02/06 16:32
/// 作者: Elshat
/// 文件: config.dart
/// 描述: 项目配置文件中心，导出所有配置

// 导入地图配置
import 'map_config.dart';

// 导出所有配置文件
export 'api_config.dart';
export 'app_constants.dart';
export 'environment_config.dart';
export 'map_config.dart';

/// 应用全局配置
/// 此类包含应用程序的全局配置信息
class Config {
  /// 私有构造函数，防止实例化
  Config._();

  /// 应用版本
  static const String appVersion = 'V3.1.96';

  /// 应用名称
  static const String appName = 'Mulazim';

  /// 是否启用日志
  static const bool enableLogging = true;

  /// 是否启用分析
  static const bool enableAnalytics = true;

  /// 高德地图iOS Key
  static String get amapIOSKey => MapConfig.amapIOSKey;

  /// 高德地图Android Key
  static String get amapAndroidKey => MapConfig.amapAndroidKey;

  /// 地图应用模型
  static MapModel get mapMpdel => MapConfig.mapModel;
}
