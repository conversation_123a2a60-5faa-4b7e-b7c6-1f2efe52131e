/// Copyright (c) 2025 Almas Inc All rights reserved.
/// 修改时间: 2025/02/06 16:32
/// 作者: Elshat
/// 文件: map_config.dart
/// 描述: 地图配置文件

/// 地图配置类
class MapConfig {
  // 私有构造函数，防止实例化
  MapConfig._();
  
  /// 高德地图官网 : https://lbs.amap.com
  /// 高德地图IOSkey
  static const String amapIOSKey = 'b246804aa0216430d4e64979bd0bb1e0';

  /// 高德地图Androidkey
  static const String amapAndroidKey = '3c2d8bc0add227e536a43db754f23db1';

  /// 第三方地图应用
  static final mapModel = MapModel(
    /// 高德地图
    amap: "itms-apps://itunes.apple.com/app/id461703208",
    /// 腾讯地图
    qqMap: "itms-apps://itunes.apple.com/app/id481623196",
    /// 百度地图
    baiduMap: "itms-apps://itunes.apple.com/app/id452186370"
  );
}

/// 地图应用模型
class MapModel {
  /// 高德地图应用商店链接
  final String amap;
  
  /// 百度地图应用商店链接
  final String baiduMap;
  
  /// 腾讯地图应用商店链接
  final String qqMap;
  
  /// 构造函数
  const MapModel({
    required this.amap,
    required this.baiduMap,
    required this.qqMap,
  });
}
