import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/home/<USER>/self_take_provider.dart';
import 'package:user_app/features/search/widgets/xm_start_rating.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class SelfTakePage extends ConsumerStatefulWidget {
  SelfTakePage({super.key, required this.buildingId});
  int buildingId;

  @override
  ConsumerState createState() => _SelfTakePageState();
}

class _SelfTakePageState extends ConsumerState<SelfTakePage> {

  int page = 1;
  bool isLoadingMore = false;  // 添加加载状态变量

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      ref.read(selfTakeProvider.notifier).fetchSelfTakeData(buildingId: widget.buildingId, page: 1, limit: 10);
      ref.read(selfTakeProvider);
    });
  }


  // 方法在滚动到底部时触发
  Future<void> _onScrollToBottom() async {
    if (isLoadingMore) return;  // 如果正在加载，直接返回
    
    setState(() {
      isLoadingMore = true;  // 设置加载状态为true
    });
    
    page = page + 1;
    
    await ref.read(selfTakeProvider.notifier).fetchSelfTakeData(
        page: page,
        buildingId: widget.buildingId,
        limit: 10
    );
    
    setState(() {
      isLoadingMore = false;  // 加载完成后设置状态为false
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        centerTitle: true,
        title: Text(S.current.my_self_take,style: TextStyle(fontSize: titleSize),),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification notification) {
          if (notification is ScrollUpdateNotification) {
            // 当滚动到距离底部100像素时就开始加载
            if (notification.metrics.pixels >= notification.metrics.maxScrollExtent - 100) {
              if (ref.watch(canSelfTakeDataProvider) && !isLoadingMore){
                _onScrollToBottom();
              }
            }
          }
          return true;
        },
        child: ref.watch(selfTakeProvider).when(data: (data){
          return SingleChildScrollView(
            child: Column(
              children: [
                ...List.generate((ref.watch(selfTakeListProvider)).length, (index)=>_cardItem(ref.watch(selfTakeListProvider)[index],index)),
                if (isLoadingMore)  // 添加底部加载提示
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 15.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20.w,
                          height: 20.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.w,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.baseGreenColor),
                          ),
                        ),
                        SizedBox(width: 10.w),
                        Text(
                          S.current.loading_wait,
                          style: TextStyle(
                            fontSize: mainSize,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }, error: (error, stackTrace) {
          // 数据加载失败，显示错误信息
          return Center(child: Text('address Page Error: $error'));
        }, loading: () {
          // 正在加载，显示加载指示器
          return LoadingWidget();
        },),
      ),
    );
  }

  Widget _cardItem(DataList dataList,int index){
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': dataList.id ?? 0,
            'buildingId': ref.watch(homeNoticeProvider).value?.location?.id ?? 0,
            'ids': [],
          },
        );
      },
      child: Column(
        children: [
          if(index == 0)SizedBox(height: 10.w,),
          Container(
            margin: EdgeInsets.only(right:10.w, left: 10.w, bottom: 10.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.w),
              color: Colors.white,
            ),
            child: Column(
              children: [
                _restaurantInfo(dataList),
                SizedBox(height: 10.w,),
                _foodInfo(dataList.foods,dataList.id ?? 0),
              ],
            ),
          ),
        ],
      ),


    );
  }

  Widget _restaurantInfo(DataList dataList){
    return Container(
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10.w),
            child: PrefectImage(
              imageUrl: dataList.logo ?? '',
              width: 68.w,
              height: 68.w,
              fit: BoxFit.fitHeight,
            ),
          ),

          SizedBox(width: 10.w,),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${dataList.restaurantName}',style: TextStyle(fontSize: mainSize,color: Colors.black,fontWeight: FontWeight.bold),maxLines: 1,overflow: TextOverflow.ellipsis,),
                SizedBox(height: 10.w,),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    XMStartRating(rating: dataList.starAvg ?? 0,size: soBigSize,spacing: 0,totalRating: 5,selectColor: AppColors.baseOrangeColor,),
                    if(dataList.canMulazimTake == 1)Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(width: 5.w,),
                        _borderContainer(MarketTag(title: S.current.deliver_mulazim,color:'10C35A')),
                      ],
                    ),
                    if(dataList.canSelfTake == 1)Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(width: 5.w,),
                        _borderContainer(MarketTag(title: S.current.my_self_take,color:'FF4F27')),
                      ],
                    )
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _borderContainer(MarketTag takeTag){
    return Container(
      padding: EdgeInsets.symmetric(vertical: 2.w,horizontal: 5.w),
      decoration: BoxDecoration(
          border: Border.all(
            color: FormatUtil.parseColor(takeTag.color ?? '00FF00'),
            width: 0.5.w,
          ),
          borderRadius: BorderRadius.circular(4.w),
      ),
      child: Text(takeTag.title ?? '',style: TextStyle(color: FormatUtil.parseColor(takeTag.color ?? '00FF00'),fontSize: littleSize),),
    );
  }

  Widget _foodInfo(List<Foods>? foods, int restaurantId){
    return SingleChildScrollView(
      scrollDirection:Axis.horizontal,
      child: Row(
        children: List.generate((foods ?? []).length, (index)=>_foodCardItem(foods![index], index,restaurantId)),
      ),
    );
  }

  Widget _foodCardItem(Foods foods, int index, int restaurantId){
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': restaurantId,
            'buildingId': ref.watch(homeNoticeProvider).value?.location?.id ?? 0,
            'ids': [foods.id ?? 0],
          },
        );
      },
      child: Container(
        width: 168.w,
        margin: 5 != index ? EdgeInsets.only(left: 10.w):EdgeInsets.zero,
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topRight:Radius.circular(10.w),
                topLeft:Radius.circular(10.w),
              ),
              child: PrefectImage(
                imageUrl: foods.image ?? '',
                width: 168.w,
                height: 130.w,
                fit: BoxFit.fill,
              ),
            ),
            SizedBox(height: 5.w,),
            Container(
                alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                child:Text('${foods.foodsName}',textAlign:TextAlign.start,style: TextStyle(fontSize: mainSize,color: Colors.black),maxLines: 1,overflow: TextOverflow.ellipsis,)
            ),
            SizedBox(height: 8.w,),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text('￥${foods.price}',style: TextStyle(color: Colors.red,fontSize: titleSize,fontWeight: FontWeight.bold),),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('${S.current.sales_count} ${foods.monthOrderCount}',style: TextStyle(color: Colors.black,fontSize: secondSize),),
                    SizedBox(width: 3.w,),
                    Image.asset(
                      'assets/images/huo.png',
                      fit: BoxFit.fill,
                      width: 15.w,
                      height: 17.w,
                    ),
                    SizedBox(width: 3.w,),

                  ],
                ),
              ],
            ),
            SizedBox(height: 5.w,),
          ],
        ),
      ),
    );
  }

}
