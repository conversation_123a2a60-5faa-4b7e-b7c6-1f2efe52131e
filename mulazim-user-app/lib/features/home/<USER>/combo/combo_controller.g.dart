// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'combo_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$comboControllerHash() => r'50da6efeb1276aff6471f1e8e596a24bbf8dc7f9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$ComboController
    extends BuildlessAutoDisposeNotifier<ComboState> {
  late final int buildingId;

  ComboState build(
    int buildingId,
  );
}

/// See also [ComboController].
@ProviderFor(ComboController)
const comboControllerProvider = ComboControllerFamily();

/// See also [ComboController].
class ComboControllerFamily extends Family<ComboState> {
  /// See also [ComboController].
  const ComboControllerFamily();

  /// See also [ComboController].
  ComboControllerProvider call(
    int buildingId,
  ) {
    return ComboControllerProvider(
      buildingId,
    );
  }

  @override
  ComboControllerProvider getProviderOverride(
    covariant ComboControllerProvider provider,
  ) {
    return call(
      provider.buildingId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'comboControllerProvider';
}

/// See also [ComboController].
class ComboControllerProvider
    extends AutoDisposeNotifierProviderImpl<ComboController, ComboState> {
  /// See also [ComboController].
  ComboControllerProvider(
    int buildingId,
  ) : this._internal(
          () => ComboController()..buildingId = buildingId,
          from: comboControllerProvider,
          name: r'comboControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$comboControllerHash,
          dependencies: ComboControllerFamily._dependencies,
          allTransitiveDependencies:
              ComboControllerFamily._allTransitiveDependencies,
          buildingId: buildingId,
        );

  ComboControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.buildingId,
  }) : super.internal();

  final int buildingId;

  @override
  ComboState runNotifierBuild(
    covariant ComboController notifier,
  ) {
    return notifier.build(
      buildingId,
    );
  }

  @override
  Override overrideWith(ComboController Function() create) {
    return ProviderOverride(
      origin: this,
      override: ComboControllerProvider._internal(
        () => create()..buildingId = buildingId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        buildingId: buildingId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<ComboController, ComboState>
      createElement() {
    return _ComboControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ComboControllerProvider && other.buildingId == buildingId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, buildingId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ComboControllerRef on AutoDisposeNotifierProviderRef<ComboState> {
  /// The parameter `buildingId` of this provider.
  int get buildingId;
}

class _ComboControllerProviderElement
    extends AutoDisposeNotifierProviderElement<ComboController, ComboState>
    with ComboControllerRef {
  _ComboControllerProviderElement(super.provider);

  @override
  int get buildingId => (origin as ComboControllerProvider).buildingId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
