import 'package:user_app/data/models/home/<USER>';

class ComboState {
  final List<ComboFoodsModel> comboList;
  final bool isLoading;
  final bool hasMore;
  final int page;
  final int limit;
  final String? error;
  final bool isInitialLoading;

  const ComboState({
    this.comboList = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.page = 1,
    this.limit = 10,
    this.error,
    this.isInitialLoading = true,
  });

  ComboState copyWith({
    List<ComboFoodsModel>? comboList,
    bool? isLoading,
    bool? hasMore,
    int? page,
    String? error,
    bool? isInitialLoading,
  }) {
    return ComboState(
      comboList: comboList ?? this.comboList,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore, 
      page: page ?? this.page,
      limit: limit,
      error: error ?? this.error,
      isInitialLoading: isInitialLoading ?? this.isInitialLoading,
    );
  }
}
