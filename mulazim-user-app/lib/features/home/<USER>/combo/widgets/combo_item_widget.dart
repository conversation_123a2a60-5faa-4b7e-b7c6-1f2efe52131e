import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/routes/paths.dart';

class ComboItemWidget extends StatelessWidget {
  final int buildingId;
  final ComboFoodsModel item;
  const ComboItemWidget({
    super.key,
    required this.buildingId,
    required this.item,
  });

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': item.restaurantId,
            'buildingId': buildingId,
            'ids': [item.id],
          },
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 10.r, vertical: 5.r),
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                      imageUrl: item.image ?? '',
                      width: 85.w,
                      height: 85.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                  if (item.foodType == 2)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              const Color(0xFF9DE797),
                              const Color(0xD900C040),
                            ],
                            stops: const [0.12776, 0.61203],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  const Color(0x2900FF09), // rgba(0,255,9,0.16)
                              offset: Offset(0, 4),
                              blurRadius: 11,
                            ),
                          ],
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r),
                          ),
                        ),
                        child: Text(
                          S.current.comboTitle,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 11.sp,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      item.name ?? '',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      item.restaurantName ?? '',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${S.current.sales_count}: ${item.monthOrderCount ?? 0}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      if (item.originPrice != item.price)
                        Text(
                          '¥${FormatUtil.formatAmount(item.originPrice ?? 0)}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey,
                            decoration: TextDecoration.lineThrough,
                            decorationColor: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontFamily: '',
                          ),
                        ),
                      SizedBox(width: 5.w),
                      Text(
                        '¥${FormatUtil.formatAmount(item.price ?? 0)}',
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontFamily: '',
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 5.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      S.current.special_btn,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
