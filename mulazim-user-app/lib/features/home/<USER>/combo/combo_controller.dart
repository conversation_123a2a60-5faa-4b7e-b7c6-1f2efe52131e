import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/features/home/<USER>/combo/combo_state.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_controller.dart';

part 'combo_controller.g.dart';

@riverpod
class ComboController extends _$ComboController {
  @override
  ComboState build(final int buildingId) {
    Future.microtask(() => loadCombos());
    return const ComboState(isLoading: true);
  }

  Future<void> loadCombos({final bool isLoadMore = false}) async {
    state = state.copyWith(isLoading: !isLoadMore);

    final currentPage = isLoadMore ? state.page + 1 : 1;

    try {
      final result = await ref.read(restaurantRepositoryProvider).getComboList({
        'building_id': buildingId,
        'limit': state.limit,
        'page': currentPage,
      });
      if (result.success) {
        final data = result.data!;
        final newList = isLoadMore ? [...state.comboList, ...data] : data;
        state = state.copyWith(
          comboList: newList,
          hasMore: data.length == state.limit,
          page: currentPage,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: result.msg);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
    state = state.copyWith(isInitialLoading: false);
  }
}
