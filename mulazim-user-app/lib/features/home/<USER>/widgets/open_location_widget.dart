import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// OpenLocation 组件 - 处理定位错误和代理服务显示
class OpenLocationWidget extends ConsumerWidget {
  const OpenLocationWidget({
    super.key,
    this.topOffset = 95.0,
    this.isPermissionError = false,
  });

  /// 顶部偏移量
  final double topOffset;

  /// 是否是权限错误
  final bool isPermissionError;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(homeControllerProvider.notifier);
    final language = ref.read(languageProvider);

    // 根据错误类型显示不同的内容
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 定位失败图标
          Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.center,
            children: [
              SizedBox(
                height: 157.5.h,
                child: Image.asset(
                  'assets/images/home/<USER>',
                  fit: BoxFit.contain,
                ),
              ),
              Positioned(
                bottom: 10.h,
                child: _buildErrorText(),
              ),
            ],
          ),

          // 按钮区域
          _buildButtonArea(controller, context, language),
          if (!isPermissionError)
            // 代理申请按钮
            _buildAgentButton(controller),
        ],
      ),
    );
  }

  /// 构建错误文本
  Widget _buildErrorText() {
    return Text(
      isPermissionError
          ? S.current.location_error
          : S.current.your_area_not_open,
      style: TextStyle(
        color: AppColors.textSecondaryColor,
        fontSize: 16.sp,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建按钮区域
  Widget _buildButtonArea(
      final HomeController controller, final BuildContext context, final String language) {
    return Directionality(
      textDirection: isPermissionError
          ? TextDirection.ltr
          : language == 'ug'
              ? TextDirection.rtl
              : TextDirection.ltr,
      child: Container(
        margin: EdgeInsets.only(top: 20.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSelectAddressButton(controller),
            SizedBox(width: 10.w),
            _buildRetryButton(controller, context),
          ],
        ),
      ),
    );
  }

  /// 构建选择地址按钮
  Widget _buildSelectAddressButton(final HomeController controller) {
    return GestureDetector(
      onTap: () => controller.handleSelectAddress(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
        decoration: isPermissionError
            ? BoxDecoration(
                border: Border.all(color: AppColors.primary, width: 1.r),
                borderRadius: BorderRadius.circular(5.5.r),
              )
            : BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(5.r),
              ),
        child: Text(
          S.current.home_select_address,
          style: TextStyle(
            color: isPermissionError ? AppColors.primary : Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 构建重试按钮
  Widget _buildRetryButton(
      final HomeController controller, final BuildContext context) {
    return GestureDetector(
      onTap: () => isPermissionError
          ? controller.initHomePage(context, isRefresh: true)
          : controller.handleRetry(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
        decoration: isPermissionError
            ? BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(5.r),
              )
            : BoxDecoration(
                border: Border.all(color: AppColors.primary, width: 1.r),
                borderRadius: BorderRadius.circular(5.5.r),
              ),
        child: Text(
          isPermissionError ? S.current.go_open : S.current.retry,
          style: TextStyle(
            color: isPermissionError ? Colors.white : AppColors.primary,
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 构建代理申请按钮
  Widget _buildAgentButton(final HomeController controller) {
    return GestureDetector(
      onTap: () => controller.handleAsAgent(),
      child: Container(
        margin: EdgeInsets.only(top: 27.h),
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          color: const Color(0xFFffa707),
          borderRadius: BorderRadius.circular(28.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFffa707).withValues(alpha: 0.3),
              offset: Offset(5.r, 7.5.r),
              blurRadius: 16.r,
              spreadRadius: -8.r,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/images/home/<USER>',
              width: 35.w,
              height: 35.h,
            ),
            SizedBox(width: 17.5.w),
            Text(
              S.current.nov_be_proxy,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
