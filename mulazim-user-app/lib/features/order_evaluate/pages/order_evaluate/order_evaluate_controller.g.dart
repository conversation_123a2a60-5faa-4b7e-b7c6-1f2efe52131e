// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_evaluate_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderEvaluateControllerHash() =>
    r'327669f45474d5b1b2f609faef26a7ab89078d35';

/// See also [OrderEvaluateController].
@ProviderFor(OrderEvaluateController)
final orderEvaluateControllerProvider = AutoDisposeNotifierProvider<
    OrderEvaluateController, OrderEvaluateState>.internal(
  OrderEvaluateController.new,
  name: r'orderEvaluateControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderEvaluateControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderEvaluateController = AutoDisposeNotifier<OrderEvaluateState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
