import 'dart:io';
import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';
import 'package:user_app/data/repositories/mine/comment_repository.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_state.dart';

part 'order_evaluate_controller.g.dart';

@riverpod
class OrderEvaluateController extends _$OrderEvaluateController {
  @override
  OrderEvaluateState build() {
    return const OrderEvaluateState();
  }

  /// 获取订单评价数据
  Future<void> getOrderEvaluateData(final int orderId) async {
    state = state.copyWith(isLoading: true, error: "null");

    try {
      final repository = ref.read(commentRepositoryProvider);
      final evaluateData = await repository.getOrderEvaluateDetail(orderId);

      if (evaluateData != null) {
        // 设置配送员的orderId
        OrderEvaluateModel finalEvaluateData = evaluateData;
        if (evaluateData.shipper != null) {
          final updatedShipper =
              evaluateData.shipper!.copyWith(orderId: orderId);
          finalEvaluateData = evaluateData.copyWith(shipper: updatedShipper);
        }

        state = state.copyWith(
          isLoading: false,
          evaluateData: finalEvaluateData,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: '获取评价数据失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 更新配送员匿名状态
  void updateShipperAnonymous(final bool isAnonymous) {
    final currentData = state.evaluateData;
    if (currentData?.shipper != null) {
      final updatedShipper =
          currentData!.shipper!.copyWith(isAnonymous: isAnonymous);
      final updatedData = currentData.copyWith(shipper: updatedShipper);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 更新配送员满意度 - 按小程序逻辑
  void updateShipperSatisfaction(final int satisfaction) {
    final currentData = state.evaluateData;
    if (currentData?.shipper != null) {
      // 重置所有建议标签为非激活状态
      final resetBadTags = currentData!.shipper!.shipperBad
          .map((final tag) => tag.copyWith(active: false))
          .toList();
      final resetGoodTags = currentData.shipper!.shipperGood
          .map((final tag) => tag.copyWith(active: false))
          .toList();

      final updatedShipper = currentData.shipper!.copyWith(
        star: satisfaction == 1 ? 5 : 1, // 满意5星，不满意1星
        isSatisfied: satisfaction,
        shipperBad: resetBadTags,
        shipperGood: resetGoodTags,
        comment: '', // 重置评论
      );
      final updatedData = currentData.copyWith(shipper: updatedShipper);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 更新配送员建议标签 - 按小程序逻辑
  void updateShipperSuggestTag(final String tagName, final int type) {
    final currentData = state.evaluateData;
    if (currentData?.shipper != null) {
      List<SuggestTag> targetTags;
      List<SuggestTag> updatedTags;

      if (type == 1) {
        // 好评标签
        targetTags = currentData!.shipper!.shipperGood;
        updatedTags = targetTags.map((final tag) {
          if (tag.name == tagName) {
            return tag.copyWith(active: !tag.active);
          }
          return tag;
        }).toList();

        // 生成选中标签的评论文本，逗号分隔
        final selectedTags = updatedTags
            .where((final tag) => tag.active)
            .map((final tag) => tag.name)
            .join(',');

        final updatedShipper = currentData.shipper!.copyWith(
          shipperGood: updatedTags,
          comment: selectedTags,
        );
        final updatedData = currentData.copyWith(shipper: updatedShipper);
        state = state.copyWith(evaluateData: updatedData);
      } else {
        // 差评标签
        targetTags = currentData!.shipper!.shipperBad;
        updatedTags = targetTags.map((final tag) {
          if (tag.name == tagName) {
            return tag.copyWith(active: !tag.active);
          }
          return tag;
        }).toList();

        // 生成选中标签的评论文本，逗号分隔
        final selectedTags = updatedTags
            .where((final tag) => tag.active)
            .map((final tag) => tag.name)
            .join(',');

        final updatedShipper = currentData.shipper!.copyWith(
          shipperBad: updatedTags,
          comment: selectedTags,
        );
        final updatedData = currentData.copyWith(shipper: updatedShipper);
        state = state.copyWith(evaluateData: updatedData);
      }
    }
  }

  /// 更新美食匿名状态
  void updateFoodAnonymous(final bool isAnonymous) {
    final currentData = state.evaluateData;
    if (currentData != null) {
      final updatedOrderDetail =
          currentData.orderDetail.copyWith(isAnonymous: isAnonymous);
      final updatedData = currentData.copyWith(orderDetail: updatedOrderDetail);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 更新美食评论
  void updateFoodComment(final String comment) {
    final currentData = state.evaluateData;
    if (currentData != null) {
      final updatedOrderDetail =
          currentData.orderDetail.copyWith(comment: comment);
      final updatedData = currentData.copyWith(orderDetail: updatedOrderDetail);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 更新评分
  void updateRating(final String type, final int rating) {
    final currentData = state.evaluateData;
    if (currentData != null) {
      OrderDetailEvaluate updatedOrderDetail;
      switch (type) {
        case 'general':
          updatedOrderDetail = currentData.orderDetail.copyWith(star: rating);
          break;
        case 'taste':
          updatedOrderDetail =
              currentData.orderDetail.copyWith(foodStar: rating);
          break;
        case 'packing':
          updatedOrderDetail =
              currentData.orderDetail.copyWith(boxStar: rating);
          break;
        default:
          return;
      }

      // 同时更新所有美食的评分
      final updatedFoods = currentData.orderDetail.foods.map((final food) {
        switch (type) {
          case 'general':
            return food.copyWith(star: rating);
          case 'taste':
            return food.copyWith(foodStar: rating);
          case 'packing':
            return food.copyWith(boxStar: rating);
          default:
            return food;
        }
      }).toList();

      final finalOrderDetail = updatedOrderDetail.copyWith(foods: updatedFoods);
      final updatedData = currentData.copyWith(orderDetail: finalOrderDetail);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 选择美食添加到评论 - 按小程序逻辑
  void selectFood(final int index, final String foodName) {
    final currentData = state.evaluateData;
    if (currentData != null) {
      final currentComment = currentData.orderDetail.comment;
      // 按小程序格式添加: #美食名称#
      final newComment = '$currentComment #$foodName# ';

      // 更新评论
      final updatedOrderDetail =
          currentData.orderDetail.copyWith(comment: newComment);

      // 更新美食选中状态
      final updatedFoods =
          currentData.orderDetail.foods.asMap().entries.map((final entry) {
        if (entry.key == index) {
          return entry.value.copyWith(active: true);
        }
        return entry.value;
      }).toList();

      final finalOrderDetail = updatedOrderDetail.copyWith(foods: updatedFoods);
      final updatedData = currentData.copyWith(orderDetail: finalOrderDetail);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 上传图片
  Future<void> uploadImage(final File imageFile) async {
    final currentData = state.evaluateData;
    if (currentData != null && currentData.orderDetail.images.length < 3) {
      // 设置上传状态
      state = state.copyWith(isUploading: true, error: null);

      try {
        final repository = ref.read(commentRepositoryProvider);
        final imageUrl = await repository.uploadCommentImage(imageFile);

        if (imageUrl != null && imageUrl.isNotEmpty) {
          final updatedImages =
              List<String>.from(currentData.orderDetail.images)..add(imageUrl);
          final updatedOrderDetail =
              currentData.orderDetail.copyWith(images: updatedImages);
          final updatedData =
              currentData.copyWith(orderDetail: updatedOrderDetail);
          state = state.copyWith(
            evaluateData: updatedData,
            isUploading: false,
          );
        } else {
          state = state.copyWith(
            isUploading: false,
            error: '图片上传失败: 服务器返回空的图片地址',
          );
        }
      } catch (e) {
        state = state.copyWith(
          isUploading: false,
          error: '图片上传失败: ${e.toString()}',
        );
      }
    }
  }

  /// 删除图片
  Future<void> removeImage(final int index) async {
    final currentData = state.evaluateData;
    if (currentData != null && index < currentData.orderDetail.images.length) {
      try {
        final imageUrl = currentData.orderDetail.images[index];
        final repository = ref.read(commentRepositoryProvider);
        await repository.deleteCommentImage(imageUrl);

        final updatedImages = List<String>.from(currentData.orderDetail.images)
          ..removeAt(index);
        final updatedOrderDetail =
            currentData.orderDetail.copyWith(images: updatedImages);
        final updatedData =
            currentData.copyWith(orderDetail: updatedOrderDetail);
        state = state.copyWith(evaluateData: updatedData);
      } catch (e) {
        state = state.copyWith(error: '图片删除失败: ${e.toString()}');
      }
    }
  }

  /// 更新美食图片列表
  void updateFoodImages(final List<String> images) {
    final currentData = state.evaluateData;
    if (currentData != null) {
      final updatedOrderDetail =
          currentData.orderDetail.copyWith(images: images);
      final updatedData = currentData.copyWith(orderDetail: updatedOrderDetail);
      state = state.copyWith(evaluateData: updatedData);
    }
  }

  /// 提交评价 - 按小程序逻辑
  Future<void> submitEvaluate(final int orderId) async {
    final currentData = state.evaluateData;
    if (currentData == null) return;

    state = state.copyWith(isSubmitting: true, error: null);

    try {
      final repository = ref.read(commentRepositoryProvider);
      final params = _buildSubmitParams(currentData, orderId);

      final result = await repository.createComment(params);

      if (result.success) {
        // 按小程序逻辑：如果有配送员评价，显示弹窗；否则直接显示成功并退出
        if (currentData.shipper != null) {
          state = state.copyWith(
            isSubmitting: false,
            showSuccessPopup: true, // 显示成功弹窗
          );
        } else {
          // 没有配送员评价，显示toast提示并标记为成功
          BotToast.showText(
              text: result.msg.isNotEmpty ? result.msg : '评价提交成功');
          state = state.copyWith(
            isSubmitting: false,
            showSuccessPopup: false,
            isSuccess: true, // 标记为成功，页面可以据此处理退出逻辑
          );
        }
      } else {
        BotToast.showText(text: result.msg);
        state = state.copyWith(
          isSubmitting: false,
          error: '提交失败，请重试',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: '提交失败: $e',
      );
    }
  }

  /// 关闭成功弹窗
  void closeSuccessPopup() {
    state = state.copyWith(
      showSuccessPopup: false,
      isSuccess: true, // 关闭弹窗后标记为成功
    );
  }

  /// 构建提交参数 - 完全按照小程序逻辑
  Map<String, dynamic> _buildSubmitParams(
      final OrderEvaluateModel data, final int orderId) {
    final params = <String, dynamic>{};
    int commentIndex = 0;

    // 添加配送员评价
    if (data.shipper != null) {
      final shipper = data.shipper!;
      final prefix = 'comment[$commentIndex]';
      params['$prefix[order_id]'] = orderId;
      params['$prefix[type]'] = 1;
      params['$prefix[shipper_id]'] = shipper.id ?? 0;
      params['$prefix[star]'] = shipper.star;
      params['$prefix[text]'] = shipper.comment;
      params['$prefix[is_anonymous]'] = shipper.isAnonymous ? '1' : '0';
      params['$prefix[is_satisfied]'] = shipper.isSatisfied;
      commentIndex++;
    }

    // 添加美食评价 - 完全按小程序逻辑
    final order = data.orderDetail;
    final foods = order.foods;
    final hasActive = foods.any((final food) => food.active);

    for (int i = 0; i < foods.length; i++) {
      final food = foods[i];
      final isActive = food.active;
      final prefix = 'comment[$commentIndex]';

      params['$prefix[order_id]'] = orderId;
      params['$prefix[type]'] = 2;
      params['$prefix[order_detail_id]'] = food.id;
      params['$prefix[food_id]'] = food.foodId;

      // 星级评分逻辑：如果有选中的美食，只有选中的使用评分，否则默认5星
      params['$prefix[star]'] =
          hasActive ? (isActive ? order.star : 5) : order.star;
      params['$prefix[food_star]'] =
          hasActive ? (isActive ? order.foodStar : 5) : order.foodStar;
      params['$prefix[box_star]'] =
          hasActive ? (isActive ? order.boxStar : 5) : order.boxStar;
      params['$prefix[text]'] =
          hasActive ? (isActive ? order.comment : '') : order.comment;
      params['$prefix[is_anonymous]'] = order.isAnonymous ? '1' : '0';

      // 添加图片 - 按小程序逻辑
      if (hasActive && isActive) {
        // 有选中的美食且当前是选中的，使用图片
        for (int j = 0; j < order.images.length; j++) {
          params['$prefix[images][$j]'] = order.images[j];
        }
      } else if (!hasActive) {
        // 没有选中的美食，所有美食都使用图片
        for (int j = 0; j < order.images.length; j++) {
          params['$prefix[images][$j]'] = order.images[j];
        }
      } else {
        // 有选中但当前不是选中的，不使用图片
        params['$prefix[images][0]'] = '';
      }

      commentIndex++;
    }

    return params;
  }

  /// 清除错误信息
  void clearError() {
    state = state.clearError();
  }
}
