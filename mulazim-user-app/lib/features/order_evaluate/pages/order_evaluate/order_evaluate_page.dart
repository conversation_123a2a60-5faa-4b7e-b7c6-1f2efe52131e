import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/shipper_evaluate_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/food_evaluate_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/submit_button_widget.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/widgets/success_popup_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单评价页面
class OrderEvaluatePage extends ConsumerWidget {
  /// 订单ID
  final int orderId;

  /// 来源页面
  final String fromPage;

  /// 构造函数
  const OrderEvaluatePage({
    super.key,
    required this.orderId,
    this.fromPage = '',
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 在页面首次构建时获取数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      final state = ref.read(orderEvaluateControllerProvider);
      if (state.evaluateData == null && !state.isLoading) {
        ref
            .read(orderEvaluateControllerProvider.notifier)
            .getOrderEvaluateData(orderId);
      }
    });

    // 监听成功状态，用于页面退出
    ref.listen<bool>(
      orderEvaluateControllerProvider.select((final state) => state.isSuccess),
      (final previous, final current) {
        if (current) {
          // 延迟退出页面，与小程序逻辑一致
          Future.delayed(const Duration(milliseconds: 500), () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
          });
        }
      },
    );

    // 监听成功弹窗状态
    final showSuccessPopup = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.showSuccessPopup),
    );

    // 监听配送员数据
    final shipperData = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.evaluateData?.shipper),
    );

    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        title: Text(S.current.order_evaluate),
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          OrderEvaluateBody(orderId: orderId),
          // 成功弹窗
          SuccessPopupWidget(
            isVisible: showSuccessPopup,
            shipper: shipperData,
            onClose: () {
              ref
                  .read(orderEvaluateControllerProvider.notifier)
                  .closeSuccessPopup();
            },
          ),
        ],
      ),
      bottomNavigationBar: !showSuccessPopup ? SubmitButtonWidget(orderId: orderId) : null,
    );
  }
}

/// 订单评价主体内容
class OrderEvaluateBody extends ConsumerWidget {
  /// 订单ID
  final int orderId;

  /// 构造函数
  const OrderEvaluateBody({super.key, required this.orderId});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 精确监听加载状态
    final isLoading = ref.watch(
      orderEvaluateControllerProvider.select((final state) => state.isLoading),
    );

    // // 精确监听错误状态
    // final error = ref.watch(
    //   orderEvaluateControllerProvider.select((final state) => state.error),
    // );

    // 精确监听数据
    final evaluateData = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.evaluateData),
    );

    if (isLoading) {
      return const LoadingWidget();
    }

    if (evaluateData == null) {
      return EmptyView(
        message: evaluateData == null ? S.current.no_data : null,
        retryMessage: S.current.retry,
        onRetry: () {
          ref
              .read(orderEvaluateControllerProvider.notifier)
              .getOrderEvaluateData(orderId);
        },
      );
    }

    return const MainContent();
  }
}

/// 主要内容区域
class MainContent extends ConsumerWidget {
  const MainContent({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final lang = ref.watch(languageProvider);
    final isRtl = lang == 'ug';

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.baseGreenColor,
            AppColors.baseGreenColor.withValues(alpha: 0.9),
            const Color(0xFFEFF1F6),
          ],
          stops: const [0.0, 0.069, 0.5],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(10.w),
        child: Directionality(
          textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
          child: Column(
            children: [
              // 配送员评价组件（条件显示）
              const ShipperEvaluateWidget(),

              // 间距（仅在有配送员数据时显示）
              const ConditionalSpacing(),

              // 美食评价组件
              const FoodEvaluateWidget(),

              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }
}

/// 条件间距组件
class ConditionalSpacing extends ConsumerWidget {
  const ConditionalSpacing({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 精确监听配送员数据是否存在
    final hasShipper = ref.watch(
      orderEvaluateControllerProvider.select(
        (final state) => state.evaluateData?.shipper != null,
      ),
    );

    return hasShipper ? SizedBox(height: 10.h) : const SizedBox.shrink();
  }
}
