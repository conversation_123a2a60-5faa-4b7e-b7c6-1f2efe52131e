import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 提交按钮组件
class SubmitButtonWidget extends ConsumerWidget {
  final int orderId;

  const SubmitButtonWidget({
    super.key,
    required this.orderId,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 精确监听提交状态
    final isSubmitting = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.isSubmitting),
    );

    // 精确监听是否有数据
    final hasData = ref.watch(
      orderEvaluateControllerProvider
          .select((final state) => state.evaluateData != null),
    );

    if (!hasData) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(10.w),
      margin: EdgeInsets.only(bottom: 20.h),
      child: ElevatedButton(
        onPressed: isSubmitting
            ? null
            : () => ref
                .read(orderEvaluateControllerProvider.notifier)
                .submitEvaluate(orderId),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.baseGreenColor,
          minimumSize: Size(double.infinity, 40.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(40.w),
          ),
        ),
        child: isSubmitting
            ? SizedBox(
                width: 20.w,
                height: 20.w,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                S.current.evaluate_submit,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
