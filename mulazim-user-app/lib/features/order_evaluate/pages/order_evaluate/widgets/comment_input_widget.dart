import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/dash_painter.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论输入框组件（包含图片选择功能）
class CommentInputWidget extends ConsumerStatefulWidget {
  final String comment;
  final List<String> images;
  final Function(String) onChanged;
  final Function(List<String>) onImagesChanged;

  const CommentInputWidget({
    final Key? key,
    required this.comment,
    this.images = const [],
    required this.onChanged,
    required this.onImagesChanged,
  }) : super(key: key);

  @override
  ConsumerState<CommentInputWidget> createState() => _CommentInputWidgetState();
}

class _CommentInputWidgetState extends ConsumerState<CommentInputWidget> {
  late TextEditingController _controller;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.comment);
  }

  @override
  void didUpdateWidget(final CommentInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.comment != widget.comment) {
      _controller.text = widget.comment;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 选择图片并上传
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 600,
        imageQuality: 80,
      );

      if (image != null) {
        // 立即上传图片到服务器
        final controller = ref.read(orderEvaluateControllerProvider.notifier);
        await controller.uploadImage(File(image.path));
      }
    } catch (e) {
      // 处理选择图片错误
      debugPrint('选择图片失败: $e');
    }
  }

  /// 删除图片
  void _removeImage(final int index) {
    // 调用控制器的删除方法
    final controller = ref.read(orderEvaluateControllerProvider.notifier);
    controller.removeImage(index);
  }

  @override
  Widget build(final BuildContext context) {
    final lang = ref.watch(languageProvider);
    final isRtl = lang == 'ug';
    final isUploading = ref.watch(
      orderEvaluateControllerProvider.select((state) => state.isUploading),
    );

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论输入框
          TextField(
            controller: _controller,
            textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
            textAlign: isRtl ? TextAlign.right : TextAlign.left,
            maxLines: 4,
            maxLength: 250, // 对应小程序的maxlength="250"
            style: TextStyle(
              color: Colors.black,
              fontSize: 16.sp, // 30rpx / 2 = 15sp
              fontFamily: AppConstants.mainFont, // 对应小程序的字体
            ),
            decoration: InputDecoration(
              hintText: S.current.evaluate_shipper_placeholder,
              hintStyle: TextStyle(
                color: AppColors.textSecondColor,
                fontSize: 16.sp, // 30rpx / 2 = 15sp
                fontFamily: AppConstants.mainFont, // 对应小程序的字体
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.w),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.w),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.w),
                borderSide: BorderSide(color: Colors.grey.shade200),
              ),
              filled: true,
              fillColor: Colors.grey.shade200,
              contentPadding: EdgeInsets.all(0),
              counterText: '',
            ),
            onChanged: widget.onChanged,
          ),

          SizedBox(height: 12.w),

          // 图片选择区域
          _buildImageSection(!isRtl, isUploading),
        ],
      ),
    );
  }

  /// 构建图片选择区域
  Widget _buildImageSection(final bool isRtl, final bool isUploading) {
    return SizedBox(
      width: double.infinity,
      child: Wrap(
        alignment: isRtl ? WrapAlignment.end : WrapAlignment.start,
        spacing: 10.w,
        runSpacing: 10.w,
        children: [
          // 已选择的图片列表
          ...widget.images.asMap().entries.map((final entry) {
            final index = entry.key;
            final imagePath = entry.value;
            return _buildImageItem(imagePath, index);
          }).toList(),
          // 添加图片按钮（最多3张）
          if (widget.images.length < 3) _buildAddImageButton(isUploading),
        ],
      ),
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton(final bool isUploading) {
    return GestureDetector(
      onTap: isUploading ? null : _pickImage,
      child: SizedBox(
        width: 80.w,
        height: 80.w,
        child: DashedContainer(
          width: 80.w,
          height: 80.w,
          dashColor: isUploading ? Colors.grey : const Color(0xFF8C8C8C),
          strokeWidth: 1.w,
          dashPattern: [1, 1],
          borderRadius: 8.r,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (isUploading)
                SizedBox(
                  width: 20.sp,
                  height: 20.sp,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.w,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                  ),
                )
              else
                Icon(
                  Icons.camera_alt_outlined,
                  size: 30.sp,
                  color: const Color(0xFF8C8C8C),
                ),
              SizedBox(height: 2.w),
              Text(
                isUploading ? S.current.uploading : S.current.choiseImage,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: isUploading ? Colors.grey : const Color(0xFF8C8C8C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建单个图片项
  Widget _buildImageItem(final String imagePath, final int index) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => showImageViewer(context, imageUrls: [imagePath]),
          child: Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.w),
              border: Border.all(
                color: const Color(0xFFE8EBF0),
                width: 1.w,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.w),
              child: _buildImageWidget(imagePath),
            ),
          ),
        ),
        // 删除按钮
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              width: 20.w,
              height: 20.w,
              decoration: const BoxDecoration(
                color: Colors.black,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 12.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建图片显示组件（支持本地文件和网络图片）
  Widget _buildImageWidget(final String imagePath) {
    if (imagePath.startsWith('http')) {
      // 网络图片
      return Image.network(
        imagePath,
        fit: BoxFit.cover,
        errorBuilder: (final context, final error, final stackTrace) {
          return Container(
            color: const Color(0xFFF7F9FA),
            child: Icon(
              Icons.broken_image,
              size: 20.sp,
              color: const Color(0xFF8C8C8C),
            ),
          );
        },
      );
    } else {
      // 本地文件
      return Image.file(
        File(imagePath),
        fit: BoxFit.cover,
        errorBuilder: (final context, final error, final stackTrace) {
          return Container(
            color: const Color(0xFFF7F9FA),
            child: Icon(
              Icons.broken_image,
              size: 20.sp,
              color: const Color(0xFF8C8C8C),
            ),
          );
        },
      );
    }
  }
}
