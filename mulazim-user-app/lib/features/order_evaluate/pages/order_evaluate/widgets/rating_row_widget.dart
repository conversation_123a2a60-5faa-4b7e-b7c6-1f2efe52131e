import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';

/// 评分行组件
class RatingRowWidget extends ConsumerWidget {
  final String label;
  final int rating;
  final String type;

  /// 星星大小
  final double size;

  /// 星星间隔 (对应微信小程序 8rpx)
  final double gap;

  const RatingRowWidget({
    final Key? key,
    required this.label,
    required this.rating,
    required this.type,
    required this.size,
    required this.gap,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isRtl = Localizations.localeOf(context).languageCode == 'en';

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: isRtl ? MainAxisSize.max : MainAxisSize.min,
      children: [
        Expanded(
          flex: isRtl ? 3 : 1,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 7,
          child: StarRating(
            rating: rating.toDouble(),
            size: size,
            isClickable: true,
            hideTip: false,
            gap: gap,
            onRatingChanged: (final newRating) {
              ref
                  .read(orderEvaluateControllerProvider.notifier)
                  .updateRating(type, newRating.toInt());
            },
          ),
        ),
      ],
    );
  }
}
