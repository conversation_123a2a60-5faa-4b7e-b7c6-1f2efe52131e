import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 满意度选择卡片组件
class SatisfactionCardWidget extends ConsumerWidget {
  const SatisfactionCardWidget({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 精确监听满意度状态
    final satisfaction = ref.watch(
      orderEvaluateControllerProvider.select(
        (final state) => state.evaluateData?.shipper?.isSatisfied ?? 1,
      ),
    );

    return Row(
      children: [
        Expanded(
          child: _buildSatisfactionCard(
            value: 1,
            label: S.current.evaluate_satis,
            satisfaction: satisfaction,
            onTap: () {
              ref
                  .read(orderEvaluateControllerProvider.notifier)
                  .updateShipperSatisfaction(1);
            },
          ),
        ),
        SizedBox(width: 12.5.w),
        Expanded(
          child: _buildSatisfactionCard(
            value: 0,
            label: S.current.evaluate_no_satis,
            satisfaction: satisfaction,
            onTap: () {
              ref
                  .read(orderEvaluateControllerProvider.notifier)
                  .updateShipperSatisfaction(0);
            },
          ),
        ),
      ],
    );
  }

  /// 构建单个满意度卡片
  Widget _buildSatisfactionCard({
    required final int value,
    required final String label,
    required final int satisfaction,
    required final VoidCallback onTap,
  }) {
    final isSelected = satisfaction == value;

    // 根据满意度值选择图标
    String? iconPath;
    if (value == 1) {
      // 满意 - 笑脸
      iconPath = isSelected
          ? 'assets/images/evaluate/laugh_select.png'
          : 'assets/images/evaluate/laugh.png';
    } else {
      // 不满意 - 哭脸
      iconPath = isSelected
          ? 'assets/images/evaluate/cry_select.png'
          : 'assets/images/evaluate/cry.png';
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 40.h,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFFAF0) : const Color(0xFFF7F9FA),
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            color:
                isSelected ? const Color(0xFFFA8C16) : const Color(0xFFE8EBF0),
            width: 1.5.w,
          ),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 图标
              Image.asset(
                iconPath,
                width: isSelected ? 40.w : 30.w,
                height: isSelected ? 40.h : 30.h,
                fit: BoxFit.contain,
              ),
              SizedBox(width: 8.w),
              // 文字
              Text(
                label,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: isSelected
                      ? const Color(0xFFFA8C16)
                      : const Color(0xFF8C8C8C),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
