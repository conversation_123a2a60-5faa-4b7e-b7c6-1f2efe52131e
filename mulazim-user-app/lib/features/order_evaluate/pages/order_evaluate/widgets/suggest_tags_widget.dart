import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/order_evaluate/order_evaluate_model.dart';
import 'package:user_app/features/order_evaluate/pages/order_evaluate/order_evaluate_controller.dart';

/// 建议标签组件
class SuggestTagsWidget extends ConsumerWidget {
  const SuggestTagsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 精确监听配送员数据
    final shipperData = ref.watch(
      orderEvaluateControllerProvider.select(
        (state) => state.evaluateData?.shipper,
      ),
    );

    if (shipperData == null) {
      return const SizedBox.shrink();
    }

    // 根据满意度选择对应的标签
    final tags = shipperData.isSatisfied == 1
        ? shipperData.shipperGood
        : shipperData.shipperBad;

    return Wrap(
      spacing: 5.w,
      runSpacing: 5.w,
      children: tags.map((tag) {
        return _buildSuggestTag(
          tag: tag,
          shipperData: shipperData,
          ref: ref,
        );
      }).toList(),
    );
  }

  /// 构建单个建议标签
  Widget _buildSuggestTag({
    required SuggestTag tag,
    required ShipperEvaluate shipperData,
    required WidgetRef ref,
  }) {
    return GestureDetector(
      onTap: () {
        ref
            .read(orderEvaluateControllerProvider.notifier)
            .updateShipperSuggestTag(tag.name, shipperData.isSatisfied);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 5.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.w),
          color: tag.active ? const Color(0xFFFFF8E3) : const Color(0xFFF8F9FB),
        ),
        child: Text(
          tag.name,
          style: TextStyle(
            fontSize: 15.sp,
            color:
                tag.active ? const Color(0xFFFFA30E) : const Color(0xFFA0ABC1),
          ),
        ),
      ),
    );
  }
}
