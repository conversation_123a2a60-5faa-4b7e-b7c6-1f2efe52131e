// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_address_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addressListHash() => r'dd7ab86db92ca906ad7d1d1b33e882c8e53ffd70';

/// See also [AddressList].
@ProviderFor(AddressList)
final addressListProvider = AutoDisposeAsyncNotifierProvider<AddressList,
    List<HistoryAddressData>?>.internal(
  AddressList.new,
  name: r'addressListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$addressListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AddressList = AutoDisposeAsyncNotifier<List<HistoryAddressData>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
