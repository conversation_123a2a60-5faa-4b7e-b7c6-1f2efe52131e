// 当前选中的地址
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';
import 'package:user_app/features/address/providers/address_provider.dart';
part 'my_address_provider.g.dart';

@riverpod
class AddressList extends _$AddressList {
  @override
  FutureOr<List<HistoryAddressData>?> build() async{
    // 在build方法中直接获取数据，这样页面加载时就会自动获取
    return await getAddressList();
  }

  //获取地址数据
  Future<List<HistoryAddressData>?> getAddressList() async {
    try {
      // 获取我的订单数据
      final addressRepository = AddressRepository(apiClient: ref.read(apiClientProvider));
      final historyAddress = await addressRepository.getHistoryAddress();
      return historyAddress.data;
    } catch (e, stack) {
      // 记录错误
      print("获取订单数据错误: $e");
      print("堆栈: $stack");
      // 重新抛出错误，让Riverpod处理
      throw e;
    }
  }

  //删除地址
  Future<void> updateAddress(Map<String, dynamic> param) async {
    try {
      // 获取我的订单数据
      final addressRepository = AddressRepository(apiClient: ref.read(apiClientProvider));
      await addressRepository.updateAddress(param);
      ref.invalidate(addressProvider);
    } catch (e, stack) {
      // 记录错误
      print("删除地址错误: $e");
      print("堆栈: $stack");
      // 重新抛出错误，让Riverpod处理
      throw e;
    }
  }

  //删除地址
  Future<void> deleteAddress(Map<String, dynamic> param) async {
    try {
      // 获取我的订单数据
      final addressRepository = AddressRepository(apiClient: ref.read(apiClientProvider));
      await addressRepository.deleteAddress(param);
    } catch (e, stack) {
      // 记录错误
      print("删除地址错误: $e");
      print("堆栈: $stack");
      // 重新抛出错误，让Riverpod处理
      throw e;
    }
  }

}

