import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/address/location_list_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';

///获取地址列表按输入数据
Future<LocationListData?> getListByInput(Ref ref,
    {required String keyWord}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'key': keyWord,
    'area_id': 1,
  };
  // 获取首页数据
  final addressRepository =
      AddressRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await addressRepository.getListByInput(param);
  return addressInfo?.data;
}

///地址列表按输入数据提供者类
class AddressSearchNotifier
    extends StateNotifier<AsyncValue<LocationListData?>> {
  AddressSearchNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchAddressData({required String keyWord}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getListByInput(ref, keyWord: keyWord);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

///提供者实列
final addressSearchProvider = StateNotifierProvider.autoDispose<
    AddressSearchNotifier, AsyncValue<LocationListData?>>(
  (ref) => AddressSearchNotifier(ref),
);
