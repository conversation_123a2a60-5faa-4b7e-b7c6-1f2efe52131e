import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/formatter.dart';
import 'package:user_app/data/models/address/street_list_model.dart';
import 'package:user_app/data/repositories/address/address_repository.dart';

import 'address_street_provider.dart';

///获取对应小区提供者
class AddressParkNotifier
    extends StateNotifier<AsyncValue<List<StreetListData>?>> {
  AddressParkNotifier(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchAddressData({required int streetId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final addressRepository =
          AddressRepository(apiClient: ref.read(apiClientProvider));
      final parkList = await addressRepository.getParkList(streetId);
      state = AsyncValue.data(parkList?.data);
      if ((parkList?.data ?? []).isNotEmpty) {
        int langNum = ref.read(languageProvider) == 'zh' ? 2 : 1;
        var formatter = Formatter(langNum);
        ref.read(newStreetProvider.notifier).state =
            formatter.formatList(parkList?.data ?? []);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

///提供者实列
final addressParkProvider = StateNotifierProvider<AddressParkNotifier,
    AsyncValue<List<StreetListData>?>>(
  (ref) => AddressParkNotifier(ref),
);
