import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/services/mine_service.dart';
import 'package:user_app/generated/l10n.dart';


class ConnectPage extends ConsumerStatefulWidget {
  ConnectPage({super.key, required this.name});
  String? name;

  @override
  ConsumerState createState() => _ConnectPageState();
}

class _ConnectPageState extends ConsumerState<ConnectPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.current.app_name),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              SizedBox(height: 160.w,),

              Container(
                  padding: EdgeInsets.symmetric(horizontal: 26.w),
                  alignment: Alignment.center,
                  child: Text('${widget.name}${S.current.connect_title}',style: TextStyle(color: AppColors.baseGreenColor,fontSize: 18.sp,fontWeight: FontWeight.bold),textAlign: TextAlign.center,)
              ),
              SizedBox(height: 30.w,),
              Text('400-1111-990',style: TextStyle(color: AppColors.baseGreenColor,fontSize: 32.sp,fontWeight: FontWeight.bold),textAlign: TextAlign.center,),
            ],
          ),
          SizedBox(height: 10.w,),
          SizedBox(height: 10.w,),
          SizedBox(height: 10.w,),
          InkWell(
            onTap: () async {
              await ref.read(mineServiceProvider.notifier).makePhoneCall('400-1111-990');
            },
            child: ClipOval(
              child: Container(
                color: AppColors.baseGreenColor,
                width: 56.w,
                height: 56.w,
                child: Icon(Icons.phone,color: Colors.white,size: 22.sp,),
              ),
            ),
          ),
          SizedBox(height: 10.w,),

        ],
      ),
    );
  }
}