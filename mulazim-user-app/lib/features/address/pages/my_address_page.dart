import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/order/history_address_model.dart';
import 'package:user_app/features/address/dialog/animate_confirm_dialog.dart';
import 'package:user_app/features/address/pages/add_address_page.dart';
import 'package:user_app/features/address/providers/my_address_provider.dart';
import 'package:user_app/generated/l10n.dart';

class MyAddressPage extends ConsumerStatefulWidget {
  const MyAddressPage({super.key});

  @override
  ConsumerState createState() => _MyAddressPageState();
}

class _MyAddressPageState extends ConsumerState<MyAddressPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          S.current.my_address,
          style: TextStyle(fontSize: soBigSize),
        ),
      ),
      body: ref.watch(addressListProvider).when(
        data: (final data) {
          return SingleChildScrollView(
            child: Column(
              children: List.generate(
                (data ?? []).length,
                (final index) => _addressItem(data![index]),
              ),
            ),
          );
        },
        error: (final error, final stackTrace) {
          // 数据加载失败，显示错误信息
          return Center(child: Text('address Page Error: $error'));
        },
        loading: () {
          // 正在加载，显示加载指示器
          return Center(
            child: LoadingWidget(),
          );
        },
      ),
      bottomNavigationBar: InkWell(
        onTap: () {
          final StorageService _storageService = StorageService();
          int buildingId = int.tryParse((_storageService.read('buildingId') ?? '0')) ?? 0;
          int areaId = int.tryParse((_storageService.read('areaId') ?? '0')) ?? 0;
          String areaName = '${_storageService.read('areaName')}';
          String buildingName = '${_storageService.read('buildingName')}';
          String buildingNameZh = '${_storageService.read('buildingNameZh')}';
          Navigator.of(context)
              .push(
                MaterialPageRoute(
                  builder: (final context) => AddAddressPage(
                    buildingName: buildingName,
                    buildingNameZh: buildingNameZh,
                    buildingId: buildingId,
                    address: '',
                    name: '',
                    tel: '',
                    addressId: 0,
                    areaName: areaName,
                  ),
                ),
              )
              .then((final value) => ref.invalidate(addressListProvider));
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade100, width: 1.0),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5), // 设置阴影颜色和透明度
                spreadRadius: 2, // 阴影扩散半径
                blurRadius: 5, // 阴影模糊半径
                offset: Offset(0, 4), // 阴影偏移量，(x, y)
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              Image.asset(
                "assets/images/add.png",
                width: 30.w,
              ),
              SizedBox(
                width: 10.w,
              ),
              Text(
                S.current.add_address1,
                style: TextStyle(color: Colors.green, fontSize: titleSize),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _addressItem(final HistoryAddressData historyAddressData) {
    return InkWell(
      onTap: () {
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (final context) => AddAddressPage(
              buildingName: historyAddressData.buildingName ?? '',
              buildingNameZh: historyAddressData.buildingNameZh ??
                  historyAddressData.buildingName ??
                  '',
              buildingId: historyAddressData.buildingId ?? 0,
              address: historyAddressData.address ?? '',
              name: historyAddressData.name ?? '',
              tel: historyAddressData.tel ?? '',
              addressId: historyAddressData.id ?? 0,
              areaName: historyAddressData.areaName ?? '',
            ),
          ),
        )
            .then((final value) {
          ref.invalidate(addressListProvider);
        });
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
        ),
        margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
        child: Row(
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.all(10.w),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          historyAddressData.name ?? '',
                          style: TextStyle(
                            fontSize: mainSize,
                            color: AppColors.baseGreenColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(
                          width: 20.w,
                        ),
                        Text(
                          historyAddressData.tel ?? '',
                          style: TextStyle(fontSize: mainSize),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 3.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // Icon(Icons.add),
                        SizedBox(
                          width: MediaQuery.of(context).size.width - 145.w,
                          child: Text(
                            historyAddressData.address ?? '',
                            style: TextStyle(fontSize: mainSize),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 3.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: MediaQuery.of(context).size.width - 145.w,
                          child: Text(
                            historyAddressData.buildingName ?? '',
                            style: TextStyle(fontSize: mainSize),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            Icon(
              IconFont.bianji,
              color: Colors.green,
              size: 20.w,
            ),
            SizedBox(
              width: 28.w,
            ),
            InkWell(
              onTap: () {
                AnimateConfirmDialog(context, MediaQuery.of(context).size.width,
                    S.current.delete_address_tips, () {
                  Navigator.pop(context);
                  deleteAddress(historyAddressData.id ?? 0);
                });
              },
              child: Icon(
                IconFont.qingkong,
                color: Colors.green,
                size: 20.w,
              ),
            ),
            SizedBox(
              width: 15.w,
            ),
          ],
        ),
      ),
    );
  }

  //删除地址
  void deleteAddress(final int id) {
    Map<String, dynamic> param = {'address_ids[0]': id};
    ref.read(addressListProvider.notifier).deleteAddress(param);
    ref.invalidate(addressListProvider);
  }
}
