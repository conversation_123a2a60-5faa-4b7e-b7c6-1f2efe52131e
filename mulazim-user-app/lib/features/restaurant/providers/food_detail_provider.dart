import 'package:bot_toast/bot_toast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/address/post_response_model.dart';
import 'package:user_app/data/models/restaurant/food_detail_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/restaurant/restaurant_comment_list_model.dart';
import 'package:user_app/data/repositories/restaurant/restaurant_repository.dart';
///获取地址列表按定位数据
Future<FoodDetailData?> getFoodDetailData(Ref ref,{required int foodId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'food_id': foodId
  };
  // 秒杀活动数据
  final restaurantRepository = RestaurantRepository(apiClient: ref.read(apiClientProvider));
  final response = await restaurantRepository.getFoodDetail(param);
  return response.data;
}

///地址列表按定位数据提供者类
class FoodDetailProvider extends StateNotifier<AsyncValue<FoodDetailData?>> {
  FoodDetailProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchFoodDetailData({required int foodId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getFoodDetailData(ref, foodId: foodId);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final foodDetailProvider = StateNotifierProvider<
    FoodDetailProvider, AsyncValue<FoodDetailData?>>(
  (ref) => FoodDetailProvider(ref),
);




Future<RestaurantCommentListData?> getRestaurantCommentList(Ref ref,{required int page,required int id,required int type,required int foodId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'page': page,
    'limit': 10,
    'id': id,
    'type': type,
    'food_id': foodId,
  };
  // 秒杀活动数据
  final restaurantRepository = RestaurantRepository(apiClient: ref.read(apiClientProvider));
  final response = await restaurantRepository.getRestaurantCommentList(param);
  return response.data;
}

///地址列表按定位数据提供者类
class RestaurantCommentListProvider extends StateNotifier<AsyncValue<RestaurantCommentListData?>> {
  RestaurantCommentListProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchRestaurantCommentList({required int page,required int id,required int type,required int foodId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getRestaurantCommentList(ref,page: page,id: id,type: type,foodId: foodId);
      state = AsyncValue.data(response);

      if ((response?.items ?? []).length < 10) {
        ref.watch(canCommentDataProvider.notifier).state = false;
      } else {
        ref.watch(canCommentDataProvider.notifier).state = true;
      }

      if (page > 1) {
        ref
            .read(commentItemsProvider.notifier)
            .state
            .addAll(state?.value?.items ?? []);
      } else {
        ref.watch(commentItemsProvider.notifier).state =
            state?.value?.items ?? [];
      }


    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final restaurantCommentListProvider = StateNotifierProvider<
    RestaurantCommentListProvider, AsyncValue<RestaurantCommentListData?>>(
      (ref) => RestaurantCommentListProvider(ref),
);

final canCommentDataProvider = StateProvider.autoDispose<bool>((ref) => true);

final commentItemsProvider = StateProvider.autoDispose<List<Items>>((ref) => []);


///收藏
Future<ApiResult?> postAddFoodCollect(Ref ref,{required int foodId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'food_id': foodId
  };
  // 秒杀活动数据
  final restaurantRepository = RestaurantRepository(apiClient: ref.read(apiClientProvider));
  final response = await restaurantRepository.addFoodCollect(param);
  return response;
}

///取消收藏
Future<ApiResult?> postRemoveFoodCollect(Ref ref,{required int foodId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'foods_id[0]': foodId
  };
  // 秒杀活动数据
  final restaurantRepository = RestaurantRepository(apiClient: ref.read(apiClientProvider));
  final response = await restaurantRepository.removeFoodCollect(param);
  return response;
}

///地址列表按定位数据提供者类
class FoodCollectProvider extends StateNotifier<AsyncValue<PostResponseModel?>> {
  FoodCollectProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<bool> foodAddCollect({required int foodId}) async {
    try {
      final response = await postAddFoodCollect(ref, foodId: foodId);
      if(response?.msg != null)BotToast.showText(text: response!.msg!);
      if(response?.status == 200){
        return true;
      }else{
        return false;
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return false;
    }
  }

  Future<bool> foodRemoveCollect({required int foodId}) async {
    try {
      final response = await postRemoveFoodCollect(ref, foodId: foodId);
      if(response?.msg != null)BotToast.showText(text: response!.msg!);
      if(response?.status == 200){
        return true;
      }else{
        return false;
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      return false;
    }
  }

}

final foodCollectProvider = StateNotifierProvider<
    FoodCollectProvider, AsyncValue<PostResponseModel?>>(
      (ref) => FoodCollectProvider(ref),
);
