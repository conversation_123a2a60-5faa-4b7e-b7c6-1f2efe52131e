import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_state.dart';

/// 营业执照服务
class LicenseService {
  final ApiClient _apiClient;

  LicenseService(this._apiClient);

  /// 获取验证码和营业执照数据
  /// [restaurantId] 餐厅ID
  Future<ApiResult<LicenseCaptchaResponse>> getLicenseCaptcha(
    final int restaurantId,
  ) async {
    final result = await _apiClient.get(
      '/smart/v2/restaurant/license-captcha',
      params: {'restaurant_id': restaurantId},
      fromJson: (final response, final data) {
        return LicenseCaptchaResponse.fromJson(data);
      },
    );

    return result;
  }

  /// 验证验证码
  /// [restaurantId] 餐厅ID
  /// [captcha] 验证码
  Future<ApiResult<LicenseCaptchaResponse>> verifyCaptcha(
    final int restaurantId,
    final String captcha,
  ) async {
    final result = await _apiClient.post(
      '/smart/v2/restaurant/license-captcha-check',
      data: {
        'restaurant_id': restaurantId,
        'captcha': captcha,
      },
      fromJson: (final response, final data) {
        return LicenseCaptchaResponse.fromJson(data);
      },
    );

    return result;
  }
}
