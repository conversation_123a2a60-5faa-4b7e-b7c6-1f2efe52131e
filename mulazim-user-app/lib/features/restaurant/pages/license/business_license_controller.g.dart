// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_license_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$licenseControllerHash() => r'1f15a847045131d6fdaaeeb4d83c0b90047ca72e';

/// 营业执照页面控制器
///
/// Copied from [LicenseController].
@ProviderFor(LicenseController)
final licenseControllerProvider =
    AutoDisposeNotifierProvider<LicenseController, LicenseState>.internal(
  LicenseController.new,
  name: r'licenseControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$licenseControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LicenseController = AutoDisposeNotifier<LicenseState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
