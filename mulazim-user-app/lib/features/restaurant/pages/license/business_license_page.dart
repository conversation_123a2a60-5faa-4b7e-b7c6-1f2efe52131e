import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/app_bar.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_controller.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/license_verification_widget.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/license_display_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 营业执照页面
class BusinessLicensePage extends ConsumerStatefulWidget {
  /// 餐厅ID
  final int restaurantId;

  const BusinessLicensePage({
    super.key,
    required this.restaurantId,
  });

  @override
  ConsumerState<BusinessLicensePage> createState() =>
      _BusinessLicensePageState();
}

class _BusinessLicensePageState extends ConsumerState<BusinessLicensePage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      ref
          .read(licenseControllerProvider.notifier)
          .initPage(widget.restaurantId);
    });
  }

  @override
  Widget build(final BuildContext context) {
    final isVerified = ref.watch(licenseControllerProvider.select((final state) => state.isVerified));

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: isVerified
            ? S.current.business_license
            : S.current.license_verification,
      ),
      body: isVerified
              ? const LicenseDisplayWidget()
              : const LicenseVerificationWidget(),
    );
  }
}
