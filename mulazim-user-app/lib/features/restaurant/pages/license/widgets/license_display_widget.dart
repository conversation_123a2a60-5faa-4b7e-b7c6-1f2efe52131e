import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/features/restaurant/pages/license/business_license_controller.dart';
import 'package:user_app/features/restaurant/pages/license/widgets/license_item_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 营业执照展示组件
class LicenseDisplayWidget extends ConsumerWidget {
  const LicenseDisplayWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final licenseData = ref.watch(licenseControllerProvider.select((final state) => state.licenseData));
    final allUrls = ref.watch(licenseControllerProvider.select((final state) => state.allUrls));

    return Padding(
      padding: EdgeInsets.all(10.w),
      child: licenseData.isNotEmpty
          ? ListView.builder(
              itemCount: licenseData.length,
              itemBuilder: (final context, final index) {
                final item = licenseData[index];
                return LicenseItemWidget(
                  licenseItem: item,
                  onTap: () =>
                      _previewImage(context, item.bigImage, allUrls),
                );
              },
            )
          : Center(
              child: Text(
                S.current.no_business_license,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey,
                ),
              ),
            ),
    );
  }

  /// 预览图片
  void _previewImage(
      final BuildContext context, final String currentUrl, final List<String> allUrls) {
    showImageViewer(
      context,
      imageUrls: allUrls,
      initialIndex: allUrls.indexOf(currentUrl),
    );
  }
}
