import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 验证码输入框组件
class CaptchaInputWidget extends StatefulWidget {
  final String inputCode;
  final ValueChanged<String> onChanged;

  const CaptchaInputWidget({
    super.key,
    required this.inputCode,
    required this.onChanged,
  });

  @override
  State<CaptchaInputWidget> createState() => _CaptchaInputWidgetState();
}

class _CaptchaInputWidgetState extends State<CaptchaInputWidget> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.inputCode);
  }

  @override
  void didUpdateWidget(final CaptchaInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.inputCode != oldWidget.inputCode) {
      _textController.text = widget.inputCode;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Container(
      height: 50.h,
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFDDDDDD), width: 1.w),
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: TextField(
        controller: _textController,
        maxLength: 10,
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
        style: TextStyle(fontSize: 18.sp),
        decoration: InputDecoration(
          border: InputBorder.none,
          counterText: '',
          hintText: S.current.captcha_placeholder,
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 14.sp,
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 15.w),
        ),
        onChanged: widget.onChanged,
      ),
    );
  }
}
