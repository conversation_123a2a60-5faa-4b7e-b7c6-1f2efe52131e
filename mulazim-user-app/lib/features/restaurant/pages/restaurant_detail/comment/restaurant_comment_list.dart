import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/widgets/comment_list_section.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';

/// 餐厅评论列表组件
/// 显示餐厅的评分和用户评论列表
class RestaurantCommentList extends ConsumerStatefulWidget {
  /// 食品ID（可选）
  final int? foodId;

  /// 是否显示星级
  final bool showStar;

  /// 构造函数
  const RestaurantCommentList({
    super.key,
    this.foodId,
    this.showStar = true,
  });

  @override
  ConsumerState<RestaurantCommentList> createState() =>
      _RestaurantCommentListState();
}

class _RestaurantCommentListState extends ConsumerState<RestaurantCommentList> {


  @override
  void initState() {
    super.initState();

    // 页面绘制完成后执行
    SchedulerBinding.instance.addPostFrameCallback((final _) {
      // 从riverpod获取餐厅ID
      final restaurantId = ref.read(
        restaurantDetailControllerProvider
            .select((state) => state.restaurantData?.id ?? 0),
      );

      // 只有在首次加载时才调用API
      debugPrint("RestaurantCommentList初始化完成");

      // 初始加载评论数据
      if (restaurantId != 0) {
        debugPrint("初始加载评论数据: 餐厅ID=$restaurantId");
        ref.read(restaurantCommentControllerProvider.notifier).getCommentList(
              restaurantId: restaurantId,
              foodId: widget.foodId,
              isRefresh: true,
            );
      }
    });
  }

  /// 加载更多评论的简化方法，直接调用控制器
  Future<void> _loadMoreComments() async {
    // 从riverpod获取餐厅ID
    final restaurantId = ref.read(
      restaurantDetailControllerProvider
          .select((state) => state.restaurantData?.id ?? 0),
    );

    // 直接调用控制器的loadMoreComments方法
    await ref
        .read(restaurantCommentControllerProvider.notifier)
        .loadMoreComments(restaurantId, widget.foodId);
  }

  @override
  Widget build(final BuildContext context) {
    // 从riverpod获取餐厅ID
    final restaurantId = ref.watch(
      restaurantDetailControllerProvider
          .select((state) => state.restaurantData?.id ?? 0),
    );

    return CommentListSection(
      restaurantId: restaurantId,
      foodId: widget.foodId,
      loadMoreComments: _loadMoreComments,
      showStar: widget.showStar,
    );
  }
}
