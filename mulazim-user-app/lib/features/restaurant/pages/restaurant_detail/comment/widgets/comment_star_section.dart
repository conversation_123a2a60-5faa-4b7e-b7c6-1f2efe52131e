import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/data/models/restaurant/commnent_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 评论星级部分组件
class CommentStarSection extends ConsumerWidget {
  final bool showStar;

  const CommentStarSection({
    super.key,
    required this.showStar,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接从 RestaurantCommentController 读取评论星级数据
    final commentState = ref.watch(restaurantCommentControllerProvider);
    final star = commentState.star;

    // 星级数据为空时显示空布局
    if (star == null) {
      return const SizedBox.shrink();
    }

    return _buildStarSection(context, star);
  }

  // 构建评分部分UI - 遵循微信小程序的水平布局设计
  Widget _buildStarSection(BuildContext context, CommentStar star) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: const Color(0xFFEFF1F6),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 总分
          Column(
            children: [
              Text(
                star.starAvg?.toString() ?? "0",
                style: TextStyle(
                  fontSize: 25.sp, // 50rpx / 2
                  fontWeight: FontWeight.bold,
                  color: AppColors.baseOrangeColor2,
                  fontFamily: AppConstants.numberFont,
                  height: 1.2,
                ),
              ),
            ],
          ),

          // 商家评分 + 星级
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                S.current.shop_score,
                style: TextStyle(
                  fontSize: 13.sp, // 26rpx / 2
                  color: const Color(0xFF757575),
                  height: 1.3,
                ),
              ),
              SizedBox(height: 4.h), // 8rpx / 2
              StarRating(
                rating: star.starAvg?.toDouble() ?? 0,
                size: 16, // 32rpx / 2
                gap: 2,
                hideTip: true,
              ),
            ],
          ),

          // 口味评分
          _buildScoreItem(
            title: S.current.evaluate_taste,
            score: star.foodStarAvg?.toString() ?? "0.0",
          ),

          // 包装评分
          _buildScoreItem(
            title: S.current.evaluate_packing,
            score: star.boxStarAvg?.toString() ?? "0.0",
          ),

          // 包装评分和准时率之间的分割线
          Container(
            height: 45.h,
            width: 1.w,
            color: const Color(0xFFEFF1F6),
          ),

          // 准时率
          _buildScoreItem(
            title: S.current.on_time_percent,
            score: "${star.shipperAvg?.toString() ?? "0"}%",
          ),
        ],
      ),
    );
  }

  // 构建评分项 - 符合小程序布局
  Widget _buildScoreItem({
    required String title,
    required String score,
    bool isLast = false,
  }) {
    return Container(
      constraints: BoxConstraints(minWidth: 60.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 13.sp, // 26rpx / 2
              color: const Color(0xFF757575),
              height: 1.3,
            ),
          ),
          SizedBox(height: 4.h), // 8rpx / 2
          Text(
            score,
            style: TextStyle(
              fontSize: 20.sp, // 40rpx / 2
              fontWeight: FontWeight.w600,
              color: const Color(0xFF424242),
            ),
          ),
        ],
      ),
    );
  }
}
