import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/restaurant/commnent_list_model.dart';
import 'package:user_app/data/repositories/restaurant/restaurant_repository.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_state.dart';

part 'restaurant_comment_controller.g.dart';

/// 餐厅评论控制器
@riverpod
class RestaurantCommentController extends _$RestaurantCommentController {
  @override
  RestaurantCommentState build() {
    return const RestaurantCommentState();
  }

  /// 获取评论列表
  Future<void> getCommentList({
    required int restaurantId,
    int? foodId,
    int? type,
    bool isRefresh = true,
  }) async {
    // 如果restaurantId为0，则不进行请求
    if (restaurantId == 0) {
      state = const RestaurantCommentState();
      return;
    }

    // 设置类型为当前选中类型或默认值1
    final commentType = type ?? state.selectedType;

    // 更新加载状态
    if (isRefresh) {
      state = state.copyWith(
        isLoading: true,
        hasError: false,
        errorMessage: '',
        currentPage: 1,
        selectedType: commentType,
      );
    } else {
      state = state.copyWith(
        isLoadingMore: true,
        hasError: false,
        errorMessage: '',
      );
    }

    try {
      // 构建参数
      final params = {
        "id": restaurantId,
        "type": commentType,
        "page": isRefresh ? 1 : state.currentPage + 1,
        "limit": 10
      };

      // 如果有foodId参数，则添加到请求中
      if (foodId != null) {
        params["food_id"] = foodId;
      }

      debugPrint("请求参数: $params");

      // 获取repository
      final repository = ref.read(restaurantRepositoryProvider);

      // 发送请求 - 修正为使用getRestaurantCommentList而不是getCommentList
      final response = await repository.getRestaurantCommentList(params);

      // 处理响应
      if (response.status != 200) {
        throw Exception(response.msg ?? "获取评论失败");
      }

      // 将RestaurantCommentListData转换为CommentListData
      final restaurantCommentData = response.data!;
      final commentListData = CommentListData(
        perPage: restaurantCommentData.perPage,
        currentPage: restaurantCommentData.currentPage,
        lastPage: restaurantCommentData.lastPage,
        type: restaurantCommentData.type
            ?.map(
                (t) => CommnentType(name: t.name, type: t.type, count: t.count))
            .toList(),
        star: restaurantCommentData.star != null
            ? CommentStar(
                starAvg: restaurantCommentData.star!.starAvg,
                foodStarAvg: restaurantCommentData.star!.foodStarAvg,
                boxStarAvg: restaurantCommentData.star!.boxStarAvg,
                shipperAvg: restaurantCommentData.star!.shipperAvg,
              )
            : null,
        items: restaurantCommentData.items
            ?.map((item) => CommentItems(
                  id: item.id,
                  type: item.type,
                  createdAt: item.createdAt,
                  star: item.star,
                  text: item.text,
                  foodStar: item.foodStar,
                  boxStar: item.boxStar,
                  foodName: item.foodName,
                  foodId: item.foodId,
                  userAvatar: item.userAvatar,
                  userName: item.userName,
                  images: item.images,
                  replies: item.replies
                      ?.map((r) => {
                            'type': r.type,
                            'text': r.text,
                            'created_at': r.createdAt
                          })
                      .toList(),
                ))
            .toList(),
      );

      // 根据数据判断是否有更多
      bool hasMoreData = false;

      // 首先使用API返回的当前页/最大页判断
      if (commentListData.lastPage != null && commentListData.lastPage! > 0) {
        hasMoreData = commentListData.currentPage! < commentListData.lastPage!;
      }
      // 如果lastPage不可靠，则使用评论类型中的计数判断
      else if (commentListData.type != null &&
          commentListData.type!.isNotEmpty) {
        // 找到当前选中类型的评论总数
        final currentTypeInfo = commentListData.type!.firstWhere(
          (t) => t.type == commentType,
          orElse: () => commentListData.type!.first,
        );

        // 计算已加载的评论总数
        final loadedItemsCount = isRefresh
            ? (commentListData.items?.length ?? 0)
            : (state.commentData?.items?.length ?? 0) +
                (commentListData.items?.length ?? 0);

        // 如果已加载数量小于总数，则还有更多数据
        final totalCount = currentTypeInfo.count ?? 0;
        hasMoreData = loadedItemsCount < totalCount;

        debugPrint(
            '已加载评论数: $loadedItemsCount, 总评论数: $totalCount, 还有更多: $hasMoreData');
      }

      // 检查是否是首次加载
      final isFirstLoad = state.isFirstLoad;

      if (isRefresh) {
        // 刷新数据时
        state = state.copyWith(
          isLoading: false,
          commentData: commentListData,
          currentPage: commentListData.currentPage,
          canLoadMore: hasMoreData,
          // 设置isFirstLoad为false
          isFirstLoad: false,
        );

        // 首次加载或类型/星级为空时，更新这些数据
        if (isFirstLoad || state.star == null || state.types == null) {
          state = state.copyWith(
            star: commentListData.star,
            types: commentListData.type,
          );
        }
      } else {
        // 加载更多数据时，只更新items部分，保留原有的type和star
        final List<CommentItems> existingItems = state.commentData?.items ?? [];
        final List<CommentItems> newItems = commentListData.items ?? [];
        final List<CommentItems> mergedItems = [...existingItems, ...newItems];

        // 创建合并后的数据，只合并items部分，保留原始的type和star
        final mergedData = CommentListData(
          perPage: commentListData.perPage,
          currentPage: commentListData.currentPage,
          lastPage: commentListData.lastPage,
          type: state.commentData?.type, // 保留原有type
          star: state.commentData?.star, // 保留原有star
          items: mergedItems,
        );

        state = state.copyWith(
          isLoadingMore: false,
          commentData: mergedData,
          currentPage: commentListData.currentPage,
          canLoadMore: hasMoreData,
        );
      }
    } catch (e) {
      // 错误处理
      debugPrint("获取评论列表出错: $e");

      if (isRefresh) {
        state = state.copyWith(
          isLoading: false,
          hasError: true,
          errorMessage: e.toString(),
        );
      } else {
        state = state.copyWith(
          isLoadingMore: false,
          hasError: true,
          errorMessage: e.toString(),
        );
      }
    }
  }

  /// 加载更多评论
  Future<void> loadMoreComments(int restaurantId, [int? foodId]) async {
    if (!state.canLoadMore || state.isLoading || state.isLoadingMore) {
      return;
    }

    try {
      await getCommentList(
        restaurantId: restaurantId,
        foodId: foodId,
        isRefresh: false,
      );
    } catch (e) {
      debugPrint("加载更多评论出错: $e");
    }
  }

  /// 切换评论类型
  Future<void> changeCommentType({
    required int restaurantId,
    int? foodId,
    required int type,
  }) async {
    // 直接更新状态中的selectedType
    state = state.copyWith(selectedType: type);

    // 更新类型并刷新数据
    await getCommentList(
      restaurantId: restaurantId,
      foodId: foodId,
      type: type,
      isRefresh: true,
    );
  }

  /// 检查是否还有更多评论
  bool hasMoreComments() {
    // 如果状态中已经显示没有更多，直接返回false
    if (!state.canLoadMore) return false;

    // 如果正在加载中，暂时不加载更多
    if (state.isLoading || state.isLoadingMore) return false;

    // 检查根据评论类型判断是否有更多评论
    if (state.types != null && state.types!.isNotEmpty) {
      // 找到当前选中类型
      final selectedType = state.selectedType;
      final currentTypeInfo = state.types!.firstWhere(
        (type) => type.type == selectedType,
        orElse: () => state.types!.first,
      );

      // 计算已加载的评论数
      final loadedItems = state.commentData?.items?.length ?? 0;
      final totalCount = currentTypeInfo.count ?? 0;

      if (totalCount == 0) {
        return true;
      }

      // 如果已加载数量小于总数，则还有更多数据
      return loadedItems < totalCount;
    }

    // 默认返回state中的判断
    return state.canLoadMore;
  }
}

/// 餐厅评论 Repository Provider
@riverpod
RestaurantRepository restaurantRepository(RestaurantRepositoryRef ref) {
  return RestaurantRepository(apiClient: ref.read(apiClientProvider));
}
