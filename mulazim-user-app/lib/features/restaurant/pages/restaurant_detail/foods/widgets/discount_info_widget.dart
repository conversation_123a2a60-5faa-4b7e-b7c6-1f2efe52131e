import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/discount_prompt_text.dart';

/// 满减和配送费减免信息组件
class DiscountInfoWidget extends StatelessWidget {
  /// 满减值信息
  final Map<String, dynamic>? reductionValues;

  /// 配送费减免信息
  final Map<String, dynamic>? shipmentValues;

  /// 语言
  final String lang;

  /// 构造函数
  const DiscountInfoWidget({
    super.key,
    required this.reductionValues,
    required this.shipmentValues,
    required this.lang,
  });

  @override
  Widget build(final BuildContext context) {
    /// 是否显示满减信息
    final isShowReduction = reductionValues != null &&
        (reductionValues!['isCurrentTierMet'] == true ||
            reductionValues!['remainingAmount'] != null ||
            reductionValues!['currentTierPrice'] != null ||
            reductionValues!['currentDiscount'] != null);

    /// 是否显示配送费减免信息
    final isShowShipment = shipmentValues != null &&
        (shipmentValues!['isCurrentTierMet'] == true ||
            shipmentValues!['remainingAmount'] != null ||
            shipmentValues!['currentTierPrice'] != null ||
            shipmentValues!['actualFee'] != null);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 满减信息 - 仅当有内容可以显示时才显示
        if (isShowReduction)
          Container(
            padding: EdgeInsets.symmetric(vertical: 3.h),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(50),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildReductionText(reductionValues, lang),
              ],
            ),
          ),
        // 配送费减免信息 - 仅当有内容可以显示时才显示
        if (isShowShipment)
          Container(
            padding: EdgeInsets.symmetric(vertical: 3.h),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(50),
              border: Border(
                top: BorderSide(color: Colors.red.shade100, width: 1.0),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildShipmentText(shipmentValues, lang),
              ],
            ),
          ),
      ],
    );
  }

  // 构建满减文本
  Widget _buildReductionText(
      final Map<String, dynamic>? values, final String lang) {
    return DiscountPromptText(
      values: values,
      isShipment: false,
      lang: lang,
    );
  }

  // 构建配送费减免文本
  Widget _buildShipmentText(
      final Map<String, dynamic>? values, final String lang) {
    return DiscountPromptText(
      values: values,
      isShipment: true,
      lang: lang,
      fontFamily: "UkijTuzTom",
      textStyle: TextStyle(fontSize: 16.sp, color: Colors.red),
      boldTextStyle: TextStyle(
          fontSize: 16.sp, color: Colors.red, fontWeight: FontWeight.bold),
    );
  }
}
