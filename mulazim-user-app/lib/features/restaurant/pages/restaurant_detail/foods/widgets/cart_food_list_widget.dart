import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/calculation_utils.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/cart_item_count_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/multi_discount_popup_widget.dart';

/// 购物车食品列表组件
class CartFoodListWidget extends ConsumerWidget {
  /// 关闭购物车列表回调
  final VoidCallback onClose;

  /// 关闭购物车列表并清空回调
  final VoidCallback onClearAndClose;

  /// 构造函数
  const CartFoodListWidget({
    super.key,
    required this.onClose,
    required this.onClearAndClose,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final screenHeight = MediaQuery.of(context).size.height;
    // 使用select精确监听购物车项目状态
    final cartItems = ref.watch(shoppingCartProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.r),
          topRight: Radius.circular(8.r),
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.all(10.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 固定头部
                Container(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  padding: EdgeInsets.symmetric(vertical: 5.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        S.current.selected,
                        style: TextStyle(
                          fontSize: 18.sp,
                          color: Color(0xff333333),
                        ),
                      ),
                      Row(
                        children: [
                          InkWell(
                            onTap: onClearAndClose,
                            child: Row(
                              children: [
                                // 清空
                                Icon(
                                  Icons.delete_forever,
                                  color: Color(0xff999999),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 20.w),
                          InkWell(
                            onTap: onClose,
                            child: Icon(
                              Icons.close,
                              size: 30.w,
                              color: Color(0xff666666),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 动态商品列表
                Container(
                  constraints: BoxConstraints(maxHeight: screenHeight * 0.5),
                  child: cartItems.isEmpty
                      ? Center(
                          child: Padding(
                            padding: EdgeInsets.only(top: 50.h),
                            child: Column(
                              children: [
                                Image.asset(
                                  'assets/images/order/empty_card.png',
                                  width: 160.w,
                                  height: 160.w,
                                ),
                                SizedBox(height: 10.h),
                                Text(
                                  "${S.current.shopping_cart_empty}...",
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.textHintColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: ClampingScrollPhysics(),
                          itemCount: cartItems.length,
                          itemBuilder: (final context, final idx) {
                            final item = cartItems[idx];
                            final foodsPrice = CalculationUtils.getFinalPrice(
                              cartFoodItem: item,
                            );

                            final foodsOriginalPrice =
                                (item.oldPrice ?? 0) * (item.count ?? 0);

                            final isMultiDiscount =
                                item.multiDiscountId != null &&
                                    item.multiDiscountId! > 0 &&
                                    item.multiDiscountSteps != null &&
                                    item.multiDiscountSteps!.isNotEmpty;

                            // 格式化规格选择信息 - 与微信小程序逻辑一致
                            final specOptionsText = _formatSpecSelectedOptions(
                              item.specSelectedOptions,
                            );

                            return Container(
                              margin: EdgeInsets.symmetric(vertical: 10.h),
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8.r),
                                    child: CachedNetworkImage(
                                      imageUrl: item.foodsImage ?? "",
                                      width: 64.w,
                                      height: 64.w,
                                      fit: BoxFit.cover,
                                      errorWidget: (
                                        final context,
                                        final error,
                                        final stackTrace,
                                      ) =>
                                          Container(
                                        width: 64.w,
                                        height: 64.w,
                                        color: Colors.grey.shade300,
                                        child: Icon(
                                          Icons.image_not_supported,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 13.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceAround,
                                      children: [
                                        // 商品名称
                                        Text(
                                          item.foodsName ??
                                              S.current.unknown_product,
                                          style: TextStyle(
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.textPrimaryColor,
                                          ),
                                        ),

                                        // 规格选择信息 - 与微信小程序逻辑一致
                                        if (specOptionsText.isNotEmpty)
                                          Container(
                                            margin: EdgeInsets.symmetric(
                                              vertical: 4.h,
                                            ),
                                            child: Text(
                                              specOptionsText,
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: Color(0xFF8D8C8C),
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),

                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              padding: EdgeInsets.zero,
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    "¥ ",
                                                    style: TextStyle(
                                                      fontSize: 16.sp,
                                                      color: Colors.red,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  Text(
                                                    FormatUtil.formatPrice(
                                                      foodsPrice,
                                                    ),
                                                    style: TextStyle(
                                                      fontSize: 20.sp,
                                                      color: Colors.red,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  SizedBox(width: 5.w),

                                                  // 原价
                                                  if (foodsOriginalPrice >
                                                      foodsPrice)
                                                    Text(
                                                      "¥ ",
                                                      style: TextStyle(
                                                        fontSize: 16.sp,
                                                        color: Colors.grey,
                                                        decoration:
                                                            TextDecoration
                                                                .lineThrough,
                                                        decorationColor:
                                                            Colors.grey,
                                                      ),
                                                    ),
                                                  if (foodsOriginalPrice >
                                                      foodsPrice)
                                                    Text(
                                                      FormatUtil.formatPrice(
                                                        foodsOriginalPrice,
                                                      ),
                                                      style: TextStyle(
                                                        fontSize: 16.sp,
                                                        color: Colors.grey,
                                                        decoration:
                                                            TextDecoration
                                                                .lineThrough,
                                                        decorationColor:
                                                            Colors.grey,
                                                      ),
                                                    ),
                                                  // 多份打折
                                                  if (isMultiDiscount)
                                                    SizedBox(width: 10.w),
                                                  if (isMultiDiscount)
                                                    GestureDetector(
                                                      onTapDown: (
                                                        final TapDownDetails
                                                            details,
                                                      ) {
                                                        // 显示popupmenu
                                                        _showCustomMenu(
                                                          context,
                                                          item,
                                                          details
                                                              .globalPosition,
                                                        );
                                                      },
                                                      child: Icon(
                                                        Icons.help_outline,
                                                        size: 22.sp,
                                                        color:
                                                            Color(0xFFFF7430),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                            // 购物车加减按钮
                                            CartItemCountWidget(idx: idx),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化规格选择选项文本 - 与微信小程序逻辑一致
  String _formatSpecSelectedOptions(
    final List<Map<String, dynamic>>? specSelectedOptions,
  ) {
    if (specSelectedOptions == null || specSelectedOptions.isEmpty) {
      return '';
    }

    return specSelectedOptions
        .map((final option) => option['name']?.toString() ?? '')
        .where((final name) => name.isNotEmpty)
        .join('，');
  }

  void _showCustomMenu(
    final BuildContext context,
    final SelectFoodItem food,
    final Offset position,
  ) async {
    // 使用独立的多重折扣弹窗组件
    await MultiDiscountPopupWidget.show(
      context,
      food: food,
      position: position,
    );
  }
}
