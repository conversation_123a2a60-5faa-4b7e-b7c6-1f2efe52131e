import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';

/// 购物车商品数量控制组件
class CartItemCountWidget extends ConsumerWidget {
  /// 商品在购物车列表中的索引
  final int idx;

  /// 构造函数
  const CartItemCountWidget({
    super.key,
    required this.idx,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 使用select精确监听购物车项目，只在索引对应项发生变化时重建
    final cartItems = ref.watch(shoppingCartProvider);
    final cartNotifier = ref.read(shoppingCartProvider.notifier);

    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 获取当前购物车项目
    if (idx >= cartItems.length) {
      return const SizedBox.shrink();
    }

    final cartItem = cartItems[idx];

    return Directionality(
      textDirection: isUg ? TextDirection.ltr : TextDirection.rtl,
      child: Row(
        children: [
          addRemoveIcon(
            type: CartType.add,
            onTap: (final context) {
              // 直接传递specUniqueId，让方法内部处理规格识别
              cartNotifier.addFoodCount(
                foodId: cartItem.id ?? 0,
                specUniqueId: cartItem.specUniqueId,
              );
            },
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 7.w),
            alignment: Alignment.center,
            constraints: BoxConstraints(minWidth: 30.w),
            child: Text(
              "${cartItem.count ?? 0}",
              style: TextStyle(fontSize: 16.sp),
            ),
          ),
          addRemoveIcon(
            type: CartType.remove,
            onTap: (final context) {
              // 直接传递specUniqueId，让方法内部处理规格识别
              cartNotifier.removeFoodCount(
                foodId: cartItem.id ?? 0,
                specUniqueId: cartItem.specUniqueId,
              );
            },
          ),
        ],
      ),
    );
  }
}
