// import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/primary_scroll_container.dart';
import 'package:user_app/data/models/restaurant/restaurant_home_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/comment/restaurant_comment_list.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/detail/restaurant_detail_info.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/nested_example/app_bar.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/two/widgets/restaurant_foods_list_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_action_buttons.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_bottom_bar_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/two/widgets/restaurant_top_info_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_tags_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_background_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

///
/// 超市便利样式的详情页面（样式二）
/// 用于展示超市便利的详细信息、商品列表、评论等内容
/// 该页面包含超市便利头部信息、选项卡导航和对应的内容区域
/// 选项卡包括：菜单、评论和商家信息三个部分
/// 页面采用Sliver结构实现滚动效果和吸顶导航
class RestaurantStyleTwo extends ConsumerStatefulWidget {
  /// 构造函数
  const RestaurantStyleTwo({
    super.key,
  });

  @override
  ConsumerState<RestaurantStyleTwo> createState() => _RestaurantStyleTwoState();
}

class _RestaurantStyleTwoState extends ConsumerState<RestaurantStyleTwo>
    with SingleTickerProviderStateMixin {
  /// Tab控制器，用于管理Tab切换
  late TabController _tabController;

  /// 每个Tab页面的PrimaryScrollContainer的GlobalKey
  final List<GlobalKey<PrimaryScrollContainerState>> scrollChildKeys = [
    GlobalKey<PrimaryScrollContainerState>(),
    GlobalKey<PrimaryScrollContainerState>(),
    GlobalKey<PrimaryScrollContainerState>(),
  ];

  /// 滚动控制器，用于监听滚动位置
  late ScrollController _nestedScrollController;

  /// 是否显示标题
  bool _showTitle = false;

  /// 标题显示的阈值（当滚动超过这个值时显示标题）
  double _titleShowThreshold = 50.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _nestedScrollController = ScrollController();

    // 添加Tab切换监听器，控制每个Tab页面的显示状态
    _tabController.addListener(() {
      for (int i = 0; i < scrollChildKeys.length; i++) {
        GlobalKey<PrimaryScrollContainerState> key = scrollChildKeys[i];
        if (key.currentState != null) {
          // 控制是否当前显示，只有当前选中的Tab才显示
          key.currentState!.onPageChange(_tabController.index == i);
        }
      }
    });

    // 添加滚动监听器，监听滚动位置变化
    _nestedScrollController.addListener(_onScrollChanged);
  }

  /// 滚动位置变化监听器
  void _onScrollChanged() {
    if (!_nestedScrollController.hasClients) return;

    final offset = _nestedScrollController.offset;

    final shouldShowTitle = offset > _titleShowThreshold;
    print(
        'offset: $offset, _titleShowThreshold: $_titleShowThreshold, _showTitle: $shouldShowTitle');

    if (shouldShowTitle != _showTitle) {
      setState(() {
        _showTitle = shouldShowTitle;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nestedScrollController.removeListener(_onScrollChanged);
    _nestedScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 直接从Riverpod获取所需数据
    final restaurantData = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantData),
    );
    final isResting = restaurantData?.isResting ?? false;
    final market = ref.watch(
      restaurantDetailControllerProvider.select((final state) => state.market),
    );

    // 跨平台兼容的高度计算
    final appBarHeight = 36.h;
    // final appBarHeight = AppBar().preferredSize.height;

    // 使用屏幕高度的比例来计算，确保跨平台一致性
    final screenHeight = MediaQuery.of(context).size.height;

    // 基础内容高度：占屏幕高度的30%，最小200dp，最大300dp
    final baseContentHeight = (screenHeight * 0.3).clamp(160.0, 180.0);

    // 总背景高度 = 基础内容高度 + TabBar高度
    var backgroundH = baseContentHeight.h + appBarHeight;

    // 根据标签情况调整高度
    final tags = market?.tags;
    final hasNoTags = (tags?.shipmentReductionTags?.isEmpty ?? true) &&
        (tags?.reductionTags?.isEmpty ?? true) &&
        (tags?.multiDiscountTags?.isEmpty ?? true);

    if (hasNoTags) {
      backgroundH = backgroundH - 25.h; // 减去标签区域高度
    }

    // 更新标题显示阈值为背景高度的70%
    _titleShowThreshold = backgroundH * 0.5;

    return Scaffold(
      body: NestedScrollView(
        controller: _nestedScrollController,
        headerSliverBuilder:
            (final BuildContext context, final bool innerBoxIsScrolled) {
          return <Widget>[
            _buildHeader(
              context,
              innerBoxIsScrolled,
              restaurantData,
              backgroundH,
              appBarHeight,
            ),
          ];
        },
        body: _buildTabBarView(isResting),
      ),
    );
  }

  // 头部
  Widget _buildHeader(
    final BuildContext context,
    final bool innerBoxIsScrolled,
    final RestaurantHomeData? restaurantData,
    final double backgroundH,
    final double appBarHeight,
  ) {
    // SliverOverlapAbsorber 的作用是处理重叠滚动效果，
    // 防止 CustomScrollView 中的滚动视图与其他视图重叠。
    return SliverOverlapAbsorber(
      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
      sliver: SliverAppBar(
        // 使用自定义的滚动监听来控制标题显示，而不是依赖innerBoxIsScrolled
        title: AnimatedOpacity(
          opacity: _showTitle ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(
            restaurantData?.name ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontFamily: AppConstants.mainFont,
            ),
          ),
        ),
        pinned: true,
        elevation: 0, //影深
        expandedHeight: backgroundH,
        forceElevated: innerBoxIsScrolled, //为true时展开有阴影
        flexibleSpace: FlexibleSpaceBar(
          background: Stack(
            children: [
              RestaurantBackgroundWidget(
                imageUrl: restaurantData?.logo ?? "",
                height: backgroundH + 20.h,
                blurSigma: backgroundH,
                gradientTopOpacity: 0.5,
                gradientBottomOpacity: 0.5,
              ),
              Positioned(
                left: 0,
                right: 0,

                /// 与底部固定栏上面, 25.h 是[RestaurantTagsWidget]的高度
                bottom: appBarHeight,
                child: Column(
                  children: [
                    const RestaurantTopInfoWidget(),
                    Container(
                      color: Colors.white,
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      child: const RestaurantTagsWidget(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // 底部固定栏
        bottom: MyCustomAppBar(
          height: appBarHeight,
          child: Column(
            children: [
              // 选项卡
              Container(
                height: appBarHeight,
                color: Colors.white,
                child: TabBar(
                  controller: _tabController, // 使用TabController
                  labelColor: AppColors.primary,
                  unselectedLabelColor: AppColors.textSecondColor,
                  indicatorColor: AppColors.primary,
                  indicatorSize: TabBarIndicatorSize.label,
                  // 调整标签内间距，改进布局
                  padding: EdgeInsets.only(bottom: 5.h),
                  labelStyle: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: AppConstants.mainFont,
                    fontWeight: FontWeight.bold,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: AppConstants.mainFont,
                  ),
                  // 指示器样式
                  indicator: UnderlineTabIndicator(
                    // 指示器边框样式
                    borderSide:
                        BorderSide(width: 3.h, color: AppColors.primary),
                    // 指示器边框圆角
                    borderRadius: BorderRadius.circular(5.r),
                  ),
                  // 指示器内边距
                  indicatorPadding: EdgeInsets.only(bottom: 5.h),
                  // 分隔线
                  dividerHeight: 0,
                  dividerColor: AppColors.dividerColor,
                  tabs: [
                    Tab(text: S.current.list),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            S.current.evaluate,
                            style: TextStyle(
                              fontFamily: AppConstants.mainFont,
                            ),
                          ),
                          Text(
                            ' ${restaurantData?.commentCount ?? 0}',
                            style: TextStyle(
                              fontSize: 10.sp,
                              fontFamily: AppConstants.mainFont,
                              color: AppColors.textSecondColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Tab(
                      text: S.current.business,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          // 顶部操作按钮
          AnimatedOpacity(
            opacity: _showTitle ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Consumer(
                builder: (final context, final ref, final child) {
                  // 获取收藏状态
                  final isFavorite = ref.watch(
                    restaurantDetailControllerProvider.select(
                      (final state) => state.isFavorite,
                    ),
                  );

                  return RestaurantActionButtons.horizontal(
                    onLike: () {
                      // 处理收藏操作
                      ref
                          .read(restaurantDetailControllerProvider.notifier)
                          .toggleFavorite();
                    },
                    onShare: () {
                      // 处理分享操作
                      ref
                          .read(restaurantDetailControllerProvider.notifier)
                          .shareRestaurant();
                    },
                    restaurantId: restaurantData?.id.toString(),
                    buttonSize: 25,
                    iconSize: 25,
                    spacing: 20,
                    isFavorite: isFavorite,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBarView(final bool isResting) {
    return AnimatedBuilder(
      animation: _tabController,
      builder: (final context, final child) {
        return IndexedStack(
          index: _tabController.index, // 根据TabController的index显示对应页面
          children: [
            // 菜单选项卡：使用PrimaryScrollContainer包装，初始显示
            PrimaryScrollContainer(
              scrollChildKeys[0], // GlobalKey
              Stack(
                // 主体内容区域
                children: [
                  const RestaurantFoodsListWidget(),
                  // 底部导航栏
                  Positioned(
                    bottom: isResting ? 0.w : 12.w,
                    right: isResting ? 0.w : 12.w,
                    left: isResting ? 0.w : 12.w,
                    child: RestaurantBottomBarWidget(
                      cartIconKey: globalCartIconKey,
                    ),
                  ),
                ],
              ),
              initialShow: true, // 第一个Tab初始显示
            ),
            // 评论选项卡：使用PrimaryScrollContainer包装，初始不显示
            PrimaryScrollContainer(
              scrollChildKeys[1], // GlobalKey
              const RestaurantCommentList(),
              initialShow: false, // 第二个Tab初始不显示
            ),
            // 商家信息选项卡：使用PrimaryScrollContainer包装，初始不显示
            PrimaryScrollContainer(
              scrollChildKeys[2], // GlobalKey
              const RestaurantDetailInfo(),
              initialShow: false, // 第三个Tab初始不显示
            ),
          ],
        );
      },
    );
  }
}
