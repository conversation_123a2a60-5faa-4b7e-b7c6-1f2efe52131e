import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 餐厅评分、月订单和配送时间信息组件
/// 用于三种不同样式的餐厅详情页共用
class ResOrderNumberWidget extends StatelessWidget {
  /// 构造函数
  const ResOrderNumberWidget({
    super.key,
    required this.starAvg,
    required this.sales,
    this.avgDeliveryTime = '0',
    this.range,
    this.distance,
    this.color = AppColors.textSecondColor,
    this.iconColor = AppColors.baseOrangeColor2,
    this.fontSize = 14,
    this.padding,
  });

  /// 餐厅评分
  final String starAvg;

  /// 月订单量
  final String sales;

  /// 平均配送时间（分钟）
  final String avgDeliveryTime;

  /// 配送范围（距离，如 2km）
  final String? range;

  /// 距离（米）
  final String? distance;

  /// 文字颜色
  final Color color;

  /// 图标颜色
  final Color iconColor;

  /// 字体大小
  final double fontSize;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(final BuildContext context) {
    return Container(
      padding: padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧信息：评分、月销量、配送时间
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 评分
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      starAvg,
                      style: TextStyle(
                        color: iconColor,
                        fontSize: fontSize.sp,
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Icon(
                      Icons.star,
                      color: iconColor,
                      size: (fontSize + 2).sp,
                    ),
                  ],
                ),
                SizedBox(width: 5.w),

                // 月销量
                Flexible(
                  child: Text(
                    '${S.current.month_order_count}: $sales',
                    style: TextStyle(
                      color: color,
                      fontSize: fontSize.sp,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 10.w),

                // 配送时间
                if (avgDeliveryTime != '0')
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.w),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          avgDeliveryTime,
                          style: TextStyle(
                            color: color,
                            fontSize: fontSize.sp,
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          S.current.minute,
                          style: TextStyle(
                            color: color,
                            fontSize: fontSize.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // 右侧信息：距离或配送范围
          if (distance != null && distance!.isNotEmpty && distance != '0')
            Text(
              '${distance}Km',
              style: TextStyle(
                color: color,
                fontSize: fontSize.sp,
              ),
            )
          else if (range != null &&
              range!.isNotEmpty &&
              range != '0' &&
              range != 'undefinedKm')
            Text(
              range!.endsWith('km') ? range! : '${range!}km',
              style: TextStyle(
                color: color,
                fontSize: fontSize.sp,
              ),
            ),
        ],
      ),
    );
  }
}
