import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';
import 'restaurant_category_chip_widget.dart';

/// 餐厅菜品分类标签栏组件
///
/// 用于展示餐厅菜品的分类标签栏，如"热销"、"主食"、"套餐"等分类
/// 支持水平滚动，显示多个分类标签
/// 管理分类标签的选中状态，并通过回调函数通知父组件
class RestaurantCategoryTabsWidget extends ConsumerWidget {
  /// 菜品分类数据列表
  final List<FoodType>? foodTypes;

  /// 当前选中的分类索引
  final int selectedCategoryIndex;

  /// 分类选中状态变化的回调函数
  final Function(int) onCategorySelected;

  /// 是否有秒杀商品
  final bool hasSeckill;

  /// 构造函数
  ///
  /// @param key Widget的key
  /// @param foodTypes 菜品分类数据列表
  /// @param selectedCategoryIndex 当前选中的分类索引
  /// @param onCategorySelected 分类选中状态变化的回调函数
  /// @param hasSeckill 是否有秒杀商品
  const RestaurantCategoryTabsWidget({
    super.key,
    required this.foodTypes,
    required this.selectedCategoryIndex,
    required this.onCategorySelected,
    this.hasSeckill = false,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 处理分类数据为空的情况
    if (foodTypes == null || foodTypes!.isEmpty) {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Text(
          S.current.search_nothing_food,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey,
          ),
        ),
      );
    }

    // 构建分类标签列表
    List<Widget> categoryWidgets =
        (foodTypes ?? []).asMap().entries.map((final entry) {
      int index = entry.key;
      var item = entry.value;

      // 如果是第一个分类且类型为0（全部），当有秒杀商品时显示为"秒杀专区"
      String displayName = item.name ?? "";
      if (index == 0 && item.type == 0 && hasSeckill) {
        displayName = S.current.sec_kill; // 秒杀专区
      }

      // 使用分类选项组件显示每个分类
      return RestaurantCategoryChipWidget(
        text: displayName,
        isSelected: selectedCategoryIndex == index,
        onTap: () {
          debugPrint('选择分类: 索引=$index, ID=${item.id}, 名称=${item.name}');
          onCategorySelected(index);
        },
      );
    }).toList();

    // 使用水平滚动视图显示所有分类
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: categoryWidgets,
      ),
    );
  }
}
