import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/res_order_number_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 餐厅顶部信息组件
/// 用于显示餐厅的基本信息，如店名、评分、配送时间等
class RestaurantTopInfoWidget extends ConsumerWidget {
  /// 构造函数
  const RestaurantTopInfoWidget({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 直接从Riverpod获取餐厅数据
    final data = ref.watch(
      restaurantDetailControllerProvider
          .select((state) => state.restaurantData),
    );

    return Stack(
      children: [
        // 白色背景，从餐厅名称开始向下覆盖
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          height: 30.h, // 调整高度以覆盖从餐厅名称开始的下半部分
          child: Container(
            color: Colors.white,
          ),
        ),
        // 餐厅信息内容
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 8.w,
            vertical: 5.h,
          ),
          child: Row(
            children: [
              // 餐厅头像
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CachedNetworkImage(
                      imageUrl: data?.logo ?? '',
                      width: 70.w,
                      height: 70.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                  // 活动标签
                  if (data?.isHaveMarket == true ||
                      data?.isHaveShipmentReduce == true ||
                      data?.isHavePref == true)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              width: 70.w,
                              height: 18.h,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.baseOrangeColor2.withOpacity(0.7),
                                    AppColors.baseOrangeColor2,
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r),
                                ),
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Image.asset(
                                'assets/images/fire.png',
                                width: 22.w,
                                height: 28.h,
                              ),
                              SizedBox(width: 2.w),
                              Padding(
                                padding: EdgeInsets.only(bottom: 2.h),
                                child: Text(
                                  S.current.has_food_pre,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              SizedBox(width: 12.w),
              // 餐厅基本信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 餐厅名称
                    Text(
                      data?.name ?? '',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 10.h),
                    // 使用统一的餐厅评分和月销量组件
                    ResOrderNumberWidget(
                      starAvg: data?.starAvg?.toString() ?? '0.0',
                      sales: data?.monthOrderCount?.toString() ?? '0',
                      avgDeliveryTime: data?.avgDeliveryTime?.toString() ?? '0',
                      distance: data?.distance?.toString(),
                      color: AppColors.textPrimaryColor,
                      iconColor: AppColors.baseOrangeColor2,
                      fontSize: 14,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
