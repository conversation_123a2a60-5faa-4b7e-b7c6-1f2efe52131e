import 'package:flutter/material.dart';

/// 自定义SliverPersistentHeaderDelegate实现
///
/// 用于创建固定高度的粘性头部组件，如选项卡栏
/// 在餐厅详情页中用于创建固定在顶部的选项卡导航
/// 该实现简化了SliverPersistentHeader的使用，固定高度为48像素
class SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  /// 要显示的子组件
  final Widget child;

  /// 构造函数
  ///
  /// @param child 要在粘性头部显示的子组件
  SliverAppBarDelegate({
    required this.child,
  });

  /// 最小高度
  ///
  /// 返回48.0，这是TabBar的默认高度
  @override
  double get minExtent => 48.0; // TabBar 的默认高度

  /// 最大高度
  ///
  /// 返回48.0，与最小高度相同，确保高度固定
  @override
  double get maxExtent => 48.0; // TabBar 的默认高度

  /// 构建方法
  ///
  /// 创建一个填满可用空间的固定高度组件
  ///
  /// @param context 构建上下文
  /// @param shrinkOffset 收缩偏移量
  /// @param overlapsContent 是否与内容重叠
  @override
  Widget build(
    final BuildContext context,
    final double shrinkOffset,
    final bool overlapsContent,
  ) {
    return SizedBox.expand(child: child); // 使用 SizedBox.expand 确保子组件填充整个空间
  }

  /// 是否需要重建
  ///
  /// 当子组件变更时重建
  ///
  /// @param oldDelegate 旧的代理对象
  @override
  bool shouldRebuild(final SliverAppBarDelegate oldDelegate) {
    return child != oldDelegate.child;
  }
}
