import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/res_order_number_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_tags_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 便利店样式的顶部信息组件
/// 用于显示便利店的基本信息，如店名、评分、配送时间等
class RestaurantTopInfoWidget extends ConsumerWidget {
  /// 构造函数
  const RestaurantTopInfoWidget({
    super.key,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 从Riverpod获取数据
    final data = ref.watch(
      restaurantDetailControllerProvider
          .select((state) => state.restaurantData),
    );

    // 获取食品数据
    final foodsData = ref.watch(
      restaurantDetailControllerProvider.select((state) => state.foods),
    );

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w),
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.r)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 1),
            blurRadius: 3,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, -2),
            blurRadius: 3,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 便利店信息行
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 便利店头像
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.network(
                      data?.logo ?? '',
                      width: 50.w,
                      height: 50.w,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (final context, final error, final stackTrace) =>
                              Container(
                        width: 50.w,
                        height: 50.w,
                        color: Colors.grey[300],
                        child: const Icon(Icons.store, color: Colors.grey),
                      ),
                    ),
                  ),
                  // 活动标签
                  if (data?.isHaveMarket == true ||
                      data?.isHaveShipmentReduce == true ||
                      data?.isHavePref == true)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Stack(
                        children: [
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              width: 68.w,
                              height: 18.h,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.baseOrangeColor2.withOpacity(0.7),
                                    AppColors.baseOrangeColor2,
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r),
                                ),
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Image.asset(
                                'assets/images/fire.png',
                                width: 10.w,
                                height: 10.h,
                              ),
                              SizedBox(width: 2.w),
                              Padding(
                                padding: EdgeInsets.only(bottom: 2.h),
                                child: Text(
                                  S.current.has_food_pre,
                                  style: TextStyle(
                                    fontSize: 8.sp,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              SizedBox(width: 12.w),
              // 便利店基本信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 便利店名称
                    Text(
                      data?.name ?? '',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    // 使用统一的便利店评分和月销量组件
                    ResOrderNumberWidget(
                      starAvg: data?.starAvg?.toString() ?? '0.0',
                      sales: data?.monthOrderCount?.toString() ?? '0',
                      avgDeliveryTime: data?.avgDeliveryTime?.toString() ?? '0',
                      distance: data?.distance?.toString(),
                      color: AppColors.textSecondColor,
                      iconColor: AppColors.baseOrangeColor2,
                      fontSize: 14,
                    ),
                  ],
                ),
              ),
            ],
          ),
          // 优惠信息横向滚动区域
          const RestaurantTagsWidget(),
        ],
      ),
    );
  }
}
