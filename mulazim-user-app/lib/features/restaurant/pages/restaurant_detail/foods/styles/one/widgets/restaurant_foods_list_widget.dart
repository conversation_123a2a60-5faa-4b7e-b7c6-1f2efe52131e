import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/index.dart';

import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/styles/widgets/restaurant_countdown_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_search_bar.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/restaurant_foods_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 餐厅食品列表组件
///
/// 采用Google外卖的经典布局，顶部分类标签栏+底部商品列表
class RestaurantFoodsListWidget extends ConsumerStatefulWidget {
  /// 构造函数
  const RestaurantFoodsListWidget({
    super.key,
  });

  @override
  ConsumerState<RestaurantFoodsListWidget> createState() =>
      _RestaurantFoodsListWidgetState();
}

class _RestaurantFoodsListWidgetState
    extends ConsumerState<RestaurantFoodsListWidget> {
  /// 搜索控制器
  final SearchBarController _searchBarController = SearchBarController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    // 使用select只监听需要的状态字段
    final selectedCategoryIndex = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.selectedCategoryIndex),
    );

    // final isFoodsLoading = ref.watch(
    //   restaurantDetailControllerProvider
    //       .select((final state) => state.isFoodsLoading),
    // );

    final restaurantId = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantData?.id ?? 0),
    );

    final isResting = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantData?.isResting),
    );

    // 监听购物车数据以便在购物车变化时更新UI
    ref.watch(shoppingCartProvider);

    // 从controller获取数据
    final foodsData = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.foodsData),
    );

    // 获取控制器
    final controller = ref.read(restaurantDetailControllerProvider.notifier);

    // 食品分类数据
    final foodTypes = foodsData?.foodType ?? [];

    // 获取当前选择的分类
    final selectedCategory =
        foodTypes.isNotEmpty && selectedCategoryIndex < foodTypes.length
            ? foodTypes[selectedCategoryIndex]
            : null;

    // 该分类下的商品列表
    final foodsList = foodsData?.foods ?? [];

    // 检查是否有秒杀商品
    final bool hasSeckill =
        foodsList.any((final food) => food.seckillActive == 1);

    // 检查当前分类是否是秒杀专区
    bool isSecKill = selectedCategory?.type == 0 &&
        (selectedCategory?.name == S.current.sec_kill || hasSeckill);
    // 如果当前分类不是秒杀专区，但有商品是秒杀商品，也显示倒计时
    if (!isSecKill) {
      isSecKill = foodsList.any((final food) => food.seckillActive == 1);
    }

    // 检查数据是否为空,且不是资质类型
    // final bool hasNoFoodsData = foodsData?.foods?.isEmpty ?? true;

    return Consumer(
      builder: (final context, final ref, final child) {
        return CustomRefreshIndicator(
          onRefresh: () async {},
          enablePullDown: false,
          enablePullUp: true,
          footerHeight: 150.h,
          hideNoMore: true,
          hasMoreData: ref.watch(
            restaurantDetailControllerProvider
                .select((final state) => state.hasMoreData),
          ),
          onLoading: () async {
            await controller.loadNextPage();
          },
          child: CustomScrollView(
            slivers: [
              SliverOverlapInjector(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              ),

              // 显示搜索栏
              SliverToBoxAdapter(
                child: RestaurantSearchBar(
                  controller: _searchBarController,
                  style: 1,
                ),
              ),

              // 倒计时组件，只在有秒杀活动的时候显示
              if (isSecKill)
                SliverToBoxAdapter(
                  child: RestaurantCountdownWidget(
                    top: 0,
                    foods: foodsData?.foods ?? [],
                  ),
                ),

              // 食品列表 - 使用SliverPadding和SliverList，避免嵌套滚动视图
              SliverPadding(
                padding: EdgeInsets.only(top: 5.h, left: 8.w, right: 8.w),
                sliver: SliverList.builder(
                  key: const ValueKey<String>("foods_list"),
                  itemCount: foodsList.length + 1,
                  itemBuilder: (final context, final index) {
                    if (index == foodsList.length) {
                      return Consumer(
                        builder: (final context, final ref, final child) {
                          final hasMoreData = ref.watch(
                            restaurantDetailControllerProvider
                                .select((final state) => state.hasMoreData),
                          );
                          if (hasMoreData) {
                            return const SizedBox.shrink();
                          }

                          return Container(
                            height: 70.h,
                            alignment: Alignment.topCenter,
                            child: Text(S.current.no_more_data),
                          );
                        },
                      );
                    }

                    final food = foodsList[index];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 8.h),
                      child: RestaurantFoodsItem(
                        style: 1, // 餐厅样式
                        foodItem: food,
                        relations: food.relations,
                        restaurantId: restaurantId,
                        isResting: isResting,
                        fromFoodIds: ref.watch(
                          restaurantDetailControllerProvider
                              .select((final state) => state.fromFoodIds),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
