import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/old_price_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/quantity_control_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';

/// 价格和数量控制行组件
class PriceQuantityRowWidget extends ConsumerWidget {
  final dynamic price;
  final int foodCount;
  final int? foodId;
  final Food? food;
  final bool relationsExists;
  final double fontSize;
  final bool? isResting;
  final Function(int, int?, Food?, bool, [BuildContext?])? onAdd;
  final Function(int?)? onRemove;
  final Function(Food)? onAddMinCount;
  final Function(Food)? onSpecSelect;

  const PriceQuantityRowWidget({
    super.key,
    this.price,
    required this.foodCount,
    this.foodId,
    this.food,
    this.relationsExists = false,
    required this.fontSize,
    this.isResting,
    this.onAdd,
    this.onRemove,
    this.onAddMinCount,
    this.onSpecSelect,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 精确匹配小程序的价格显示逻辑
            Row(
              children: [
                // 当前价格显示
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '¥',
                        style: TextStyle(
                          fontSize: fontSize - 4,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      TextSpan(
                        text: getCurrentPrice(food, ref),
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
                // 显示原价（按小程序的条件显示原价）
                OldPriceWidget(food: food, fontSize: fontSize - 4),
              ],
            ),
          ],
        ),
        QuantityControlWidget(
          foodCount: foodCount,
          foodId: foodId,
          food: food,
          relationsExists: relationsExists,
          isResting: isResting,
          onAdd: onAdd,
          onRemove: onRemove,
          onAddMinCount: onAddMinCount,
          onSpecSelect: onSpecSelect,
        ),
      ],
    );
  }

  /// 获取当前显示价格
  static String getCurrentPrice(final Food? food, final WidgetRef ref) {
    if (food == null) return '0';

    // 获取购物车中该商品的数量
    final cartItems = ref.watch(shoppingCartProvider);
    final cartItem = cartItems.firstWhere(
      (item) => item.id == food.id,
      orElse: () => SelectFoodItem(count: 0),
    );
    final currentCount = cartItem.count ?? 0;

    // 优先级顺序：秒杀 -> 优惠 -> 多份打折 -> 原价

    // 1. 秒杀价格 - 最高优先级
    if (food.seckillActive == 1 &&
        food.seckillPrice != null &&
        food.seckillMaxOrderCount != null) {
      // 如果当前数量未超出秒杀限制，显示秒杀价格
      if (currentCount < food.seckillMaxOrderCount!) {
        return food.seckillPrice.toString();
      }
      // 超出秒杀限制，继续检查其他价格
    }

    // 2. 优惠价格 - 第二优先级
    if (food.prefrentialPrice != null &&
        food.maxOrderCount != null &&
        food.maxOrderCount! > 0) {
      // 计算秒杀已占用的数量
      final seckillUsedCount =
          (food.seckillActive == 1 && food.seckillMaxOrderCount != null)
              ? food.seckillMaxOrderCount!
              : 0;

      // 如果当前数量在优惠范围内（考虑秒杀已占用数量），显示优惠价格
      if (currentCount < seckillUsedCount + food.maxOrderCount!) {
        return food.prefrentialPrice.toString();
      }
      // 超出优惠限制，继续检查其他价格
    }

    // 3. 多份打折价格 - 第三优先级
    if (food.multiDiscountId != null && food.multiDiscountSteps != null) {
      // 优先使用购物车中的实时多份打折价格
      if (cartItem.multiDiscountAdvancePrice != null) {
        return cartItem.multiDiscountAdvancePrice.toString();
      }

      // 如果购物车中没有，使用原始的多份打折价格
      if (food.multiDiscountAdvancePrice != null) {
        return food.multiDiscountAdvancePrice.toString();
      }
    }

    // 4. 原价 - 默认价格
    return food.price?.toString() ?? '0';
  }
}
