import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 多份打折标签组件
class MultiDiscountTagWidget extends StatelessWidget {
  final Food? foodItem;

  const MultiDiscountTagWidget({
    super.key,
    required this.foodItem,
  });

  @override
  Widget build(BuildContext context) {
    // 检查是否有多份打折
    if (foodItem?.multiDiscountId == null || foodItem?.multiDiscountId == 0) {
      return SizedBox.shrink();
    }

    final textDirection = Directionality.of(context);
    final bool isRTL = textDirection == TextDirection.rtl;

    return Positioned(
      top: 0,
      right: isRTL ? null : 0,
      left: isRTL ? 0 : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 9.w, vertical: 6.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: isRTL ? Radius.circular(11.r) : Radius.zero,
            topRight: isRTL ? Radius.zero : Radius.circular(11.r),
            bottomLeft: isRTL ? Radius.zero : Radius.circular(11.r),
            bottomRight: isRTL ? Radius.circular(11.r) : Radius.zero,
          ),
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Color(0xFFFF6D2C),
              Color(0xFFFFAE4C),
            ],
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/images/white-multiple.png',
              width: 11.5.w,
              height: 11.5.h,
              color: Colors.white,
            ),
          ],
        ),
      ),
    );
  }
}
