import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';

/// 原价显示组件
class OldPriceWidget extends ConsumerWidget {
  final Food? food;
  final double fontSize;

  const OldPriceWidget({
    super.key,
    required this.food,
    required this.fontSize,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    if (food == null) return SizedBox.shrink();

    // 判断是否需要显示原价
    bool shouldShowOldPrice = false;
    String oldPriceText = '';

    // 多份打折情况：从购物车获取实时的多份打折价格
    if (food!.multiDiscountId != null && food!.multiDiscountId != 0) {
      final cartItems = ref.watch(shoppingCartProvider);
      final cartItem = cartItems.firstWhere(
        (final item) => item.id == food!.id,
        orElse: () => SelectFoodItem(count: 0),
      );

      // 使用购物车中的实时多份打折价格进行比较
      final currentMultiDiscountPrice =
          cartItem.multiDiscountAdvancePrice ?? food!.multiDiscountAdvancePrice;

      if (currentMultiDiscountPrice != null &&
          currentMultiDiscountPrice != food!.price) {
        shouldShowOldPrice = true;
        oldPriceText = food!.price?.toString() ?? '';
      }
    }
    // 普通情况：如果原价不等于现价
    else if (food!.originPrice != null && food!.originPrice != food!.price) {
      shouldShowOldPrice = true;
      oldPriceText = food!.originPrice?.toString() ?? '';
    }

    if (!shouldShowOldPrice || oldPriceText.isEmpty) {
      return SizedBox.shrink();
    }

    if (food!.price == num.parse(oldPriceText)) {
      return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(left: 5.w, right: 5.w),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: '¥',
              style: TextStyle(
                fontSize: fontSize - 4,
                color: Color(0xFF8D8C8C),
              ),
            ),
            TextSpan(
              text: oldPriceText,
              style: TextStyle(
                fontSize: fontSize,
                color: Color(0xFF8D8C8C),
                decoration: TextDecoration.lineThrough,
                decorationColor: Color(0xFF8D8C8C),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
