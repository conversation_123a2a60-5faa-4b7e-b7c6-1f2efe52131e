import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 秒杀信息组件
class SeckillInfoWidget extends StatelessWidget {
  final Food? foodItem;
  final String hour;
  final String minute;
  final String second;
  final bool isComboFood;

  ///
  const SeckillInfoWidget({
    super.key,
    required this.foodItem,
    required this.hour,
    required this.minute,
    required this.second,
    required this.isComboFood,
  });

  @override
  Widget build(final BuildContext context) {
    // 检查是否是秒杀商品且未开始
    if (foodItem == null ||
        foodItem!.seckillId == null ||
        foodItem!.seckillId! <= 0 ||
        foodItem!.seckillActive != 0) {
      return SizedBox.shrink();
    }

    final textDirection = Directionality.of(context);
    final bool isRTL = textDirection == TextDirection.rtl;

    // 构建秒杀区域，使用与小程序相似的样式
    return Container(
      decoration: !isComboFood
          ? null
          : BoxDecoration(
              color: const Color(0xFFDEFFD8),
              borderRadius: BorderRadius.circular(5.r),
            ),
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
      margin: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 离开始还剩
          Text(
            S.current.seckill_list_time,
            style: TextStyle(
              fontSize: 14.sp,
              color: isComboFood ? AppColors.primary : Colors.black87,
            ),
          ),
          // 显示秒杀剩余时间
          Text(
            '$hour:$minute:$second',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),

          Text(
            S.current.seckill_list_time_finish,
            style: TextStyle(
              fontSize: 14.sp,
              color: isComboFood ? AppColors.primary : Colors.black87,
            ),
          ),
          // 分隔符和活动价格应该在同一行
          Text(isRTL ? "،" : ","),
          // 活动价格
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  S.current.seckill_list_time_price,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isComboFood ? AppColors.primary : Colors.black87,
                  ),
                ),
                Text(
                  '¥${foodItem!.seckillPrice ?? 0}',
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
