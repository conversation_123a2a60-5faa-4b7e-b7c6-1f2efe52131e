import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 最小购买数量按钮组件
class MinCountButtonWidget extends StatelessWidget {
  final Food? food;
  final int foodCount;
  final bool? isResting;
  final VoidCallback? onTap;

  const MinCountButtonWidget({
    super.key,
    required this.food,
    required this.foodCount,
    this.isResting,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // 确认是否需要显示最小购买数量按钮
    // 条件：商品状态正常(state=1) 且 餐厅没有休息 且 最小购买数量>1 且 当前数量为0
    final bool isRestingState = isResting ?? false;
    if (food == null ||
        food!.state != 1 ||
        isRestingState ||
        (food!.minCount ?? 1) <= 1 ||
        foodCount > 0) {
      return SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 5.w),
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 0),
        height: 22.h,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(25.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/images/restaurant/minCount.png',
              width: 17.w,
              height: 17.h,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 2.w),
            Text(
              '${food!.minCount}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 2.w),
            Text(
              S.current.min_count,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
