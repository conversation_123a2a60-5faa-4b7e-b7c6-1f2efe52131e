import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';

/// 美食满减标签组件
/// 用于在美食item下方显示满减活动标签
class ReductionTagsWidget extends StatefulWidget {
  /// 美食数据
  final Food? foodItem;

  /// 构造函数
  const ReductionTagsWidget({
    super.key,
    this.foodItem,
  });

  @override
  State<ReductionTagsWidget> createState() => _ReductionTagsWidgetState();
}

class _ReductionTagsWidgetState extends State<ReductionTagsWidget> {
  /// 是否展开显示所有标签
  bool isExpanded = false;

  @override
  Widget build(final BuildContext context) {
    final reductionTags = widget.foodItem?.reductionFoodsTags;

    // 如果没有满减标签，不显示组件
    if (reductionTags == null || reductionTags.isEmpty) {
      return const SizedBox.shrink();
    }

    // 转换为 ReductionFoodsTag 列表
    final tags = reductionTags
        .map((final tag) =>
            ReductionFoodsTag.fromJson(tag as Map<String, dynamic>))
        .toList();

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        left: 10.w,
        right: 10.w,
        top: widget.foodItem?.seckillId != null ? 5.h : 8.h,
        bottom: 5.h,
      ),
      child: LayoutBuilder(
        builder: (final context, final constraints) {
          return _buildTagsLayout(tags, constraints.maxWidth);
        },
      ),
    );
  }

  /// 构建单行布局（收起状态）
  Widget _buildSingleRowLayout(
      final List<ReductionFoodsTag> tags, final double maxWidth) {
    // 展开按钮的实际宽度（增加安全边距）
    final buttonTotalWidth = 16.w + 8.w + 4.w; // 按钮 + 间距 + 安全边距

    // 计算所有标签的总宽度
    final totalTagsWidth = _calculateTotalTagsWidth(tags);

    // 检查是否需要展开按钮（总宽度超出容器）
    final needsExpandButton = totalTagsWidth > maxWidth - buttonTotalWidth;

    return Row(
      children: [
        // 标签滚动区域 - 使用 Expanded 防止溢出
        Expanded(
          child: SizedBox(
            height: 20.h, // 设置固定高度
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              physics: const ClampingScrollPhysics(),
              itemCount: tags.length,
              separatorBuilder: (final context, final index) =>
                  SizedBox(width: 8.w), // 标签间距
              itemBuilder: (final context, final index) {
                final tag = tags[index];
                return _buildReductionTag(tag);
              },
            ),
          ),
        ),

        // 展开按钮（独立显示）
        if (needsExpandButton) ...[
          SizedBox(width: 8.w), // 间距
          _buildToggleButton(),
        ],
      ],
    );
  }

  /// 计算所有标签的总宽度
  double _calculateTotalTagsWidth(final List<ReductionFoodsTag> tags) {
    double totalWidth = 0;

    for (int i = 0; i < tags.length; i++) {
      final tagWidth = _calculateTagWidth(tags[i]);
      final spacingWidth = i > 0 ? 8.w : 0; // 标签间距
      totalWidth += spacingWidth + tagWidth;
    }

    return totalWidth;
  }

  /// 构建标签布局
  Widget _buildTagsLayout(
      final List<ReductionFoodsTag> tags, final double maxWidth) {
    if (isExpanded) {
      // 展开状态：显示所有标签，收起按钮在右边
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标签区域 - 使用Wrap自动换行
          Expanded(
            child: Wrap(
              spacing: 8.w,
              runSpacing: 4.h,
              children:
                  tags.map((final tag) => _buildReductionTag(tag)).toList(),
            ),
          ),

          // 收起按钮在右边
          SizedBox(width: 8.w), // 间距
          _buildToggleButton(),
        ],
      );
    } else {
      // 收起状态：横向滚动显示
      return _buildSingleRowLayout(tags, maxWidth);
    }
  }

  /// 计算标签宽度
  double _calculateTagWidth(final ReductionFoodsTag tag) {
    final text = tag.title ?? '';
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    // 标签宽度 = 文本宽度 + 左右内边距
    return textPainter.size.width + (6.w * 2);
  }

  /// 构建满减标签
  Widget _buildReductionTag(final ReductionFoodsTag tag) {
    // 解析颜色，提供默认值
    final backgroundColor =
        tag.backgroundColor != null && tag.backgroundColor!.isNotEmpty
            ? Color(int.parse(tag.backgroundColor!.replaceFirst('#', '0xff')))
            : Color(0xFFFF6B35);

    final borderColor = tag.borderColor != null && tag.borderColor!.isNotEmpty
        ? Color(int.parse(tag.borderColor!.replaceFirst('#', '0xff')))
        : backgroundColor;

    final textColor = tag.color != null && tag.color!.isNotEmpty
        ? Color(int.parse(tag.color!.replaceFirst('#', '0xff')))
        : Colors.white;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: borderColor, width: 0.5),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        tag.title ?? '',
        style: TextStyle(
          color: textColor,
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建展开/收起按钮
  Widget _buildToggleButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isExpanded = !isExpanded;
        });
      },
      child: Container(
        width: 22.w,
        height: 20.h,
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        child: Icon(
          isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
          size: 22.sp,
          color: Colors.black,
        ),
      ),
    );
  }
}
