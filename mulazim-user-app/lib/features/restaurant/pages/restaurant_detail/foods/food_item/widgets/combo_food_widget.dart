import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/old_price_widget.dart';

import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/quantity_control_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/sales_quantity_info_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 美食套餐组件
/// 显示套餐的整体信息和包含的美食列表
/// 对应微信小程序中的<!-- 美食套餐 -->部分
class ComboFoodWidget extends StatelessWidget {
  /// 套餐美食数据
  final Food foodItem;

  /// 餐厅页面样式
  final int? style;

  /// 是否休息
  final bool? isResting;

  /// 套餐美食数量
  final int foodCount;

  /// 点击事件
  final VoidCallback? onTap;

  /// 添加事件
  final Function(int foodCount, int? foodId, Food? food, bool relationsExists,
      [BuildContext? btnContext])? onAdd;

  /// 移除事件
  final Function(int? foodId)? onRemove;

  /// 最小数量添加事件
  final Function(Food food)? onAddMinCount;

  /// 规格选择事件
  final Function(Food food)? onSpecSelect;

  const ComboFoodWidget({
    super.key,
    required this.foodItem,
    required this.style,
    required this.isResting,
    required this.foodCount,
    this.onTap,
    this.onAdd,
    this.onRemove,
    this.onAddMinCount,
    this.onSpecSelect,
  });

  @override
  Widget build(final BuildContext context) {
    // 套餐商品必须有foodType为2且有comboFoodItems
    if (foodItem.foodType != 2 ||
        foodItem.comboFoodItems == null ||
        foodItem.comboFoodItems!.isEmpty) {
      return SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 10.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 套餐标题
            _buildComboTitle(),
            SizedBox(height: 8.h),

            Divider(
              height: 1.h,
              color: Color(0xffE5E5E5),
            ),
            SizedBox(height: 8.h),
            // 套餐美食横向滚动列表
            _buildComboFoodList(),
            SizedBox(height: 12.h),
            // 套餐底部信息（价格、销量、操作按钮）
            _buildComboFooter(context),
          ],
        ),
      ),
    );
  }

  /// 构建套餐标题
  Widget _buildComboTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Text(
        foodItem.name ?? '',
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
          color: Color(0xff333333),
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建套餐美食横向滚动列表
  Widget _buildComboFoodList() {
    return SizedBox(
      height: 100.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: foodItem.comboFoodItems!.length > 10
            ? 10
            : foodItem.comboFoodItems!.length,
        itemBuilder: (final context, final index) {
          final comboFoodItem = foodItem.comboFoodItems![index];
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            width: 80.w,
            child: _buildComboFoodItemCard(comboFoodItem),
          );
        },
      ),
    );
  }

  /// 构建单个套餐美食卡片
  Widget _buildComboFoodItemCard(final ComboFoodItem comboFoodItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 美食图片
        Container(
          width: 70.w,
          height: 70.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6.r),
            child: CachedNetworkImage(
              imageUrl: comboFoodItem.restaurantFood?.image ?? '',
              fit: BoxFit.cover,
              placeholder: (final context, final url) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.image,
                  color: Colors.grey[400],
                  size: 20.sp,
                ),
              ),
              errorWidget: (final context, final url, final error) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.broken_image,
                  color: Colors.grey[400],
                  size: 20.sp,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 4.h),
        // 美食名称
        Expanded(
          child: Text(
            comboFoodItem.restaurantFood?.name ?? '',
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xff333333),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ),
        Row(
          children: [
            Text(
              S.current.count,
              style: TextStyle(
                fontSize: 12.sp,
                color: Color(0xff8D8C8C),
              ),
            ),
            SizedBox(width: 6.w),

            // 数量信息
            Text(
              '${comboFoodItem.count}x',
              style: TextStyle(
                fontSize: 12.sp,
                color: Color(0xff8D8C8C),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建套餐底部信息
  Widget _buildComboFooter(final BuildContext context) {
    return Row(
      children: [
        // 左侧：价格和销量信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 价格信息
              _buildPriceInfo(),
              SizedBox(height: 4.h),
              // 销量和限购信息
              SalesQuantityInfoWidget(foodItem: foodItem),
            ],
          ),
        ),
        // 右侧：操作按钮
        _buildActionButton(context),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo() {
    final currentPrice = (foodItem.prefrentialPrice != null &&
            (foodItem.prefrentialCount ?? 0) > 0)
        ? foodItem.prefrentialPrice
        : foodItem.price;

    return Row(
      children: [
        // 当前价格
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '¥',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xffFF4348),
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextSpan(
                text: '${currentPrice ?? 0}',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: Color(0xffFF4348),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        // 原价
        OldPriceWidget(food: foodItem, fontSize: 16.sp),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(final BuildContext context) {
    return QuantityControlWidget(
      food: foodItem,
      foodId: foodItem.id,
      foodCount: foodCount,
      relationsExists: false,
      isResting: isResting ?? false,
      onAdd: onAdd,
      onRemove: onRemove,
      onAddMinCount: onAddMinCount,
      onSpecSelect: onSpecSelect,
    );
  }
}
