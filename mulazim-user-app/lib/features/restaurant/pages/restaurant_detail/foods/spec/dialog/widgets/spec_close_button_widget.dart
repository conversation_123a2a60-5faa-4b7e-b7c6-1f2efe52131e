import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 规格弹窗关闭按钮组件
class SpecCloseButtonWidget extends StatelessWidget {
  /// 关闭回调
  final VoidCallback? onClose;

  const SpecCloseButtonWidget({
    super.key,
    this.onClose,
  });

  @override
  Widget build(final BuildContext context) {
    return GestureDetector(
      onTap: onClose,
      child: Container(
        margin: EdgeInsets.only(top: 10.h),
        width: 30.w,
        height: 30.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30.r),
          border: Border.all(
            color: Colors.white,
            width: 1.5,
          ),
        ),
        child: Icon(
          Icons.close,
          size: 24.w,
          color: Colors.white,
        ),
      ),
    );
  }
}
