import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/widgets/spec_group_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 规格内容组件
class SpecContentWidget extends ConsumerWidget {
  /// 构造函数
  const SpecContentWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听相关状态
    final errorMessage = ref.watch(
      specModalControllerProvider.select((final state) => state.errorMessage),
    );
    final specGroups = ref.watch(
      specModalControllerProvider.select((final state) => state.specGroups),
    );

    // 错误状态
    if (errorMessage != null) {
      return _buildErrorState(errorMessage);
    }

    // 空状态
    if (specGroups.isEmpty) {
      return _buildEmptyState();
    }

    // 正常内容
    return _buildNormalContent(specGroups);
  }

  /// 构建错误状态
  Widget _buildErrorState(final String errorMessage) {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Text(
          errorMessage,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Text(
          S.current.no_spec_data,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 构建正常内容
  Widget _buildNormalContent(final specGroups) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(10.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 规格组列表
          ...specGroups.asMap().entries.map(
                (final entry) => SpecGroupWidget(
                  group: entry.value,
                  index: entry.key,
                ),
              ),
        ],
      ),
    );
  }
}
