import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/widgets/index.dart';

/// 规格选择弹窗组件
class SpecModalWidget extends ConsumerStatefulWidget {
  /// 商品信息
  final Food? foodItem;

  /// 餐厅ID
  final int? restaurantId;

  /// 是否休息中
  final bool? isResting;

  /// 关闭回调
  final VoidCallback? onClose;

  const SpecModalWidget({
    super.key,
    this.foodItem,
    this.restaurantId,
    this.isResting,
    this.onClose,
  });

  @override
  ConsumerState<SpecModalWidget> createState() => _SpecModalWidgetState();
}

class _SpecModalWidgetState extends ConsumerState<SpecModalWidget> {
  @override
  void initState() {
    super.initState();
    // 延迟初始化，避免在widget构建期间修改provider
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      _initializeData();
    });
  }

  /// 初始化数据
  void _initializeData() async {
    final controller = ref.read(specModalControllerProvider.notifier);

    // 使用控制器中的方法获取规格数据
    await controller.initSpecsFromApi(widget.foodItem);

    // 检查是否有错误或没有规格数据
    final state = ref.read(specModalControllerProvider);
    if (state.errorMessage != null || state.specGroups.isEmpty) {
      // 如果有错误或没有规格数据，关闭弹窗
      if (mounted && widget.onClose != null) {
        widget.onClose!();
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    final isLoading = ref.watch(
      specModalControllerProvider.select((final state) => state.isLoading),
    );

    // 检查是否有规格数据
    final hasSpecGroups = ref.watch(
      specModalControllerProvider
          .select((final state) => state.specGroups.isNotEmpty),
    );

    if (isLoading || !hasSpecGroups) {
      return const LoadingWidget();
    }
    // 检查当前语言是否需要RTL布局
    final isRTL = ref.read(languageProvider) == 'ug';

    return Directionality(
      textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 弹窗容器
          SpecDialogContainerWidget(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部
                SpecHeaderWidget(foodItem: widget.foodItem),

                Divider(
                  color: AppColors.dividerColor,
                  height: 1.h,
                ),

                // 规格内容
                const Flexible(child: SpecContentWidget()),

                // 底部操作区
                SpecFooterWidget(
                  foodItem: widget.foodItem,
                  onClose: widget.onClose,
                ),
              ],
            ),
          ),

          // 关闭按钮
          SpecCloseButtonWidget(onClose: widget.onClose),
        ],
      ),
    );
  }
}
