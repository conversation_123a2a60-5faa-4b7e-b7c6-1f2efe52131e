import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/toast_widget.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_state.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/services/spec_service.dart';
import 'package:user_app/generated/l10n.dart';

/// 规格选择弹窗控制器
class SpecModalController extends StateNotifier<SpecModalState> {
  final Ref _ref;

  /// 缓存的规格数据，用于价格计算和活动匹配
  FoodWithSpec? _cachedSpecData;

  ///
  SpecModalController(this._ref) : super(const SpecModalState()) {
    // 初始化显示状态 - 与微信小程序attached生命周期对应
    _initShow();
  }

  /// 初始化显示状态 - 与微信小程序attached逻辑一致
  void _initShow() {
    state = state.copyWith(show: true);
  }

  /// 从API获取规格数据并初始化 - 与微信小程序initSpecs逻辑一致
  Future<void> initSpecsFromApi(final Food? foodItem) async {
    state = state.copyWith(foodItem: foodItem, isLoading: true);
    _cachedSpecData =
        await _ref.read(specServiceProvider).getFoodSpecData(foodItem!.id!);
    if (_cachedSpecData?.specs?.isNotEmpty ?? false) {
      state = state.copyWith(foodItem: foodItem);
      if (state.foodItem != null) {
        initSpecs(_cachedSpecData!.specs!, state.foodItem!);
      }
    } else {
      state = state.copyWith(
        errorMessage: '规格数据为空',
        isLoading: false,
      );
    }
  }

  /// 初始化规格选择状态 - 与微信小程序initSpecs逻辑一致
  void initSpecs(final List<SpecGroup> specGroups, final Food foodItem) {
    // 获取购物车中的规格信息并合并到foodItem中 - 与微信小程序onSpecInfoHandler逻辑一致
    final cartItems = _ref.read(shoppingCartProvider);

    // 查找购物车中最新的规格商品信息（倒序查找，获取最新添加的规格）
    final matchedItems =
        cartItems.where((final item) => item.id == foodItem.id).toList();
    final fallbackItem = matchedItems.isNotEmpty ? matchedItems.last : null;

    // 将购物车信息合并到foodItem中 - 与微信小程序dataList逻辑一致
    final foodItemWithCartInfo = foodItem.copyWith(
      specUniqueId: fallbackItem?.specUniqueId ?? "",
      specSelectedOptions: fallbackItem?.specSelectedOptions ?? [],
    );

    // 更新state中的foodItem为合并后的版本
    state = state.copyWith(foodItem: foodItemWithCartInfo);

    // 查找匹配的购物车项目 - 与微信小程序_findMatchedCartItem逻辑一致
    final cartItem = _findMatchedCartItem(
      foodItemWithCartInfo.id,
      foodItemWithCartInfo.specUniqueId,
    );

    final selectedSpecs = <int, int>{};
    final selectedOptions = <SelectedSpecOption>[];
    double additionalPrice = 0.0; // 只计算规格价格，不包含foodItem原价

    // 遍历规格数据，初始化选中的规格 - 与微信小程序forEach逻辑一致
    for (final group in specGroups) {
      if (group.specOptions?.isEmpty ?? true) continue;

      // 查找匹配的规格选项 - 与微信小程序逻辑完全一致
      SpecOption? selectedOption;

      // 第一步：优先从购物车项目中查找匹配的规格 - 与微信小程序matched逻辑一致
      if (cartItem != null && cartItem.specSelectedOptions != null) {
        // 从购物车项目中查找匹配的规格
        final matched = cartItem.specSelectedOptions!.firstWhere(
          (final spec) => spec['spec_type_id'] == group.id,
          orElse: () => <String, Object>{},
        );
        if (matched.isNotEmpty && matched['spec_option_id'] != null) {
          selectedOption = group.specOptions!.firstWhere(
            (final opt) =>
                opt.id == matched['spec_option_id'] && opt.state == 1,
            orElse: () => SpecOption(),
          );
        }
      }

      // 第二步：如果购物车中没有匹配的规格，检查foodItem中的spec_option_ids - 与微信小程序specIsSelected逻辑一致
      if (selectedOption?.id == null &&
          foodItemWithCartInfo.specOptionIds != null &&
          foodItemWithCartInfo.specOptionIds!.isNotEmpty) {
        // 查找在foodItem.spec_option_ids中且状态为可用的选项
        selectedOption = group.specOptions!.firstWhere(
          (final opt) =>
              foodItemWithCartInfo.specOptionIds!.contains(opt.id) &&
              opt.state == 1,
          orElse: () => SpecOption(),
        );
      }

      // 第三步：如果前两步都没找到，使用默认选择逻辑 - 与微信小程序逻辑一致
      if (selectedOption?.id == null) {
        // 逻辑与微信小程序完全一致：
        // 1. 优先选择 is_selected == 1 && state == 1 的选项
        // 2. 如果没有，则选择第一个 state == 1 的选项
        selectedOption = group.specOptions!.firstWhere(
          (final opt) => opt.isSelected == true && opt.state == 1,
          orElse: () => group.specOptions!.firstWhere(
            (final opt) => opt.state == 1,
            orElse: () => SpecOption(),
          ),
        );
      }

      // 如果选中了规格选项，添加到选择列表中 - 与微信小程序逻辑一致
      if (selectedOption?.id != null) {
        selectedSpecs[group.id!] = selectedOption!.id!;
        additionalPrice += selectedOption.price ?? 0.0;
        selectedOptions.add(
          SelectedSpecOption(
            name: selectedOption.name ?? '',
            price: (selectedOption.price ?? 0.0),
            specTypeId: group.id!,
            specOptionId: selectedOption.id!,
          ),
        );
      }
    }

    // 设置数量，考虑最小购买数量 - 与微信小程序逻辑一致
    int count = cartItem?.count ?? 0;

    // 计算促销价格 - 关键修复：在设置基础状态之前先计算促销信息
    final promoInfo =
        _calculatePromotionPrice(foodItemWithCartInfo, selectedSpecs);

    // 计算最终价格 - 只传入规格价格，不包含foodItem原价
    final priceResult = _calculateFinalPrice(count, additionalPrice, promoInfo);
    _buildSpecFoodItem(foodItem);
    // 更新状态，包含originalPrice和show状态
    state = state.copyWith(
      specGroups: specGroups,
      foodItem: foodItemWithCartInfo,
      selectedSpecs: selectedSpecs,
      selectedOptions: selectedOptions,
      additionalPrice: additionalPrice,
      originalPrice: additionalPrice, // 与微信小程序originalPrice对应，只是规格价格
      count: count,
      totalPrice: priceResult.finalPrice,
      currentPrice: priceResult.currentPrice,
      minCount: foodItemWithCartInfo.minCount ?? 1,
      show: true, // 显示弹窗
      isLoading: false,
      // 促销信息 - 只有找到匹配活动时才设置
      seckillInfo: promoInfo.seckillInfo,
      prefInfo: promoInfo.prefInfo,
      marketInfo: promoInfo.marketInfo,
      hasPreferential: promoInfo.prefInfo != null,
      hasSeckill: promoInfo.seckillInfo != null,
      maxOrderCount: promoInfo.prefInfo?.maxOrderCount ?? 0,
      seckillMaxOrderCount: promoInfo.seckillInfo?.userMaxOrderCount ?? 0,
    );
  }

  /// 选择规格选项 - 与微信小程序selectOption逻辑完全一致
  void selectOption(final int groupId, final int optionId) {
    final group = state.specGroups.firstWhere(
      (final g) => g.id == groupId,
      orElse: () => SpecGroup(),
    );

    final option = group.specOptions?.firstWhere(
      (final opt) => opt.id == optionId,
      orElse: () => SpecOption(),
    );

    if (option?.id == null || option!.state != 1) return;

    // 更新选中的规格
    final selectedSpecs = Map<int, int>.from(state.selectedSpecs);
    selectedSpecs[groupId] = optionId;

    // 计算价格和选项
    final priceAndOptions = _calculatePriceAndOptions(selectedSpecs);

    // 查找匹配的购物车项目
    final cartItem = _findMatchedCartItemBySpecs(selectedSpecs);
    final count = cartItem?.count ?? 0;

    // 计算促销价格
    final promoInfo = _calculatePromotionPrice(state.foodItem, selectedSpecs);

    // 计算最终价格（传入基础价格 = 商品原价 + 规格附加价格）
    final priceResult =
        _calculateFinalPrice(count, priceAndOptions.additionalPrice, promoInfo);

    // 创建要更新的状态对象 - 与微信小程序逻辑一致
    state = state.copyWith(
      selectedSpecs: selectedSpecs,
      selectedOptions: priceAndOptions.selectedOptions,
      additionalPrice: priceAndOptions.additionalPrice,
      originalPrice: formatPrice(priceAndOptions.additionalPrice), // 格式化价格
      count: count,
      totalPrice: formatPrice(priceResult.finalPrice), // 格式化价格
      currentPrice: formatPrice(priceResult.currentPrice), // 格式化价格
      // 促销信息 - 只有找到匹配活动时才设置
      seckillInfo: promoInfo.seckillInfo,
      prefInfo: promoInfo.prefInfo,
      marketInfo: promoInfo.marketInfo,
      hasPreferential: promoInfo.prefInfo != null,
      hasSeckill: promoInfo.seckillInfo != null,
      maxOrderCount: promoInfo.prefInfo?.maxOrderCount ?? 0,
      seckillMaxOrderCount: promoInfo.seckillInfo?.userMaxOrderCount ?? 0,
    );

    // 调用_buildSpecFoodItem - 与微信小程序逻辑一致
    // 微信小程序中这个方法会通过setData更新促销相关状态，但不保存返回值
    _buildSpecFoodItem(state.foodItem!);
  }

  /// 更新数量 - 与微信小程序_updateCount逻辑完全一致
  /// [newCount] 新数量
  /// [eventName] 事件名称（用于区分加减操作）
  void _updateCount(final int newCount, [final String? eventName]) {
    // 计算价格和选项
    final priceAndOptions = _calculatePriceAndOptions(state.selectedSpecs);

    // 计算促销价格
    final promoInfo =
        _calculatePromotionPrice(state.foodItem, state.selectedSpecs);

    // 计算最终价格（传入基础价格 = 商品原价 + 规格附加价格）
    final priceResult = _calculateFinalPrice(
      newCount,
      priceAndOptions.additionalPrice,
      promoInfo,
    );

    // 最小购买数量处理 - 与微信小程序逻辑一致
    // 确保当前数量不低于最小购买数量，但在减少数量时允许直接归零
    int finalCount = newCount;
    if (eventName == "specAdd" && newCount > 0 && newCount < state.minCount) {
      finalCount = state.minCount;
    }

    // 检查秒杀和优惠限购提示 - 与微信小程序逻辑一致
    if (eventName == "specAdd") {
      _checkLimitPurchaseWarnings(finalCount);
    }

    // 创建要更新的状态对象 - 与微信小程序逻辑一致
    state = state.copyWith(
      count: finalCount,
      additionalPrice: priceAndOptions.additionalPrice,
      originalPrice: formatPrice(priceAndOptions.additionalPrice), // 格式化价格
      totalPrice: formatPrice(priceResult.finalPrice), // 格式化价格
      currentPrice: formatPrice(priceResult.currentPrice), // 格式化价格
      // 促销信息 - 只有找到匹配活动时才设置
      seckillInfo: promoInfo.seckillInfo,
      prefInfo: promoInfo.prefInfo,
      marketInfo: promoInfo.marketInfo,
      hasPreferential: promoInfo.prefInfo != null,
      hasSeckill: promoInfo.seckillInfo != null,
      maxOrderCount: promoInfo.prefInfo?.maxOrderCount ?? 0,
      seckillMaxOrderCount: promoInfo.seckillInfo?.userMaxOrderCount ?? 0,
    );

    // 构建规格食品项目 - 与微信小程序逻辑一致
    // 微信小程序中这个方法会通过setData更新促销相关状态，并保存返回值用于triggerEvent
    _buildSpecFoodItem(state.foodItem!);
  }

  /// 检查限购警告 - 与微信小程序逻辑一致
  void _checkLimitPurchaseWarnings(final int count) {
    final maxOrderCount = state.maxOrderCount;
    final seckillMaxOrderCount = state.seckillMaxOrderCount;

    // 秒杀限购次数超购判断 - 与微信小程序逻辑一致
    if (state.seckillInfo != null && state.seckillInfo!.seckillActive == 1) {
      // 如果有优惠价且购买数量大于秒杀限购次数
      if (state.prefInfo != null) {
        // 当前购买数量等于秒杀最大限购次数时，触发秒杀提示
        if (seckillMaxOrderCount > 0 && seckillMaxOrderCount + 1 == count) {
          final seckillStatus = {'count': seckillMaxOrderCount, 'type': 1};
          showSeckillLimitToast(seckillStatus);
        }
        // 当前优惠价购买数量等于最大限购次数时，触发折扣限制提示
        else if (maxOrderCount > 0 &&
            maxOrderCount + seckillMaxOrderCount + 1 == count) {
          showDiscountExceedLimitToast(maxOrderCount);
        }
      } else {
        // 当前购买数量等于秒杀最大限购次数时，触发秒杀提示
        if (seckillMaxOrderCount > 0 && seckillMaxOrderCount + 1 == count) {
          final seckillStatus = {'count': seckillMaxOrderCount, 'type': 0};
          showSeckillLimitToast(seckillStatus);
        }
      }
    }

    // 优惠限购次数超购判断
    if ((state.seckillInfo == null || state.seckillInfo!.seckillActive == 0) &&
        maxOrderCount > 0 &&
        maxOrderCount + 1 == count) {
      showDiscountLimitToast(maxOrderCount);
    }
  }

  /// 计算价格和选项
  ({List<SelectedSpecOption> selectedOptions, double additionalPrice})
      _calculatePriceAndOptions(
    final Map<int, int> selectedSpecs,
  ) {
    double additionalPrice = 0.0;
    List<SelectedSpecOption> selectedOptions = [];

    for (final group in state.specGroups) {
      final optId = selectedSpecs[group.id];
      if (optId == null) continue;

      final option = group.specOptions?.firstWhere(
        (final opt) => opt.id == optId,
        orElse: () => SpecOption(),
      );

      if (option?.id != null && option!.state == 1) {
        additionalPrice += option.price ?? 0.0;
        selectedOptions.add(
          SelectedSpecOption(
            name: option.name ?? '',
            price: (option.price ?? 0.0),
            specTypeId: group.id!,
            specOptionId: option.id!,
          ),
        );
      }
    }

    return (selectedOptions: selectedOptions, additionalPrice: additionalPrice);
  }

  /// 计算促销价格 - 使用SpecService中的通用方法
  /// 返回促销信息，与微信小程序_calculatePromotionPrice逻辑一致
  ({SeckillInfo? seckillInfo, PrefInfo? prefInfo, MarketInfo? marketInfo})
      _calculatePromotionPrice(
    final Food? foodItem,
    final Map<int, int> selectedSpecs,
  ) {
    if (foodItem == null || _cachedSpecData == null) {
      return (seckillInfo: null, prefInfo: null, marketInfo: null);
    }

    final selectedOptionIds = selectedSpecs.values.toList();

    // 使用SpecService中的通用方法
    final specService = _ref.read(specServiceProvider);
    final result =
        specService.calculatePromotionPrice(_cachedSpecData, selectedOptionIds);

    return result;
  }

  /// 计算最终价格 - 使用SpecService中的通用方法
  /// 与微信小程序_calculateFinalPrice逻辑完全一致
  ({double finalPrice, int promotionCount, double currentPrice})
      _calculateFinalPrice(
    final int count,
    final double basePrice,
    final ({
      SeckillInfo? seckillInfo,
      PrefInfo? prefInfo,
      MarketInfo? marketInfo
    }) promoInfo,
  ) {
    // 使用SpecService中的通用方法
    final specService = _ref.read(specServiceProvider);
    return specService.calculateFinalPrice(count, basePrice, promoInfo);
  }

  /// 查找匹配的购物车项目 - 与微信小程序_findMatchedCartItem逻辑一致
  /// [foodId] 商品ID
  /// [uniqueId] 规格唯一标识符
  SelectFoodItem? _findMatchedCartItem(
    final int? foodId,
    final String? uniqueId,
  ) {
    if (foodId == null) return null;

    final cartItems = _ref.read(shoppingCartProvider);

    // 与微信小程序逻辑完全一致：item.id == foodId && item.spec_unique_id == uniqueId
    try {
      final result = cartItems.firstWhere(
        (final item) => item.id == foodId && item.specUniqueId == uniqueId,
      );
      return result;
    } catch (e) {
      return null; // 未找到匹配项时返回null
    }
  }

  /// 通过规格查找匹配的购物车项目 - 与微信小程序_findMatchedCartItemBySpecs逻辑一致
  SelectFoodItem? _findMatchedCartItemBySpecs(
    final Map<int, int> selectedSpecs,
  ) {
    final cartItems = _ref.read(shoppingCartProvider);

    try {
      return cartItems.firstWhere(
        (final item) {
          // 与微信小程序逻辑一致：检查商品ID和规格选项匹配
          if (item.id != state.foodItem?.id ||
              item.specSelectedOptions == null) {
            return false;
          }

          // 构建匹配映射 - 与微信小程序matchMap逻辑一致
          final matchMap = <String, bool>{};
          for (final option in item.specSelectedOptions!) {
            final key = '${option['spec_type_id']}_${option['spec_option_id']}';
            matchMap[key] = true;
          }

          // 检查所有选中的规格是否都在购物车项目中 - 与微信小程序every逻辑一致
          return selectedSpecs.entries.every((final entry) {
            final key = '${entry.key}_${entry.value}';
            return matchMap.containsKey(key);
          });
        },
      );
    } catch (e) {
      return null; // 未找到匹配项时返回null
    }
  }

  /// 构建规格商品项目 - 使用SpecService中的通用方法
  /// 与微信小程序_buildSpecFoodItem逻辑一致
  Food _buildSpecFoodItem(final Food foodItem) {
    // 使用SpecService中的通用方法
    final specService = _ref.read(specServiceProvider);
    final selectedOptionIds = state.selectedSpecs.values.toList();
    final currentPromoInfo = (
      seckillInfo: state.seckillInfo,
      prefInfo: state.prefInfo,
      marketInfo: state.marketInfo
    );

    final specFoodItem = specService.buildSpecFoodItem(
      foodItem: foodItem,
      selectedOptionIds: selectedOptionIds,
      selectedOptions: state.selectedOptions,
      additionalPrice: state.additionalPrice,
      count: state.count,
      promoInfo: currentPromoInfo,
      specData: _cachedSpecData, // 传递缓存的规格数据用于验证匹配
    );

    return specFoodItem;
  }

  /// 获取已选规格的显示文本
  String getSelectedSpecsText() {
    return state.selectedOptions
        .map((final opt) => opt.name)
        .where((final name) => name.isNotEmpty)
        .join(' | ');
  }

  /// 格式化价格 - 使用SpecService中的通用方法
  double formatPrice(final double val) {
    final specService = _ref.read(specServiceProvider);
    return specService.formatPrice(val);
  }

  /// 关闭弹窗
  void closeModal() {
    state = state.copyWith(show: false);
  }

  /// 增加数量 - 与微信小程序specAdd逻辑一致
  void addCount() {
    final count = state.count;
    final minCount = state.minCount;

    // 如果当前数量为0，则直接将数量设置为最小购买数量
    if (count == 0) {
      _updateCount(minCount, "specAdd");
    } else {
      final newCount = count + 1;
      _updateCount(newCount, "specAdd");
    }

    // 与微信小程序逻辑一致：数量变化后同步到购物车
    _syncToCart("specAdd");
  }

  /// 减少数量 - 与微信小程序specMinus逻辑一致
  void minusCount() {
    if (state.count <= 0) return;

    // 获取最小购买数量
    final minCount = state.minCount;
    // 如果当前数量等于最小购买数量，则直接置为0
    if (state.count <= minCount) {
      _updateCount(0, "specMinus");
    } else {
      final newCount = state.count - 1;
      _updateCount(newCount, "specMinus");
    }

    // 与微信小程序逻辑一致：数量变化后同步到购物车
    _syncToCart("specMinus");
  }

  /// 同步到购物车 - 与微信小程序onSpecAddHandler/onSpecMinusHandler逻辑一致
  void _syncToCart(final String eventType) {
    if (state.foodItem == null) return;

    // 构建规格商品项目
    final specFoodItem = _buildSpecFoodItem(state.foodItem!);
    final cartProvider = _ref.read(shoppingCartProvider.notifier);

    if (eventType == "specAdd") {
      cartProvider.addFoodToCart(foodItem: specFoodItem);
    } else if (eventType == "specMinus") {
      cartProvider.removeFoodCount(
        foodId: specFoodItem.id!,
        foodItem: specFoodItem,
      );
    }
  }

  /// 显示秒杀限购提示
  void showSeckillLimitToast(final Map<String, dynamic> detail) {
    // 根据类型确定使用哪个提示信息
    final String message;
    final int count = detail['count'] ?? 0;
    final int type = detail['type'] ?? 0;

    if (type == 1) {
      // 秒杀限购，超过部分恢复优惠价
      message = S.current.seckill_limit_promo_price
          .replaceAll('%s', count.toString());
    } else {
      // 秒杀限购，超过部分恢复原价
      message =
          S.current.seckill_limit_old_price.replaceAll('%s', count.toString());
    }

    ToastWidget.showRich(
      message,
      duration: 3000,
    );
  }

  /// 显示优惠限购提示 - 与微信小程序showDicountLimitToast逻辑一致
  void showDiscountLimitToast(final int maxCount) {
    // 使用国际化文本
    final message =
        S.current.seckill_limit_old_price.replaceAll('%s', maxCount.toString());

    ToastWidget.showRich(
      message,
      duration: 3000,
    );
  }

  /// 显示优惠限购提示
  void showDiscountExceedLimitToast(final int maxCount) {
    // 使用国际化文本
    final message = S.current.exceed_discount_num_tips
        .replaceAll('%s', maxCount.toString());

    ToastWidget.showRich(
      message,
      duration: 3000,
    );
  }
}

/// 规格选择弹窗控制器提供者
final specModalControllerProvider =
    StateNotifierProvider.autoDispose<SpecModalController, SpecModalState>(
  (final ref) => SpecModalController(ref),
);
