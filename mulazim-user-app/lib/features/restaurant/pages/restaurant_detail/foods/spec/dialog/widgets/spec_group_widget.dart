import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/restaurant/spec_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';

/// 规格组组件
class SpecGroupWidget extends ConsumerWidget {
  /// 规格组数据
  final SpecGroup group;

  /// 索引
  final int index;

  const SpecGroupWidget({
    super.key,
    required this.group,
    required this.index,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 只监听选中的规格状态
    final selectedSpecs = ref.watch(
      specModalControllerProvider.select((final state) => state.selectedSpecs),
    );

    return Container(
      margin: EdgeInsets.only(bottom: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            group.name ?? '',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.start,
          ),
          SizedBox(height: 12.h),
          if (group.specOptions != null)
            Wrap(
              spacing: 10.w,
              runSpacing: 10.h,
              alignment: WrapAlignment.start,
              children: group.specOptions!
                  .map(
                    (final option) => SpecOptionWidget(
                      groupId: group.id!,
                      priceType: index == 0 ? 1 : group.priceType ?? 0,
                      option: option,
                      isSelected: selectedSpecs[group.id] == option.id,
                    ),
                  )
                  .toList(),
            ),
        ],
      ),
    );
  }
}

/// 规格选项组件
class SpecOptionWidget extends ConsumerWidget {
  /// 规格组ID
  final int groupId;

  /// 规格选项
  final SpecOption option;

  /// 是否选中
  final bool isSelected;

  /// 价格类型
  final int priceType;

  /// 构造函数
  const SpecOptionWidget({
    super.key,
    required this.groupId,
    required this.option,
    required this.isSelected,
    required this.priceType,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final isDisabled = option.state != 1;

    return GestureDetector(
      onTap: isDisabled
          ? null
          : () => ref
              .read(specModalControllerProvider.notifier)
              .selectOption(groupId, option.id!),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isDisabled
              ? Colors.grey[200]
              : isSelected
                  ? Colors.green.withOpacity(0.1)
                  : Colors.grey[100],
          border: Border.all(
            color: isDisabled
                ? Colors.grey[300]!
                : isSelected
                    ? Colors.green
                    : Colors.grey[300]!,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              option.name ?? '',
              style: TextStyle(
                fontSize: 14.sp,
                color: isDisabled
                    ? Colors.grey[500]
                    : isSelected
                        ? Colors.green
                        : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (option.price != null && option.price! > 0) ...[
              Text(
                ' | ',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
              Text(
                FormatUtil.formatPrice(option.price!),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '¥',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                priceType == 0 ? ' +' : '',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
