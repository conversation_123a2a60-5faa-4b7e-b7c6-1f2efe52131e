import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 规格弹窗容器组件
class SpecDialogContainerWidget extends StatelessWidget {
  /// 子组件
  final Widget child;

  const SpecDialogContainerWidget({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度并减去insetPadding的左右边距
    final screenWidth = MediaQuery.of(context).size.width;
    final maxWidth = screenWidth - (20.w * 2); // insetPadding的左右边距各20.w

    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
      contentPadding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      content: SizedBox(
        width: maxWidth,
        child: child,
      ),
    );
  }
}
