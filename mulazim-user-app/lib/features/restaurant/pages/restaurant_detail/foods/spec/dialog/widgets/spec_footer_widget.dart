import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_state.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/widgets/selected_specs_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/min_count_button_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/cart_animation_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/food_count_widget.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 规格弹窗底部操作区组件 - 与微信小程序spec-footer布局一致
class SpecFooterWidget extends ConsumerWidget {
  /// 商品信息
  final Food? foodItem;

  /// 关闭回调
  final VoidCallback? onClose;

  const SpecFooterWidget({
    super.key,
    this.foodItem,
    this.onClose,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听状态
    final state = ref.watch(specModalControllerProvider);
    final controller = ref.read(specModalControllerProvider.notifier);

    return Container(
      padding:
          EdgeInsets.fromLTRB(12.w, 8.w, 12.w, 12.w), // 对应 16rpx 24rpx 24rpx
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFEFF1F6), // 对应 #eff1f6
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 已选规格显示 - 与微信小程序 spec-selected-options 对应
          const SelectedSpecsWidget(),

          // spec-body 主体部分
          Row(
            children: [
              // 价格容器（左侧）- 对应 total-price-container
              Expanded(
                child: _buildPriceContainer(state),
              ),

              // 操作按钮（右侧）- 对应各种按钮状态
              _buildActionButton(context, state, controller),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建价格容器 - 对应微信小程序 total-price-container
  Widget _buildPriceContainer(final SpecModalState state) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 8.w, // 16rpx / 2
      runSpacing: 4.h,
      children: [
        // 当前价格 - 对应 total-price
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              FormatUtil.formatPrice(state.currentPrice),
              style: TextStyle(
                fontSize: 31.sp, // 62rpx / 2
                fontFamily: AppConstants.numberFont,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
                height: 0.6,
              ),
            ),
            Text(
              '¥',
              style: TextStyle(
                fontSize: 18.sp, // 36rpx / 2 对应 total-price-unit
                fontWeight: FontWeight.bold,
                fontFamily: AppConstants.numberFont,
                color: AppColors.primary,
              ),
            ),
          ],
        ),

        // 价格和活动信息列 - 对应 price-and-max-count
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 原价显示 - 对应 old-total-price
            // 当原价不等于总价且有活动时才显示原价
            if (state.originalPrice != state.currentPrice &&
                state.originalPrice > 0 &&
                (state.hasPreferential || state.hasSeckill)) ...[
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    FormatUtil.formatPrice(state.originalPrice),
                    style: TextStyle(
                      fontSize: 15.sp, // 30rpx / 2
                      color: Color(0xFF8D8C8C),
                      decoration: TextDecoration.lineThrough,
                      decorationColor: Color(0xFF8D8C8C),
                    ),
                  ),
                  Text(
                    '¥',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: Color(0xFF8D8C8C),
                      decoration: TextDecoration.lineThrough,
                      decorationColor: Color(0xFF8D8C8C),
                    ),
                  ),
                ],
              ),
            ],

            // 活动限购信息 - 对应 max-count-info
            if (_shouldShowActivityInfo(state)) ...[
              SizedBox(height: 2.h),
              Text(
                _getActivityInfoText(state),
                style: TextStyle(
                  fontSize: 14.sp, // 28rpx / 2
                  color: Color(0xFF8D8C8C),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  /// 构建操作按钮 - 对应微信小程序的三种按钮状态
  Widget _buildActionButton(
    final BuildContext context,
    final SpecModalState state,
    final SpecModalController controller,
  ) {
    // 1. 最小购买数量按钮 - 使用通用的MinCountButtonWidget
    if (state.minCount > 1 && state.count == 0) {
      return MinCountButtonWidget(
        food: state.foodItem,
        foodCount: state.count,
        isResting: false, // 规格弹窗打开时说明不是休息状态
        onTap: controller.addCount,
      );
    }

    // 2. 普通添加按钮 - 对应 confirm-btn
    if (state.minCount == 1 && state.count == 0) {
      return _buildConfirmButton(context, controller);
    }

    // 3. 数量控制器 - 使用通用的数量控制样式
    return _buildQuantityControls(context, state, controller);
  }

  /// 确认按钮 - 对应微信小程序 confirm-btn
  Widget _buildConfirmButton(
    final BuildContext context,
    final SpecModalController controller,
  ) {
    return GestureDetector(
      onTap: () {
        _playAddToCartAnimation(context);
        controller.addCount();
      },
      child: Container(
        height: 32.h, // 64rpx / 2
        padding: EdgeInsets.symmetric(horizontal: 22.w), // 44rpx / 2
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(30.r), // 40rpx / 2
        ),
        child: Center(
          child: Text(
            S.current.spec_card_title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 15.sp, // 30rpx / 2
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建数量控制按钮 - 使用通用的数量控制样式
  Widget _buildQuantityControls(
    final BuildContext context,
    final SpecModalState state,
    final SpecModalController controller,
  ) {
    // final isUg = Localizations.localeOf(context).languageCode == 'en';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: Container(
        alignment: Alignment.center,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 减号按钮 - 使用通用样式
            if (state.count > 0)
              addRemoveIcon(
                type: CartType.remove,
                onTap: (final _) => controller.minusCount(),
              ),
            // 商品数量显示 - 使用通用组件
            FoodCountWidget(foodCount: state.count),
            // 加号按钮 - 使用通用样式
            addRemoveIcon(
              type: CartType.add,
              onTap: (final context) {
                _playAddToCartAnimation(context);
                controller.addCount();
              },
            ),
          ],
        ),
      ),
    );
  }

  bool _shouldShowActivityInfo(final SpecModalState state) {
    return state.hasPreferential || state.hasSeckill;
  }

  String _getActivityInfoText(final SpecModalState state) {
    if (state.hasSeckill && state.seckillMaxOrderCount + 1 > state.count) {
      return '${S.current.seckill_total_count}: ${state.seckillMaxOrderCount}';
    } else if (state.hasPreferential) {
      return '${S.current.discount_max_count}: ${state.maxOrderCount}';
    }
    if (state.hasSeckill && !state.hasPreferential) {
      return '${S.current.seckill_total_count}: ${state.seckillMaxOrderCount}';
    } else if (state.hasPreferential && !state.hasSeckill) {
      return '${S.current.discount_max_count}: ${state.maxOrderCount}';
    }
    return '';
  }

  /// 播放加入购物车动画
  void _playAddToCartAnimation(final BuildContext? btnContext) {
    if (btnContext == null) return;

    // 获取加号按钮的位置
    final RenderBox? renderBox = btnContext.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final Offset startPosition = renderBox.localToGlobal(
      Offset(renderBox.size.width / 2, renderBox.size.height / 2),
    );

    // 获取购物车图标的位置
    final Offset endPosition = getWidgetGlobalPosition(globalCartIconKey);
    if (endPosition == Offset.zero) return;

    // 获取按钮的颜色
    const Color buttonColor = Colors.red;

    // 启动动画
    CartAnimationController.startAnimation(
      btnContext,
      startPosition,
      Offset(endPosition.dx + 21.w, endPosition.dy + 21.h), // 调整到购物车图标中心
      buttonColor,
    );
  }
}
