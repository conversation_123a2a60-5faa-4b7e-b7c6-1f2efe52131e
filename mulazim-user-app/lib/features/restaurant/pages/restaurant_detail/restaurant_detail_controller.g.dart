// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'restaurant_detail_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$restaurantDetailControllerHash() =>
    r'f3ad8a606c9e56507051199b70212475671471ce';

/// 餐厅详情控制器
///
/// Copied from [RestaurantDetailController].
@ProviderFor(RestaurantDetailController)
final restaurantDetailControllerProvider = AutoDisposeNotifierProvider<
    RestaurantDetailController, RestaurantDetailState>.internal(
  RestaurantDetailController.new,
  name: r'restaurantDetailControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$restaurantDetailControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RestaurantDetailController
    = AutoDisposeNotifier<RestaurantDetailState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
