import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/detail/map_list_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

// 定义滚动状态Provider
final scrollStateProvider = StateProvider<bool>((ref) => true);

class RestaurantDetailInfo extends ConsumerStatefulWidget {
  const RestaurantDetailInfo({super.key});

  @override
  ConsumerState createState() => _RestaurantDetailInfoState();
}

class _RestaurantDetailInfoState extends ConsumerState<RestaurantDetailInfo>
    with WidgetsBindingObserver {
  Map<String, dynamic> creationParams = {};
  final GlobalKey _mapKey = GlobalKey();
  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  // 默认值
  String startLatitude = '43.76832775608891';
  String startLongitude = '87.62604277173614';
  String endLatitude = '43.774392';
  String endLongitude = '87.633272';
  String restaurantLogo =
      'https://acdn.mulazim.com/upload/restaurant/license-images/202302/01/698f9104c4af9f22e854e6b867e3b211.jpg';

  // 添加一个key来强制重建地图组件
  Key _mapWidgetKey = UniqueKey();

  // 缓存地图位置信息，避免重复计算
  Rect? _mapRect;
  bool _isMapInitialized = false;

  @override
  void initState() {
    super.initState();
    // 添加应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);
    // 初始化时设置默认的creationParams
    _updateCreationParams();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(final AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台恢复时，强制刷新地图组件
    if (state == AppLifecycleState.resumed) {
      // 延迟一点时间确保应用完全恢复
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            // 生成新的key来强制重建地图组件
            _mapWidgetKey = UniqueKey();
            // 重新更新参数
            _updateCreationParams();
            // 重置地图位置缓存
            _mapRect = null;
            _isMapInitialized = false;
          });
        }
      });
    }
  }

  /// 更新地图创建参数
  void _updateCreationParams() {
    creationParams = {
      'user_latitude': startLatitude,
      'user_longitude': startLongitude,
      'store_latitude': endLatitude,
      'store_longitude': endLongitude,
      'restaurant_logo': restaurantLogo,
    };
  }

  /// 获取地图位置信息（带缓存）
  Rect? _getMapRect() {
    if (_mapRect != null) return _mapRect;

    try {
      if (_mapKey.currentContext == null) return null;

      final RenderBox mapBox =
          _mapKey.currentContext!.findRenderObject() as RenderBox;
      final Offset mapPosition = mapBox.localToGlobal(Offset.zero);

      _mapRect = Rect.fromLTWH(
        mapPosition.dx,
        mapPosition.dy,
        mapBox.size.width,
        mapBox.size.height,
      );

      return _mapRect;
    } catch (e) {
      if (kDebugMode) {
        print('获取地图位置出错: $e');
      }
      return null;
    }
  }

  // 判断手势操作是否在规划图之内（优化版本）
  bool _isPointerOnMap(final Offset globalPosition) {
    try {
      // 确保_mapKey有效且已经关联到一个渲染对象
      if (_mapKey.currentContext == null) {
        if (kDebugMode) {
          print('地图context为空');
        }
        return false;
      }

      // 获取地图区域在屏幕上的位置和大小
      final RenderBox? mapBox =
          _mapKey.currentContext!.findRenderObject() as RenderBox?;
      if (mapBox == null) {
        if (kDebugMode) {
          print('地图RenderBox为空');
        }
        return false;
      }

      final Offset mapPosition = mapBox.localToGlobal(Offset.zero);
      final Size mapSize = mapBox.size;

      // 创建地图区域矩形
      final Rect mapRect = Rect.fromLTWH(
        mapPosition.dx,
        mapPosition.dy,
        mapSize.width,
        mapSize.height,
      );

      // 判断触摸点是否在地图区域内
      final bool isInside = mapRect.contains(globalPosition);

      if (kDebugMode) {
        print('触摸位置: $globalPosition');
        print('地图区域: $mapRect');
        print('是否在地图内: $isInside');
      }

      return isInside;
    } catch (e) {
      if (kDebugMode) {
        print('判断触摸位置出错: $e');
      }
      return false;
    }
  }

  @override
  Widget build(final BuildContext context) {
    // 从Provider获取当前滚动状态
    final canScroll = ref.watch(scrollStateProvider);

    // 从Riverpod获取餐厅数据
    final data = ref.watch(
      restaurantDetailControllerProvider
          .select((state) => state.restaurantData),
    );

    // 从Riverpod获取用户位置数据
    final locationData = ref.watch(locationInfoProvider);

    // 更新餐厅数据（只在数据变化时更新）
    if (data != null) {
      final newEndLat = (data.lat ?? 0).toString();
      final newEndLng = (data.lng ?? 0).toString();
      final newLogo = data.logo ?? '';

      if (endLatitude != newEndLat ||
          endLongitude != newEndLng ||
          restaurantLogo != newLogo) {
        endLatitude = newEndLat;
        endLongitude = newEndLng;
        restaurantLogo = newLogo;
        _updateCreationParams();
      }
    }

    // 更新用户位置数据（只在数据变化时更新）
    if (locationData != null) {
      final newStartLat = locationData.latitude;
      final newStartLng = locationData.longitude;

      if (startLatitude != newStartLat || startLongitude != newStartLng) {
        startLatitude = newStartLat;
        startLongitude = newStartLng;
        _updateCreationParams();
      }
    }

    return Listener(
      onPointerDown: (final event) {
        if (_isPointerOnMap(event.position)) {
          // 触摸点在地图区域内，禁用滚动
          ref.read(scrollStateProvider.notifier).state = false;
          if (kDebugMode) {
            print('禁用页面滚动');
          }
        } else {
          // 触摸点不在地图区域内，允许滚动
          ref.read(scrollStateProvider.notifier).state = true;
          if (kDebugMode) {
            print('允许页面滚动');
          }
        }
      },
      onPointerUp: (final event) {
        // 触摸结束后，延迟恢复滚动状态，避免手势冲突
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            ref.read(scrollStateProvider.notifier).state = true;
            if (kDebugMode) {
              print('恢复页面滚动');
            }
          }
        });
      },
      onPointerCancel: (final event) {
        // 触摸取消后，立即恢复滚动状态
        ref.read(scrollStateProvider.notifier).state = true;
        if (kDebugMode) {
          print('取消触摸，恢复页面滚动');
        }
      },
      child: SafeArea(
        top: false,
        child: Container(
          color: const Color.fromRGBO(255, 255, 255, 1),
          // 使用CustomScrollView，根据canScroll状态控制滚动
          child: CustomScrollView(
            // 控制是否可以滚动，优化滚动物理特性
            physics: canScroll
                ? (Platform.isIOS
                    ? const BouncingScrollPhysics()
                    : const ClampingScrollPhysics())
                : const NeverScrollableScrollPhysics(),
            // controller: _scrollController,
            slivers: <Widget>[
              // SliverOverlapInjector 的作用是处理重叠滚动效果，
              // 确保 CustomScrollView 中的滚动视图不会与其他视图重叠。
              SliverOverlapInjector(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
              ),

              /// 店内内景
              if (data?.images?.isNotEmpty ?? false)
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.all(10.w),
                        child: Text(
                          S.current.shop_interior,
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                      // 优化图片列表，使用ListView.builder避免不必要的重建
                      SizedBox(
                        height:
                            (MediaQuery.of(context).size.width / 3.5 * 0.7) +
                                10.w,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: data!.images!.length,
                          itemBuilder: (context, index) {
                            final url = data.images![index];
                            return GestureDetector(
                              onTap: () =>
                                  showImageViewer(context, imageUrls: [url]),
                              child: Container(
                                width: MediaQuery.of(context).size.width / 3.5,
                                height: MediaQuery.of(context).size.width /
                                    3.5 *
                                    0.7,
                                padding: EdgeInsets.all(5.w),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10.w),
                                  child: CachedNetworkImage(
                                    imageUrl: url,
                                    fit: BoxFit.fill,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      Divider(height: 15.h, color: AppColors.dividerColor),
                    ],
                  ),
                ),

              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(10.w),
                  child: Row(
                    children: [
                      /// 地址
                      Text(
                        S.current.shopAddr,
                        style: TextStyle(fontSize: 16.sp),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Text(
                          "${data?.address}",
                          style: TextStyle(color: AppColors.textSecondColor),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Divider(height: 15.h, color: AppColors.dividerColor),
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.all(10.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ///营业时间
                          Text(
                            S.current.open_time,
                            style: TextStyle(fontSize: 16.sp),
                          ),
                          SizedBox(width: 10.w),
                          Text(
                            textDirection: TextDirection.ltr,
                            "${data?.openTime} - ${data?.closeTime}",
                            style: TextStyle(color: AppColors.textSecondColor),
                          ),
                        ],
                      ),
                    ),
                    // 地图部分
                    _mapPart(),
                  ],
                ),
              ),
              SliverToBoxAdapter(
                child: Divider(height: 15.h, color: AppColors.dividerColor),
              ),
              SliverToBoxAdapter(
                child: InkWell(
                  onTap: () {
                    // 跳转营业执照页面
                    context.push(
                      AppPaths.businessLicensePage,
                      extra: {
                        'restaurant_id': data?.id,
                      },
                    );
                  },
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          S.current.business_license,
                          style: TextStyle(fontSize: 18.sp),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 18.w,
                          color: AppColors.textPrimaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SliverToBoxAdapter(
                child: Divider(height: 10.h, color: AppColors.dividerColor),
              ),
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.all(10.w),
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${data?.name}",
                        style: TextStyle(fontSize: 16.sp),
                      ),
                      Text(
                        "${data?.description}",
                        textAlign: TextAlign.justify,
                      ),
                    ],
                  ),
                ),
              ),
              // 底部安全间距，避免内容被底部导航栏遮挡
              SliverToBoxAdapter(
                child: Container(
                  height: 20.h,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///规划图元素
  Widget _mapPart() {
    // This is used in the platform side to register the view.
    const String viewType = '<map-line-one>';
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      child: Stack(
        children: [
          // 地图容器 - 移除复杂的GestureDetector包装
          Container(
            key: _mapKey,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.w),
              color: AppColors.baseBackgroundColor,
              // 添加阴影，提升视觉效果
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8.w,
                  offset: Offset(0, 2.h),
                ),
              ],
            ),
            height: 200.h, // 调整高度，避免过高影响滚动
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.w),
              child: Platform.isIOS
                  ? UiKitView(
                      key: _mapWidgetKey, // 使用动态key强制重建
                      viewType: "plugin/mapview",
                      creationParams: creationParams,
                      creationParamsCodec: const StandardMessageCodec(),
                      // 简化手势识别器配置，只保留必要的
                      gestureRecognizers: <Factory<
                          OneSequenceGestureRecognizer>>{
                        // 拦截所有手势，防止传递给父级
                        Factory<EagerGestureRecognizer>(
                          () => EagerGestureRecognizer(),
                        ),
                      },
                    )
                  : AndroidView(
                      key: _mapWidgetKey, // 使用动态key强制重建
                      viewType: viewType,
                      creationParams: creationParams,
                      creationParamsCodec: const StandardMessageCodec(),
                      // 简化手势识别器配置，只保留必要的
                      gestureRecognizers: <Factory<
                          OneSequenceGestureRecognizer>>{
                        // 拦截所有手势，防止传递给父级
                        Factory<EagerGestureRecognizer>(
                          () => EagerGestureRecognizer(),
                        ),
                      },
                    ),
            ),
          ),
          // 导航按钮
          Positioned(
            top: 10.h,
            right: 10.w,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(25.w),
                onTap: () async {
                  try {
                    showModalBottomSheet(
                      backgroundColor: Colors.transparent,
                      context: context,
                      builder: (final _) => mapCallWidget(
                        context,
                        title: S.current.select_navigation,
                        startLatitude: num.parse(startLatitude),
                        startLongitude: num.parse(startLongitude),
                        endLatitude: num.parse(endLatitude),
                        endLongitude: num.parse(endLongitude),
                      ),
                    );
                  } catch (e) {
                    if (kDebugMode) {
                      print('Failed to open navigation: $e');
                    }
                  }
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: AppColors.baseGreenColor,
                    borderRadius: BorderRadius.circular(25.w),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4.w,
                        offset: Offset(0, 2.h),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.subdirectory_arrow_right,
                        color: Colors.white,
                        size: 18.w,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        S.current.gps,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
