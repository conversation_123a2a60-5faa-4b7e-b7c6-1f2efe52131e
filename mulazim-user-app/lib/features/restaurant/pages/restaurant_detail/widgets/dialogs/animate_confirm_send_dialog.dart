import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

class ConfirmSendDialog extends Dialog{
  final String content;
  final double screenWidth;
  // 构造函数赋值
  ConfirmSendDialog({Key? key,this.content="",this.screenWidth=0.0}) : super(key: key);
  // AppState? appState;

  @override
  Widget build(BuildContext context) {
    // 调用方法
    return Material(
        type:MaterialType.transparency,
        child:Center(
          child: Consumer(
            builder: (context,ref,child) {
              return Directionality(
                textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                child: Container(
                  width:(screenWidth - 74.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.w),
                    color:Colors.white,
                  ),
                  child:Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.only(top: 25.w,bottom:15.w, left: 20.w,right: 20.w),
                        child:Container(
                          alignment: Alignment.center,
                          // child: Text(S.current.do_you_send_this_profile,style: TextStyle(fontSize: AppFontSize.meddleSize,height: 1.5),),
                          child: Text(content,style: TextStyle(fontSize: 16.sp,height: 1.5,fontFamily: 'UkijTuzTom'),textAlign: TextAlign.justify,),
                        ),
                      ),
                Directionality(
                  textDirection: TextDirection.rtl,
                  child:Container(
                        width: double.infinity,
                        child:
                        Row(
                          children: [
                            Expanded(
                              child:InkWell(
                                child: Container(
                                    height: 50.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border:Border(
                                        left: BorderSide(width: 0.4, color: AppColors.textSecondaryColor),
                                        top: BorderSide(width: 0.4, color: AppColors.textSecondaryColor),
                                      ),
                                    ),
                                    // height: double.infinity,
                                    child:Text(S.current.yes,style: TextStyle(fontSize: 18.sp,color: Colors.black,fontFamily: 'UkijTuzTom'),
                                      textAlign: TextAlign.center,
                                    )),
                                  onTap: (){
                                    Navigator.of(context).pop('yes');
                                  },
                                ),
                            ),
                            Expanded(
                              child: InkWell(
                                child:
                                Container(
                                  height: 50.w,
                                  decoration: BoxDecoration(
                                    border:Border(
                                      top: BorderSide(width: 0.4, color: Colors.black),
                                    ),
                                  ),
                                  child: Center(
                                      child: Text(
                                        S.current.no,style: TextStyle(fontSize: 18.sp,color: AppColors.textSecondaryColor,fontFamily: 'UkijTuzTom'),
                                        textAlign: TextAlign.center,
                                      )
                                  ),

                                ),
                                onTap: (){
                                  Navigator.of(context).pop('no');
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      // onTap: (){
                      //   Navigator.pop(context);
                      // },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

        )
    );
  }
}
