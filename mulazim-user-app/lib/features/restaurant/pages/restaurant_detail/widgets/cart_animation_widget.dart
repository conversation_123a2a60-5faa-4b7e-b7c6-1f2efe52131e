import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 购物车动画控制器
class CartAnimationController {
  static OverlayEntry? _overlayEntry;
  static bool _isAnimating = false;

  /// 启动动画
  static void startAnimation(
    final BuildContext context,
    final Offset startPosition,
    final Offset endPosition,
    final Color? color,
  ) {
    // 如果已经有动画在进行，不再创建新动画
    if (_isAnimating) return;
    _isAnimating = true;

    // 创建覆盖层
    _overlayEntry = OverlayEntry(
      builder: (final context) => CartAnimationWidget(
        startPosition: startPosition,
        endPosition: endPosition,
        color: color,
        onAnimationComplete: () {
          // 动画完成后移除覆盖层
          if (_overlayEntry != null) {
            _overlayEntry!.remove();
            _overlayEntry = null;
            _isAnimating = false;
          }
        },
      ),
    );

    // 将动画添加到覆盖层
    Overlay.of(context).insert(_overlayEntry!);
  }
}

/// 用于创建二次贝塞尔曲线的可动画对象。
class QuadraticBezierTween extends Animatable<Offset> {
  final Offset p0; // 起始点
  final Offset p1; // 控制点
  final Offset p2; // 结束点

  QuadraticBezierTween({
    required this.p0,
    required this.p1,
    required this.p2,
  });

  @override
  Offset transform(final double t) {
    final double oneMinusT = 1.0 - t;
    final double oneMinusTSquared = oneMinusT * oneMinusT;
    final double tSquared = t * t;

    final double x =
        oneMinusTSquared * p0.dx + 2 * oneMinusT * t * p1.dx + tSquared * p2.dx;
    final double y =
        oneMinusTSquared * p0.dy + 2 * oneMinusT * t * p1.dy + tSquared * p2.dy;
    return Offset(x, y);
  }
}

/// 购物车动画组件
class CartAnimationWidget extends StatefulWidget {
  /// 动画起始位置
  final Offset startPosition;

  /// 动画终点位置
  final Offset endPosition;

  /// 动画颜色
  final Color? color;

  /// 动画完成回调
  final VoidCallback onAnimationComplete;

  /// 构造函数
  const CartAnimationWidget({
    super.key,
    required this.startPosition,
    required this.endPosition,
    this.color,
    required this.onAnimationComplete,
  });

  @override
  State<CartAnimationWidget> createState() => _CartAnimationWidgetState();
}

class _CartAnimationWidgetState extends State<CartAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _positionAnimation;

  // 缓存动画项尺寸，避免重复计算
  final double _itemSize = 25.w;
  late final double _halfItemSize = _itemSize / 2;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600), // 更好的可见性
      vsync: this,
    );

    // 设置抛物线路径动画
    _positionAnimation = _createParabolicAnimation();

    // 启动动画
    _controller.forward().then((final _) {
      widget.onAnimationComplete();
    });
  }

  /// 使用二次贝塞尔曲线创建抛物线动画
  Animation<Offset> _createParabolicAnimation() {
    final Offset p0 = widget.startPosition;
    final Offset p2 = widget.endPosition;

    // 控制点 P1 计算：
    // X: 定位到水平距离的 30% 处，使峰值更早出现。
    // Y: 定位在起始位置的 Y 轴上方，确保有明显的向上抛出。
    final double controlX = p0.dx + (p2.dx - p0.dx) * 0.3;
    final double controlY = p0.dy - 70.h; // 40.h 单位在起始位置的 Y 轴上方，确保有明显的向上抛出。

    final Offset p1 = Offset(controlX, controlY);

    return QuadraticBezierTween(
      p0: p0,
      p1: p1,
      p2: p2,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut, // 沿贝塞尔路径平滑运动
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (final context, final child) {
        return Positioned(
          left: _positionAnimation.value.dx - _halfItemSize,
          top: _positionAnimation.value.dy - _halfItemSize,
          child: RepaintBoundary(
            child: child!,
          ),
        );
      },
      child: Container(
        width: _itemSize,
        height: _itemSize,
        decoration: BoxDecoration(
          color: widget.color ?? const Color(0xFF4CAF50),
          shape: BoxShape.circle,
        ),
        alignment: Alignment.center,
        child: Text(
          '1',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12.sp,
          ),
        ),
      ),
    );
  }
}

/// 获取窗口小部件的全局位置
Offset getWidgetGlobalPosition(final GlobalKey key) {
  final RenderBox? renderBox =
      key.currentContext?.findRenderObject() as RenderBox?;
  if (renderBox == null || !renderBox.hasSize) {
    return Offset.zero;
  }
  return renderBox.localToGlobal(Offset.zero);
}
