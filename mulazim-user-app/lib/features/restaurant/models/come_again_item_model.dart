import 'package:user_app/data/models/my_order/order_detail_model.dart';

/// "再来一单"商品项模型 - 与微信小程序格式保持一致
class ComeAgainItemModel {
  /// 美食ID
  final int id;

  /// 购买数量
  final int count;

  /// 美食类型 (1: 规格商品, 2: 套餐商品, 其他: 普通商品)
  final int foodType;

  /// 规格唯一标识符（如果有规格）
  final String? specUniqueId;

  const ComeAgainItemModel({
    required this.id,
    required this.count,
    required this.foodType,
    this.specUniqueId,
  });

  /// 从订单详情数据创建"再来一单"商品项
  static List<ComeAgainItemModel> fromOrderDetailFoods(
      List<OrderDetailFoods> foods) {
    final List<ComeAgainItemModel> result = [];
    final Set<int> processedIds = <int>{};

    for (var i = 0; i < foods.length; i++) {
      final currentFood = foods[i];

      // 情况1：没有selected_food或者没有selected_food.id，作为普通食品项处理
      if (currentFood.selectedFood == null ||
          currentFood.selectedFood!.id == null) {
        String? specUniqueId;

        // 处理规格选项 - 对应微信小程序逻辑
        if (currentFood.foodType == 1 && currentFood.selectedFood != null) {
          final specOptions = currentFood.selectedFood!.specOptions ?? [];
          final specOptionIds = specOptions
              .map((option) => option.id)
              .where((id) => id != null)
              .cast<int>()
              .toList();

          if (specOptionIds.isNotEmpty) {
            // 按照微信小程序规则拼接：${food_id}_${option_ids.join("_")}
            specUniqueId = '${currentFood.foodId}_${specOptionIds.join("_")}';
          }
        }

        result.add(ComeAgainItemModel(
          id: currentFood.foodId ?? 0,
          count: currentFood.number ?? 1,
          foodType: currentFood.foodType ?? 0,
          specUniqueId: specUniqueId,
        ));
        continue;
      }

      // 情况2：有selected_food.id，需要处理合并逻辑 - 对应微信小程序逻辑
      final selectedFoodId = currentFood.selectedFood!.id!;
      if (processedIds.contains(selectedFoodId)) {
        continue;
      }
      processedIds.add(selectedFoodId);

      // 找到所有相同selected_food.id的商品
      final sameIdFoods = foods
          .where((item) =>
              item.selectedFood != null &&
              item.selectedFood!.id == selectedFoodId)
          .toList();

      // 计算总数量
      final totalCount =
          sameIdFoods.fold<int>(0, (acc, curr) => acc + (curr.number ?? 0));

      String? specUniqueId;

      // 处理规格信息
      if (currentFood.foodType == 1 && currentFood.selectedFood != null) {
        final specOptions = currentFood.selectedFood!.specOptions ?? [];
        final specOptionIds = specOptions
            .map((option) => option.id)
            .where((id) => id != null)
            .cast<int>()
            .toList();

        if (specOptionIds.isNotEmpty) {
          // 按照微信小程序规则拼接：${food_id}_${option_ids.join("_")}
          specUniqueId = '${currentFood.foodId}_${specOptionIds.join("_")}';
        }
      }

      result.add(ComeAgainItemModel(
        id: currentFood.foodId ?? 0,
        count: totalCount,
        foodType: currentFood.foodType ?? 0,
        specUniqueId: specUniqueId,
      ));
    }

    return result;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'count': count,
      'food_type': foodType,
      'spec_unique_id': specUniqueId,
    };
  }

  factory ComeAgainItemModel.fromJson(Map<String, dynamic> json) {
    return ComeAgainItemModel(
      id: json['id'] ?? 0,
      count: json['count'] ?? 1,
      foodType: json['food_type'] ?? 0,
      specUniqueId: json['spec_unique_id'],
    );
  }

  @override
  String toString() {
    return 'ComeAgainItemModel(id: $id, count: $count, foodType: $foodType, specUniqueId: $specUniqueId)';
  }
}
