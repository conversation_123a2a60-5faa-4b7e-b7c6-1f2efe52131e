import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/features/chat/pages/chat_list/chat_list_controller.dart';
import 'package:user_app/features/chat/pages/chat_list/widgets/chat_list_view.dart';
import 'package:user_app/generated/l10n.dart';

/// 聊天列表页面
class ChatListPage extends ConsumerWidget {
  /// 构造函数
  const ChatListPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听聊天列表状态
    final state = ref.watch(chatListControllerProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: S.of(context).chat_room_list,
      ),
      body: state.isLoading
          ? const Center(child: LoadingWidget())
          : ChatListView(
              chatList: state.chatList,
            ),
    );
  }
}
