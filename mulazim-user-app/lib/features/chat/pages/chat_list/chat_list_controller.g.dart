// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$chatListControllerHash() =>
    r'9e1dbd2d44fb27e79e20aa28a476ec54f407e0ee';

/// 聊天列表控制器
///
/// Copied from [ChatListController].
@ProviderFor(ChatListController)
final chatListControllerProvider =
    AutoDisposeNotifierProvider<ChatListController, ChatListState>.internal(
  ChatListController.new,
  name: r'chatListControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$chatListControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ChatListController = AutoDisposeNotifier<ChatListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
