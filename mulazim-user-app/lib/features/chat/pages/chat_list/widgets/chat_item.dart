import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/data/models/chat/chat_model.dart';
import 'package:user_app/routes/paths.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// 聊天项组件
class ChatItemWidget extends ConsumerWidget {
  /// 构造函数
  const ChatItemWidget({
    super.key,
    required this.item,
  });

  /// 聊天项数据
  final ChatItem item;

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7.r),
        ),
        child: InkWell(
          onTap: () => context.push(
            AppPaths.chatRoomPage,
            extra: {
              'order_id': item.orderId,
              'name': item.restaurantName,
            },
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 10.r),
            child: Row(
              children: [
                // 头像区域
                _buildAvatar(),

                // 文本区域
                _buildTextContent(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建头像区域
  Widget _buildAvatar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.r),
      child: Stack(
        children: [
          // 餐厅图片
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(7.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7.r),
              child: CachedNetworkImage(
                imageUrl: item.restaurantImage,
                fit: BoxFit.cover,
                errorWidget: (final context, final url, final error) =>
                    const Icon(Icons.error),
              ),
            ),
          ),

          // 未读消息数
          if (item.msgCount > 0)
            Positioned(
              child: Container(
                width: 20.r,
                height: 20.r,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Center(
                  child: Text(
                    '${item.msgCount}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建文本内容区域
  Widget _buildTextContent() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部信息行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 餐厅名称
              Expanded(
                child: Text(
                  item.restaurantName,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // 消息时间
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.r),
                child: Text(
                  item.msgTime,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ],
          ),

          // 消息预览
          SizedBox(height: 6.r),
          Text(
            item.newMsg,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14.sp,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
