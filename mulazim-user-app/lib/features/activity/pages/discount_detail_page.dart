import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/features/activity/pages/seckill_page.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class DiscountDetailPage extends ConsumerStatefulWidget {
  DiscountDetailPage(
      {super.key,
      required this.themActive,
      required this.themActivePreferential,
      required this.buildingId,
      required this.secKillFoods});
  ThemActive? themActive;
  int? buildingId;
  List<ThemActivePreferential>? themActivePreferential;
  List<SecKillFoods>? secKillFoods;

  @override
  ConsumerState createState() => _DiscountDetailPageState();
}

class _DiscountDetailPageState extends ConsumerState<DiscountDetailPage>
    with TickerProviderStateMixin {
  List<ThemActivePreferential> thisThemActivePreferential = [];
  List<SecKillFoods>? secKillFoods = [];
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    // TODO: implement initState

    // 初始化进度条动画控制器
    _progressController = AnimationController(
      duration: const Duration(seconds: 2), // 2秒动画
      vsync: this,
    );

    // 创建从0到0.6的动画（60%）
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _progressController.forward();

    super.initState();
    thisThemActivePreferential = widget.themActivePreferential!;
    secKillFoods = widget.secKillFoods ?? [];
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: FormatUtil.parseColor(widget.themActive!.color!),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                children: [
                  Stack(
                    children: [
                      Column(
                        children: [
                          Container(
                            color: AppColors.basePinkColor,
                            child: ClipRRect(
                              // borderRadius: BorderRadius.circular(8.w),
                              child: PrefectImage(
                                imageUrl: widget.themActive!.cover ?? '',
                                fit: BoxFit.cover,
                                height: 240.w,
                                width: MediaQuery.of(context).size.width,
                              ),
                            ),
                          ),
                          Container(
                            height: 40.w,
                            color: FormatUtil.parseColor(
                                widget.themActive!.color ?? 'fb3455'),
                          ),
                        ],
                      ),
                      Positioned(
                        bottom: 10.w,
                        right: 20.w,
                        left: 20.w,
                        child: Container(
                            height: 46.w,
                            padding: EdgeInsets.all(0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(100.w),
                              color: FormatUtil.parseColor(
                                  widget.themActive!.color ?? 'fb3455'),
                              border: Border.all(
                                color: Colors.yellow,
                                width: 2.w,
                              ),
                            ),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 10.w,
                                ),
                                Icon(
                                  IconFont.search,
                                  color: Colors.yellow,
                                  size: 20.sp,
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                Container(
                                  height: 18.w,
                                  width: 2.w,
                                  color: Colors.yellow,
                                ),
                                Expanded(
                                  child: Container(
                                      // color: Colors.red,
                                      padding: EdgeInsets.only(
                                          right: 5.w, left: 5.w, bottom: 5.w),
                                      child: TextField(
                                        onChanged: (text) async {
                                          // 这里的 text 是输入框中的当前文本
                                          // await getSearchByWord(keyWord: text);
                                          List<ThemActivePreferential>?
                                              keyThemActivePreferential = [];
                                          for (int i = 0;
                                              i <
                                                  thisThemActivePreferential!
                                                      .length;
                                              i++) {
                                            if (thisThemActivePreferential![i]
                                                .foodName!
                                                .contains(text)) {
                                              keyThemActivePreferential.add(
                                                  thisThemActivePreferential![
                                                      i]);
                                            }
                                          }
                                          thisThemActivePreferential =
                                              keyThemActivePreferential;
                                          setState(() {});
                                        },
                                        style: TextStyle(
                                            fontSize: mainSize,
                                            color: Colors.yellow),
                                        decoration: InputDecoration(
                                          hintText: S.current
                                              .search_place_holder_text, // 占位符
                                          hintStyle: TextStyle(
                                              fontSize: mainSize,
                                              color: Colors.yellow),
                                          border: InputBorder.none, // 取消默认边框
                                          enabledBorder:
                                              InputBorder.none, // 取消非聚焦状态下的边框
                                          focusedBorder:
                                              InputBorder.none, // 取消聚焦状态下的边框
                                        ),
                                      )),
                                ),
                              ],
                            )),
                      )
                    ],
                  ),
                  thisThemActivePreferential.isNotEmpty
                      ? Column(
                          children: [
                            Container(
                              margin: EdgeInsets.all(10.w),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 8.w),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(10.w),
                                  topLeft: Radius.circular(10.w),
                                ),
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.white,
                                    Colors.white.withOpacity(0.5)
                                  ], // 渐变的颜色列表
                                  begin: Alignment.topRight, // 渐变开始位置
                                  end: Alignment.bottomLeft, // 渐变结束位置
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.themActive!.name ?? '',
                                    style: TextStyle(
                                      fontSize: soBigSize,
                                      color: FormatUtil.parseColor(
                                          widget.themActive!.color ?? 'fb3455'),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              alignment: Alignment.topCenter,
                              // height: 1000.w,
                              // color: Colors.yellow,
                              color: FormatUtil.parseColor(
                                  widget.themActive!.color ?? 'fb3455'),
                              padding: EdgeInsets.only(
                                  right: 10.w, left: 10.w, bottom: 10.w),
                              child: GridView.count(
                                // Create a grid with 2 columns. If you change the scrollDirection to
                                // horizontal, this produces 2 rows.
                                shrinkWrap:
                                    true, // Allow GridView to take up only the space it needs
                                physics: NeverScrollableScrollPhysics(),
                                crossAxisCount: 2,
                                mainAxisSpacing: 10.w,
                                crossAxisSpacing: 10.w,
                                childAspectRatio: 0.84,
                                padding: EdgeInsets.zero,
                                // Generate 100 widgets that display their index in the List.
                                children: List.generate(
                                    (thisThemActivePreferential ?? []).length,
                                    (index) => _discountItem(
                                        thisThemActivePreferential[index])),
                              ),
                            ),
                          ],
                        )
                      : Container(
                          alignment: Alignment.bottomCenter,
                          height: 250.w,
                          child: Text(
                            S.current.search_nothing_info,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: mainSize,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                  (secKillFoods ?? []).isNotEmpty
                      ? Column(
                          children: [
                            Container(
                              margin: EdgeInsets.all(10.w),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 8.w),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(10.w),
                                  topLeft: Radius.circular(10.w),
                                ),
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.white,
                                    Colors.white.withOpacity(0.5)
                                  ], // 渐变的颜色列表
                                  begin: Alignment.topRight, // 渐变开始位置
                                  end: Alignment.bottomLeft, // 渐变结束位置
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    S.current.sec_kill,
                                    style: TextStyle(
                                      fontSize: soBigSize,
                                      color: FormatUtil.parseColor(
                                          widget.themActive!.color ?? 'fb3455'),
                                    ),
                                  )
                                ],
                              ),
                            ),

                            // Container(
                            //   height: 500.w,
                            //   child: SingleChildScrollView(
                            //     physics: const NeverScrollableScrollPhysics(),
                            //         // : NeverScrollableScrollPhysics(),
                            //     child:
                            //   ),
                            // )
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: List.generate(
                                (secKillFoods ?? []).length,
                                (index) =>
                                    _foodItem(secKillFoods![index], context),
                              ),
                            ),
                          ],
                        )
                      : Container(
                          alignment: Alignment.bottomCenter,
                          height: 250.w,
                          child: Text(
                            S.current.search_nothing_info,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: mainSize,
                                fontWeight: FontWeight.bold),
                          ),
                        )
                ],
              ),
            ),
            Positioned(
              top: 50.w,
              left: 20.w,
              child: ClipOval(
                child: InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: ref.watch(languageProvider) == 'zh'
                        ? EdgeInsets.only(left: 10.w)
                        : null,
                    height: 46.w,
                    width: 46.w,
                    color: Colors.black.withOpacity(0.5),
                    child: Icon(
                      ref.watch(languageProvider) == 'ug'
                          ? Icons.arrow_forward_ios
                          : Icons.arrow_back_ios,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget _discountItem(ThemActivePreferential themActivePreferential) {
    return InkWell(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': themActivePreferential.restaurantId ?? 0,
            'buildingId': widget.buildingId,
            'ids': [themActivePreferential.foodId ?? 0],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w), color: Colors.white),
        padding: EdgeInsets.symmetric(horizontal: 8.w),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: 8.w, bottom: 2.h),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.w),
                child: PrefectImage(
                  imageUrl: themActivePreferential.image ?? '',
                  fit: BoxFit.fill,
                  width: MediaQuery.of(context).size.width - 30.w,
                  height: 132.w,
                ),
              ),
            ),
            Container(
                alignment: ref.watch(languageProvider) == 'ug'
                    ? Alignment.centerRight
                    : Alignment.centerLeft,
                child: Text(
                  themActivePreferential.foodName ?? '',
                  style: TextStyle(fontSize: mainSize),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                )),
            SizedBox(
              height: 2.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${themActivePreferential.price}',
                  style: TextStyle(
                    fontSize: titleSize,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NumberFont',
                  ),
                ),
                Text(
                  '￥',
                  style: TextStyle(
                    fontSize: secondSize,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Text(
                  '￥${themActivePreferential.originPrice}',
                  style: TextStyle(
                    fontSize: secondSize,
                    color: AppColors.textSecondColor,
                    decoration: TextDecoration.lineThrough,
                    decorationColor: AppColors.textSecondColor,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 2.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  S.current.sell_count,
                  style: TextStyle(
                      fontSize: secondSize, color: AppColors.textSecondColor),
                ),
                SizedBox(
                  width: 5.w,
                ),
                Text(
                  '${themActivePreferential.monthOrderCount}',
                  style: TextStyle(
                      fontSize: secondSize, color: AppColors.textSecondColor),
                ),
                SizedBox(
                  width: 5.w,
                ),
                Image.asset(
                  'assets/images/huo.png',
                  fit: BoxFit.cover,
                  width: 14.w,
                ),
              ],
            ),
            SizedBox(
              height: 8.h,
            ),
          ],
        ),
      ),
    );
  }

  Widget _foodItem(SecKillFoods secKillFoods, BuildContext context) {
    double lineWidth = MediaQuery.of(context).size.width - 115.w - 60.w;

    return InkWell(
      onTap: () {
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': secKillFoods.restaurantId ?? 0,
            'buildingId': widget.buildingId,
            'ids': [secKillFoods.id ?? 0],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w), color: Colors.white),
        margin: EdgeInsets.only(bottom: 10.w, right: 10.w, left: 10.w),
        padding: EdgeInsets.all(10.w),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: secKillFoods.image ?? '',
                width: 115.w,
                height: 105.w,
                fit: BoxFit.fill,
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
            Expanded(
              child: Container(
                child: Column(
                  children: [
                    Container(
                        alignment: ref.watch(languageProvider) == 'ug'
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Text(
                          '${secKillFoods.name}',
                          textAlign: TextAlign.start,
                          style: TextStyle(
                              fontSize: titleSize, color: Colors.black),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                    SizedBox(
                      height: 8.w,
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 5.w,
                            ),
                            Text(
                                FormatUtil.formatAmount(
                                    secKillFoods.price ?? 0),
                                style: TextStyle(
                                  fontSize: 20.sp,
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'NumberFont',
                                )),
                            Text('￥',
                                style: TextStyle(
                                    fontSize: mainSize, color: Colors.red)),
                            Text(
                              FormatUtil.formatAmount(
                                  secKillFoods.oldPrice ?? 0),
                              style: TextStyle(
                                  fontSize: mainSize,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: AppColors.textSecondColor,
                                  color: AppColors.textSecondaryColor),
                            ),
                            Text('￥',
                                style: TextStyle(
                                    decoration: TextDecoration.lineThrough,
                                    fontSize: littleSize,
                                    decorationColor: AppColors.textSecondColor,
                                    color: AppColors.textSecondaryColor)),
                            SizedBox(
                              width: 5.w,
                            ),
                          ],
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 5.w),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.w),
                            gradient: LinearGradient(
                              colors: [
                                AppColors.discountRedSeColor,
                                AppColors.discountRedColor
                              ], // 渐变的颜色列表
                              begin: Alignment.centerLeft, // 渐变开始位置
                              end: Alignment.centerRight, // 渐变结束位置
                            ),
                          ),
                          child: Text(
                            S.current.sec_kill_buy,
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: mainSize,
                                fontWeight: FontWeight.bold),
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 12.w,
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: lineWidth,
                          child: AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (context, child) {
                              return CustomLinearProgressIndicator(
                                progress: int.parse(
                                        (secKillFoods.saledCount ?? 0)
                                            .toString()) /
                                    ((secKillFoods.totalCount ?? 0)),
                                color: AppColors.discountRedSeColor,
                                width: lineWidth,
                                height: 15.w,
                              );
                            },
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
