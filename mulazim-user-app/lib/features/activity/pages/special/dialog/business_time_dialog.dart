import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

class BusinessTimeDialog extends Dialog {
  const BusinessTimeDialog({
    super.key,
    required this.title,
    required this.content,
    required this.onTap,
    required this.onDiscountTap,
  });
  final String title;
  final String content;
  final VoidCallback onTap;
  final VoidCallback onDiscountTap;

  @override
  Widget build(final BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return Center(
      child: Container(
        width: screenWidth - 50.w,
        // height:Adapt.setPx(140),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              // height: 520.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
                // color: AppColors.redColor,
                gradient: LinearGradient(
                  colors: [
                    AppColors.secondGreenColor,
                    Colors.white,
                    Colors.white,
                    AppColors.secondGreenColor,
                  ], // 渐变的颜色列表
                  begin: Alignment.topRight, // 渐变开始位置
                  end: Alignment.bottomRight, // 渐变结束位置
                ),
              ),
              child: Column(
                children: [
                  Container(
                    // height: 300.w,
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(12.w),
                        topLeft: Radius.circular(12.w),
                      ),
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 30.w,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Icon(Icons.accessibility,color: Colors.green,),
                            Image.asset(
                              'assets/images/order_left.png',
                              fit: BoxFit.fill,
                              width: 20.w,
                              height: 12.w,
                            ),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              title,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                                fontFamily: AppConstants.mainFont,
                              ),
                            ),
                            SizedBox(
                              width: 8.w,
                            ),
                            Image.asset(
                              'assets/images/order_right.png',
                              fit: BoxFit.fill,
                              width: 20.w,
                              height: 12.w,
                            ),
                            // Icon(Icons.accessibility,color: Colors.green,),
                          ],
                        ),
                        SizedBox(
                          height: 30.w,
                        ),
                        Text(
                          content,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontFamily: AppConstants.mainFont,
                            fontSize: 18.sp,
                          ),
                        ),
                        SizedBox(
                          height: 30.w,
                        ),
                        Row(
                          children: [
                            //跳转优惠券页面按钮
                            Flexible(
                              flex: 2,
                              child: InkWell(
                                onTap: () {
                                  Navigator.of(context).pop();
                                  onDiscountTap();
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(40.w),
                                  ),
                                  height: 40.w,
                                  child: Center(
                                    child: Text(
                                      S.current.more_discount,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 18.sp,
                                        fontFamily: AppConstants.mainFont,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.w),
                            // 我知道了
                            Flexible(
                              flex: 1,
                              child: InkWell(
                                onTap: onTap,
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(40.w),
                                    border: Border.all(
                                      width: 1.w,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                  height: 40.w,
                                  child: Center(
                                    child: Text(
                                      S.current.got_it,
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18.sp,
                                        fontFamily: AppConstants.mainFont,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 20.w,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // child: _swiperPart(popupAdver!),
            ),
          ],
        ),
      ),
    );
  }
}
