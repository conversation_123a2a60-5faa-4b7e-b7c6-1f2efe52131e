import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';
import '../user_ranking_controller.dart';
import '../user_ranking_state.dart';

/// 用户信息行组件
class UserLine extends ConsumerWidget {
  const UserLine({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final state = ref.watch(userRankingControllerProvider);
    final controller = ref.read(userRankingControllerProvider.notifier);
    final localStorageRepository = ref.watch(localStorageRepositoryProvider);
    final userInfo = localStorageRepository.getUserInfo();

    return Row(
      children: [
        // 用户头像和信息
        _buildUserSection(state, userInfo, languageId),

        const Spacer(),

        // 历史记录按钮
        _buildHistoryButton(controller, languageId),
      ],
    );
  }

  /// 构建历史记录按钮
  Widget _buildHistoryButton(
    final UserRankingController controller,
    final int languageId,
  ) {
    return GestureDetector(
      onTap: () => controller.goToRankingHistory(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 历史图标
          Icon(
            Icons.history,
            color: Colors.white,
            size: 18.sp,
          ),
          SizedBox(width: 4.w),
          // 历史文字
          Text(
            S.current.ranking_history,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 2.w),

          // 更多图标
          Icon(
            Icons.chevron_right,
            color: Colors.white,
            size: 18.sp,
          ),
        ],
      ),
    );
  }

  /// 构建用户区域
  Widget _buildUserSection(
    final UserRankingState state,
    final dynamic userInfo,
    final int languageId,
  ) {
    return Row(
      children: [
        // 用户头像
        CircleAvatar(
          radius: 20.r,
          backgroundColor: Colors.white.withOpacity(0.2),
          child: CircleAvatar(
            radius: 20.r,
            backgroundImage: _getUserAvatar(userInfo),
            child: _getUserAvatar(userInfo) == null
                ? Icon(
                    Icons.person,
                    size: 20.sp,
                    color: Colors.grey.shade400,
                  )
                : null,
          ),
        ),
        SizedBox(width: 8.h),

        // 用户名称或排名信息
        if (state.status != RankingStatus.inProgress) ...[
          // 显示用户名
          Text(
            _getUserDisplayName(userInfo, languageId),
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ] else if (state.status == RankingStatus.inProgress) ...[
          // 显示排名信息
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                S.current.ranking_ranking,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 17.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                state.currentRanking == 0
                    ? (languageId == 1 ? 'يوق' : '没有')
                    : '${state.currentRanking}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 22.sp,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.italic,
                ),
              ),
              SizedBox(width: 10.w),
              Image.asset(
                'assets/images/ranking/crown.png',
                width: 20.w,
                height: 20.h,
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 获取用户显示名称
  String _getUserDisplayName(final dynamic userInfo, final int languageId) {
    final userName = userInfo?.name ?? '';
    if (userName == '微信用户') {
      return languageId == 1 ? 'ئادەتتىكى ئابونت' : userName;
    }
    return userName.isNotEmpty
        ? userName
        : (languageId == 1 ? 'ئىشلەتكۈچى' : '用户');
  }

  /// 获取用户头像
  ImageProvider? _getUserAvatar(final dynamic userInfo) {
    final userName = userInfo?.name ?? '';
    final userAvatar = userInfo?.avatar ?? '';

    if (userName == '微信用户') {
      return const NetworkImage(
        'https://mulazim-5gp50e7zf7ca-7bwa2af2e78-**********.tcloudbaseapp.com/img/agentAvatar.png',
      );
    }

    if (userAvatar.isNotEmpty) {
      return CachedNetworkImageProvider(userAvatar);
    }

    return null;
  }
}
