import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_controller.dart';

import 'ranking_header.dart';
import 'ranking_tabs.dart';

/// 排行榜列表容器组件
class RankingListContainer extends ConsumerWidget {
  const RankingListContainer({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final state = ref.watch(userRankingControllerProvider);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFFC0AF),
        borderRadius: BorderRadius.all(
          Radius.circular(12.r),
        ),
      ),
      child: <PERSON><PERSON>(
        children: [
          // 装饰圆形元素
          Positioned(
            right: -25.w,
            top: -20.h,
            child: Container(
              width: 100.w,
              height: 100.h,
              decoration: const BoxDecoration(
                color: Color(0x7FFFA58F),
                shape: BoxShape.circle,
                backgroundBlendMode: BlendMode.multiply,
              ),
            ),
          ),
          Positioned(
            left: -20.w,
            top: 20.h,
            child: Container(
              width: 70.w,
              height: 70.h,
              decoration: const BoxDecoration(
                color: Color(0xFFFFA68F),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部区域
              const RankingHeader(),

              // 内容区域
              RankingTabs(
                giftList: state.giftList,
                rankingUsers: state.rankingUsers,
                languageId: languageId,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
