import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';

import 'ranking_user_item.dart';

/// 用户排行榜列表组件
class RankingUserList extends StatelessWidget {
  final List<RankingUserInfo> users;
  final int languageId;

  const RankingUserList({
    super.key,
    required this.users,
    required this.languageId,
  });

  @override
  Widget build(final BuildContext context) {
    if (users.isEmpty) {
      return Center(
        child: Text(
          languageId == 1 ? 'ھازىرچە قاتناشقان ئادەم يوق' : '还没有参加的人',
          style: TextStyle(
            color: const Color(0xFF9DA0C2),
            fontSize: 14.sp,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: users.length,
      itemBuilder: (final context, final index) {
        final user = users[index];
        return RankingUserItem(
          user: user,
          index: index,
          languageId: languageId,
        );
      },
    );
  }
}
