import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 用户排行榜项目组件
class RankingUserItem extends StatelessWidget {
  final RankingUserInfo user;
  final int index;
  final int languageId;

  const RankingUserItem({
    super.key,
    required this.user,
    required this.index,
    required this.languageId,
  });

  @override
  Widget build(final BuildContext context) {
    final isTopThree = index < 3;

    return Directionality(
      textDirection: languageId == 2 ? TextDirection.rtl : TextDirection.ltr,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
        child: Column(
          children: [
            // 用户信息主体
            Row(
              children: [
                // 消费金额
                SizedBox(
                  width: 80.w,
                  child: Text(
                    '¥${user.totalPrice?.toString() ?? '0'}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: isTopThree
                          ? const Color(0xFFFF5545)
                          : const Color(0xFF333333),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // 用户信息区域
                Expanded(
                  child: Row(
                    children: [
                      // 用户信息文本
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              user.name ??
                                  (languageId == 1
                                      ? 'ئادەتتىكى ئابونت'
                                      : '微信用户'),
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: isTopThree
                                    ? const Color(0xFFFF5545)
                                    : const Color(0xFF333333),
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              user.mobile ?? '',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: isTopThree
                                    ? const Color(0xFFFF5545)
                                    : const Color(0xFF333333),
                              ),
                              textDirection: TextDirection.ltr,
                              textAlign: languageId == 1
                                  ? TextAlign.right
                                  : TextAlign.left,
                            ),
                          ],
                        ),
                      ),

                      SizedBox(width: 10.w),

                      // 用户头像
                      Container(
                        width: 60.w,
                        height: 60.h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          // border: isTopThree
                          //     ? Border.all(
                          //         color: const Color(0xFFFF5545),
                          //         width: 2.w,
                          //       )
                          //     : null,
                        ),
                        child: CircleAvatar(
                          radius: 25.r,
                          backgroundImage: user.avatar != null
                              ? CachedNetworkImageProvider(user.avatar!)
                              : null,
                          child: user.avatar == null
                              ? Icon(
                                  Icons.person,
                                  size: 20.sp,
                                  color: Colors.grey.shade400,
                                )
                              : null,
                        ),
                      ),

                      SizedBox(width: 10.w),

                      // 排名
                      Column(
                        children: [
                          Text(
                            '${index + 1}',
                            style: TextStyle(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.bold,
                              fontStyle: FontStyle.italic,
                              color: isTopThree
                                  ? const Color(0xFFFF5545)
                                  : AppColors.textHintColor,
                            ),
                          ),
                          ShaderMask(
                            shaderCallback: (final bounds) => LinearGradient(
                              colors: const [
                                Color(0xFF9DA0C2), // rgb(157, 160, 194)
                                Color(0xFF929292), // rgb(146, 146, 146)
                              ],
                              begin: const Alignment(-0.4, -0.9), // 对应194.40度
                              end: const Alignment(0.4, 0.9),
                            ).createShader(bounds),
                            child: Text(
                              S.current.ranking_user_rank,
                              style: TextStyle(
                                fontSize: 20.sp,
                                fontStyle: FontStyle.italic,
                                fontWeight: FontWeight.bold,
                                color: Colors.white, // 用白色作为遮罩的基色
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // 获奖信息（如果有）
            if (user.giftName?.isNotEmpty == true) ...[
              SizedBox(height: 4.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: AppColors.baseBackgroundColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(15.r),
                    bottomRight: Radius.circular(15.r),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      S.current.ranking_reward_fix,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.redColor,
                      ),
                    ),
                    SizedBox(width: 5.w),
                    Expanded(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Flexible(
                            child: Text(
                              textDirection: languageId == 1
                                  ? TextDirection.rtl
                                  : TextDirection.ltr,
                              user.giftName!,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColors.textPrimaryColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          SizedBox(width: 5.w),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4.r),
                            child: SizedBox(
                              height: 40.h,
                              child: CachedNetworkImage(
                                imageUrl: user.giftImage!,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
