import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/config/index.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_controller.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_state.dart';

/// 中间内容组件（进度条等）
class MiddleContent extends ConsumerStatefulWidget {
  const MiddleContent({super.key});

  @override
  ConsumerState<MiddleContent> createState() => _MiddleContentState();
}

class _MiddleContentState extends ConsumerState<MiddleContent> {
  final GlobalKey _progressBarKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // 在下一帧计算进度条宽度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateProgressBarWidth();
    });
  }

  /// 动态计算进度条宽度
  void _calculateProgressBarWidth() {
    final RenderBox? renderBox =
        _progressBarKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox != null) {
      final containerWidth = renderBox.size.width;
      final state = ref.read(userRankingControllerProvider);
      final calculatedWidth = UserRankingState.calculateProgressBarWidth(
        state.rankingData,
        containerWidth,
      );

      if (calculatedWidth != null) {
        ref
            .read(userRankingControllerProvider.notifier)
            .setProgressBarWidth(calculatedWidth);
      }
    }
  }

  @override
  Widget build(final BuildContext context) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final state = ref.watch(userRankingControllerProvider);
    final localStorageRepository = ref.watch(localStorageRepositoryProvider);
    final userInfo = localStorageRepository.getUserInfo();

    // 监听数据变化，重新计算进度条宽度
    ref.listen<UserRankingState>(userRankingControllerProvider,
        (previous, next) {
      if (previous?.rankingData != next.rankingData) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _calculateProgressBarWidth();
        });
      }
    });

    return Column(
      children: [
        // 价格提示信息
        _buildPriceTips(state, languageId),
        SizedBox(height: 15.h),

        // 进度条
        _buildProgressBar(state, userInfo, languageId),

        // 底部提示（仅在Status 2且有剩余金额时显示）
        if (state.status == RankingStatus.inProgress &&
            state.userRemainingAmount != '0') ...[
          SizedBox(height: 8.h),
          Text(
            S.current.ranking_card_title2
                .replaceAll('%s', state.userRemainingAmount),
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// 构建价格提示信息
  Widget _buildPriceTips(final UserRankingState state, final int languageId) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 总金额信息
        Directionality(
          textDirection: TextDirection.rtl,
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '¥${state.userAmount}',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 22.sp,
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                TextSpan(
                  text: ' ',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 17.sp,
                  ),
                ),
                TextSpan(
                  text: S.current.ranking_total_price,
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: AppConstants.mainFont,
                    fontWeight: FontWeight.w600,
                    fontSize: 17.sp,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Status 2: 剩余金额 | Status 4: 用户排名
        if (state.status == RankingStatus.inProgress &&
            state.userRemainingAmount != '0') ...[
          Directionality(
            textDirection: TextDirection.rtl,
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '¥${state.userRemainingAmount}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22.sp,
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  TextSpan(
                    text: ' ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 17.sp,
                    ),
                  ),
                  TextSpan(
                    text: S.current.ranking_remain,
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: AppConstants.mainFont,
                      fontWeight: FontWeight.w600,
                      fontSize: 17.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ] else if (state.status == RankingStatus.winner &&
            state.currentRanking > 0) ...[
          Directionality(
            textDirection:
                languageId == 1 ? TextDirection.rtl : TextDirection.ltr,
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '${state.currentRanking}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22.sp,
                      fontWeight: FontWeight.bold,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  TextSpan(
                    text: ' ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 17.sp,
                    ),
                  ),
                  TextSpan(
                    text: S.current.ranking_user_rank,
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: AppConstants.mainFont,
                      fontWeight: FontWeight.bold,
                      fontSize: 17.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建进度条
  Widget _buildProgressBar(
    final UserRankingState state,
    final dynamic userInfo,
    final int languageId,
  ) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Container(
        key: _progressBarKey,
        width: double.infinity,
        height: 30.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.r),
        ),
        padding: EdgeInsets.all(3.w),
        child: Stack(
          children: [
            // 奖品图标（左侧外部）
            Positioned(
              left: 5.w,
              top: 0,
              bottom: 0,
              child: Container(
                width: 30.w,
                height: 30.h,
                margin: EdgeInsets.symmetric(vertical: 4.h),
                child: Image.asset(
                  'assets/images/ranking/prize.png',
                  width: 30.w,
                  height: 30.h,
                ),
              ),
            ),

            // 动态进度条（从右侧开始）
            Positioned(
              right: 0.w,
              top: 0.h,
              bottom: 0.h,
              child: AnimatedContainer(
                duration: const Duration(seconds: 1),
                width:
                    (state.progressBarWidth ?? _getMinWidth(state.userAmount))
                        .clamp(_getMinWidth(state.userAmount),
                            double.infinity), // 根据用户金额设置最小宽度
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFF6F47), Color(0xFFFF4B44)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 用户头像
                    Container(
                      width: 25.w,
                      height: 25.h,
                      margin: EdgeInsets.all(2.w),
                      child: CircleAvatar(
                        radius: 30.r,
                        backgroundColor: Colors.white.withOpacity(0.2),
                        child: CircleAvatar(
                          radius: 30.r,
                          backgroundImage: _getUserAvatar(userInfo),
                          child: _getUserAvatar(userInfo) == null
                              ? Icon(
                                  Icons.person,
                                  size: 12.sp,
                                  color: Colors.grey.shade400,
                                )
                              : null,
                        ),
                      ),
                    ),
                    if (state.userAmount != '0') ...[
                      // 闪电图标
                      Container(
                        width: 13.w,
                        height: 13.h,
                        margin: EdgeInsets.only(right: 3.w),
                        child: Icon(
                          Icons.flash_on,
                          color: Colors.white,
                          size: 13.sp,
                        ),
                      ),

                      // 金额显示
                      Flexible(
                        child: Padding(
                          padding: EdgeInsets.only(right: 5.w),
                          child: Text(
                            '¥${state.userAmount}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              fontStyle: FontStyle.italic,
                            ),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取用户头像
  ImageProvider? _getUserAvatar(final dynamic userInfo) {
    final userName = userInfo?.name ?? '';
    final userAvatar = userInfo?.avatar ?? '';

    if (userName == '微信用户') {
      return const NetworkImage(
        'https://mulazim-5gp50e7zf7ca-7bwa2af2e78-**********.tcloudbaseapp.com/img/agentAvatar.png',
      );
    }

    if (userAvatar.isNotEmpty) {
      return CachedNetworkImageProvider(userAvatar);
    }

    return null;
  }

  /// 根据用户金额获取进度条最小宽度
  double _getMinWidth(final String userAmount) {
    final amount = double.tryParse(userAmount) ?? 0;
    return amount > 0 ? 80.w : 30.w;
  }
}
