import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart' as intl show DateFormat;
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 卡片组件
Widget cardWidget({required final Widget child}) {
  return Container(
    width: double.infinity,
    padding: EdgeInsets.all(10.w),
    margin: EdgeInsets.fromLTRB(7.w, 7.w, 7.w, 0),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(7),
    ),
    child: child,
  );
}

/// 排行榜奖品项
/// [imageUrl] 图片地址
/// [title] 标题
/// [subtitle] 副标题
/// [price] 价格
/// [rank] 排名
Widget rankingPrizeItem(
    {required final String imageUrl,
    required final String title,
    required final String subtitle,
    required final String price,
    required final String rank}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(5.w),
            child: Image.network(
              imageUrl,
              width: 72.w,
              height: 72.w,
              fit: BoxFit.cover,
              errorBuilder: (final context, final error, final stackTrace) {
                return Container(
                  width: 72.w,
                  height: 72.w,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(5.w),
                  ),
                  child: Icon(Icons.image_not_supported_outlined, size: 24, color: Colors.grey.shade400),
                );
              },
            ),
          ),
          SizedBox(width: 20.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title,
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black)),
              SizedBox(height: 10.w),
              Text(subtitle,
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black)),
              SizedBox(height: 10.w),
              Row(
                children: [
                  Text(S.current.original_price,
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: Color(0xff9DA0C2))),
                  SizedBox(width: 10.w),
                  Text(price,
                      style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: Color(0xff9DA0C2),
                          decoration: TextDecoration.lineThrough,
                          decorationColor: Color(0xff9DA0C2),
                          ),
                        ),
                ],
              ),
            ],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              rank,
              style: TextStyle(
                fontSize: 36.sp,
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.italic,
                color: Color(0xFFFF4C45),
              ),
            ),
          Text(S.current.level,
              style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.black)),

        ],
      )
    ],
  );
}

/// 底部弹出框标题
/// [context] 上下文
/// [title] 标题
Widget bottomSheetTitle(final BuildContext context, final String title) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      InkWell(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          width: 50,
          height: 50,
          padding: EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Container(
            padding: EdgeInsets.all(1),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(40),
              border: Border.all(color: Colors.black, width: 1),
            ),
            child: Icon(Icons.close_rounded, size: 20),
          ),
        ),
      ),
      Text(title,
          style: TextStyle(
              fontSize: 18, fontWeight: FontWeight.w500, color: Colors.black)),
      SizedBox(width: 40),
    ],
  );
}

/// 排行榜底部弹出框
/// [context] 上下文
/// [child] 子组件
Future<void> rankingbottomSheet({
  required final BuildContext context, 
  required final Widget child, 
  required final String title
}) {
  return showModalBottomSheet(
      backgroundColor: Colors.transparent,
      context: context,
      isScrollControlled: true,
      builder: (final context) => Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            bottomSheetTitle(context, title),
            Container(
              width: double.infinity, 
              height: 1, 
              color: Colors.grey.shade200,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: child,
              ),
            ),
          ],
        ),
      ),
    );
}


  /// 活动规则内容
  Widget ruleContent(final RankingActivity rankingData, final WidgetRef ref) {
    final beginTime = intl.DateFormat('yyyy/MM/dd')
        .format(DateTime.parse(rankingData.rankingBeginTime ?? ''));
    final endTime = intl.DateFormat('yyyy/MM/dd')
        .format(DateTime.parse(rankingData.rankingEndTime ?? ''));
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(S.current.rule,
            style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black)),
        SizedBox(height: 8.w),
        Text(
          ref.watch(languageProvider) == 'ug'
              ? (rankingData.rankingRuleUg ?? '')
              : (rankingData.rankingRuleZh ?? ''),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          "${S.current.activity_time}: $beginTime ~ $endTime",
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 活动奖品列别表内容
  Widget rulePrizeContent(final RankingActivity rankingData, final WidgetRef ref) {
    final language = ref.watch(languageProvider);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _rulePrizeTitle(),
        SizedBox(height: 10.w),
        if (rankingData.gift != null && rankingData.gift!.isNotEmpty)
          ...rankingData.gift!.map(
            (final prize) => Container(
              margin: EdgeInsets.only(bottom: 10.w),
              padding: EdgeInsets.only(bottom: 10.w),
              decoration: BoxDecoration(
                border: Border(
                    bottom:
                        BorderSide(color: Color(0xffEFF1F6), width: 1)),
              ),
              child: rankingPrizeItem(
                  imageUrl: prize.rewardImage ?? '',
                  title: language == 'ug' ? prize.rewardNameUg ?? '' : prize.rewardNameZh ?? '',
                  subtitle: '${S.current.ranking_count} ${prize.rewardCount}',
                  price: prize.rewardOldPrice ?? '',
                  rank: prize.rewardRank.toString()),
            ),
          ),
      ],
    );
  }

  Widget _rulePrizeTitle() {
    return Container(
      margin: EdgeInsets.only(top: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset('assets/images/ranking/rule_prize_bg.png',
              width: 24.w, height: 24.w),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 15.w),
            child: Text(
              S.current.prize_list,
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black),
            ),
          ),
          Image.asset('assets/images/ranking/rule_prize_bg.png',
              width: 24.w, height: 24.w),
        ],
      ),
    );
  }
