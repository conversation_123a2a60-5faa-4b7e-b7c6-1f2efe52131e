import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';

import 'ranking_prize_item.dart';

/// 奖品列表组件
class RankingPrizeList extends StatelessWidget {
  final List<RankingGift> gifts;
  final int languageId;

  const RankingPrizeList({
    super.key,
    required this.gifts,
    required this.languageId,
  });

  @override
  Widget build(final BuildContext context) {
    if (gifts.isEmpty) {
      return Center(
        child: Text(
          languageId == 1 ? 'مۇكاپات يوق' : '暂无奖品',
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14.sp,
          ),
        ),
      );
    }

    return ListView.separated(
      itemCount: gifts.length,
      separatorBuilder: (final context, final index) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        child: const Divider(
          height: 1,
          thickness: 1,
          color: AppColors.dividerColor,
        ),
      ),
      itemBuilder: (final context, final index) {
        final gift = gifts[index];
        return RankingPrizeItem(
          gift: gift,
          languageId: languageId,
        );
      },
    );
  }
}
