import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 排行榜活动数据模型
class RankingCardData {
  final String imageUrl;
  final String title;
  final Map<String, dynamic> extraData;

  RankingCardData({
    required this.imageUrl,
    required this.title,
    required this.extraData,
  });
}

/// 排行榜活动入口卡片
class RankingCardWidget extends StatefulWidget {
  /// 构造函数
  const RankingCardWidget({
    super.key,
    required this.rankingList,
    required this.onTap,
    required this.onClose,
    required this.onShow,
    required this.isHidden,
  });

  /// 排行榜数据列表
  final List<dynamic> rankingList;

  /// 点击事件 - 接收处理后的参数
  final Function(Map<String, dynamic>) onTap;

  /// 关闭事件
  final VoidCallback onClose;

  /// 显示事件
  final VoidCallback onShow;

  /// 是否隐藏
  final bool isHidden;

  @override
  State<RankingCardWidget> createState() => _RankingCardWidgetState();
}

class _RankingCardWidgetState extends State<RankingCardWidget> {
  late PageController _pageController;
  Timer? _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startAutoScroll();
  }

  @override
  void dispose() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _pageController.dispose();
    super.dispose();
  }

  /// 开始自动滚动
  void _startAutoScroll() {
    if (widget.rankingList.length <= 1) return;

    _timer = Timer.periodic(Duration(seconds: 3), (timer) {
      if (mounted) {
        _currentIndex = (_currentIndex + 1) % widget.rankingList.length;
        _pageController.animateToPage(
          _currentIndex,
          duration: Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  /// 获取当前活动数据
  RankingCardData _getCurrentData() {
    if (widget.rankingList.isEmpty) {
      return RankingCardData(
        imageUrl: '',
        title: '',
        extraData: {},
      );
    }

    final currentItem = widget.rankingList[_currentIndex];
    return RankingCardData(
      imageUrl: currentItem['image'] ?? '',
      title: currentItem['name'] ?? '',
      extraData: currentItem,
    );
  }

  /// 处理点击事件，统一处理不同数据格式
  void _handleTap(dynamic item) {
    Map<String, dynamic> extraData = {};

    if (item is Map<String, dynamic>) {
      // 餐厅详情页面的数据格式
      extraData = {
        'areaId': item['area_id'] ?? 0,
        'restaurantId': item['restaurant_id'] ?? 0,
        'rankingId': item['id'] ?? 0,
        'type': item['ranking_type'] ?? 1,
      };
    } else {
      // 首页的数据格式 - 尝试访问对象属性
      try {
        extraData = {
          'areaId': item.areaId ?? 0,
          'restaurantId': item.restaurantId ?? 0,
          'rankingId': item.id ?? 0,
          'type': item.rankingType ?? 1,
        };
      } catch (e) {
        // 如果无法访问属性，使用默认值
        extraData = {
          'areaId': 0,
          'restaurantId': 0,
          'rankingId': 0,
          'type': 1,
        };
      }
    }

    widget.onTap(extraData);
  }

  @override
  Widget build(BuildContext context) {
    return _buildCard();
  }

  /// 构建卡片
  Widget _buildCard() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        width: 115.w,
        height: 155.w,
        decoration: BoxDecoration(
          color: Color(0xFFFF4C45),
          borderRadius: BorderRadius.circular(10),
          gradient: LinearGradient(
            colors: [
              Color(0xFFFF4C45),
              Color(0xFFFF6F47),
              Color(0xFFFF4B44),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: _buildCardContent(),
      ),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent() {
    return Stack(
      children: [
        // 轮播内容 - 始终显示
        PageView.builder(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          itemCount: widget.rankingList.length,
          itemBuilder: (context, index) {
            final item = widget.rankingList[index];
            return _buildCardItem(item);
          },
        ),
        // 关闭按钮
        if (!widget.isHidden)
          Positioned(
            top: 10.w,
            right: 10.w,
            child: GestureDetector(
              onTap: widget.onClose,
              child: Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 16.w,
                  ),
                ),
              ),
            ),
          ),
        // 指示器
        if (widget.rankingList.length > 1)
          Positioned(
            top: 15.w,
            left: 15.w,
            child: Row(
              children: List.generate(
                widget.rankingList.length,
                (index) => Container(
                  width: 6.w,
                  height: 6.w,
                  margin: EdgeInsets.only(right: 4.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? Colors.white
                        : Colors.white.withOpacity(0.5),
                  ),
                ),
              ),
            ),
          ),
        _buildCardBottom(),
      ],
    );
  }

  /// 构建单个卡片项
  Widget _buildCardItem(dynamic item) {
    // 处理不同的数据类型
    String imageUrl = '';
    String title = '';

    if (item is Map<String, dynamic>) {
      // 如果是Map类型（餐厅详情页面）
      imageUrl = item['image'] ?? '';
      title = item['name'] ?? '';
    } else {
      // 如果是对象类型（首页）
      try {
        imageUrl = item.image ?? '';
        title = item.name ?? '';
      } catch (e) {
        // 如果无法访问属性，尝试转换为Map
        final itemMap = item as Map<String, dynamic>?;
        if (itemMap != null) {
          imageUrl = itemMap['image'] ?? '';
          title = itemMap['name'] ?? '';
        }
      }
    }

    return InkWell(
      onTap: () => _handleTap(item),
      child: Container(
        width: 100.w,
        height: 150.w,
        margin: EdgeInsets.fromLTRB(7.w, 7.w, 7.w, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            // 图片区域 - 固定高度，添加圆角
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(5),
                topRight: Radius.circular(5),
              ),
              child: Container(
                width: 110.w,
                height: 72.w,
                child: imageUrl.isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: imageUrl,
                        width: 110.w,
                        height: 72.w,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 100.w,
                          height: 72.w,
                          color: Colors.grey[200],
                          child: Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.grey[400]!),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) {
                          return Container(
                            width: 100.w,
                            height: 72.w,
                            color: Colors.grey[200],
                            child: Icon(
                              Icons.image_not_supported_outlined,
                              color: Colors.grey[400],
                              size: 24.w,
                            ),
                          );
                        },
                      )
                    : Container(
                        width: 100.w,
                        height: 72.w,
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.image_not_supported_outlined,
                          color: Colors.grey[400],
                          size: 24.w,
                        ),
                      ),
              ),
            ),
            // 标题区域
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(5.0),
                child: Text(
                  title.isNotEmpty ? title : '暂无标题',
                  maxLines: 2,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建卡片底部
  Widget _buildCardBottom() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 54.w,
        width: 110.w,
        decoration: BoxDecoration(
          // 设置一个背景图片
          image: DecorationImage(
            image: AssetImage(
              'assets/images/ranking/ranking_card_bg.png',
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Center(
          child: Container(
            margin: EdgeInsets.only(top: 10.w, left: 5.w, right: 5.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 隐藏时显示左箭头按钮
                if (widget.isHidden)
                  Padding(
                    padding: EdgeInsets.only(right: 2.w),
                    child: GestureDetector(
                      onTap: widget.onShow,
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: 24.w,
                        color: Colors.white,
                      ),
                    ),
                  ),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (widget.rankingList.isNotEmpty) {
                        final currentItem = widget.rankingList[_currentIndex];
                        _handleTap(currentItem);
                      }
                    },
                    child: Text(
                      S.current.receive_prize,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis, // 文字超出时显示省略号
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                SizedBox(
                  width: 5.w,
                ),
                // 显示时显示右箭头按钮
                if (!widget.isHidden)
                  GestureDetector(
                    onTap: widget.onClose,
                    child: Icon(
                      Icons.arrow_forward_ios,
                      size: 20.w,
                      color: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
