import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 排行榜头部组件
class RankingHeader extends StatelessWidget {
  const RankingHeader({super.key});

  @override
  Widget build(final BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 标题
            Text(
              S.current.ranking_list_title,
              style: TextStyle(
                color: const Color(0xFFFF5C29),
                fontSize: 25.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            // 奖杯图标
            Transform.translate(
              offset: Offset(-10.w, 10.w),
              child: Image.asset(
                'assets/images/ranking/cup.png',
                width: 90.w,
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
