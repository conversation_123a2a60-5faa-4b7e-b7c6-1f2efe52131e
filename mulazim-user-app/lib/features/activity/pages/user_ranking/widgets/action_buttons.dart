import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/generated/l10n.dart';
import '../user_ranking_controller.dart';
import '../user_ranking_state.dart';

/// 操作按钮组件
class ActionButtons extends ConsumerWidget {
  const ActionButtons({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final state = ref.watch(userRankingControllerProvider);
    final controller = ref.read(userRankingControllerProvider.notifier);

    return Column(
      children: [
        // 添加状态为4且确认地址的提示文字
        if (state.status == RankingStatus.winner &&
            state.rankingData?.ranking?.confirmed == true) ...[
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 25.h),
            child: Text(
              S.current.ranking_give_confirm_address,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        // 按钮区域
        _buildButtonsForStatus(state.status, languageId, controller, state),
      ],
    );
  }

  /// 根据状态构建按钮
  Widget _buildButtonsForStatus(
    final RankingStatus status,
    final int languageId,
    final UserRankingController controller,
    final UserRankingState state,
  ) {
    switch (status) {
      case RankingStatus.notStarted: // status == 1
        return _buildNotStartedButtons(languageId, controller, state);
      case RankingStatus.inProgress: // status == 2
      case RankingStatus.ended: // status == 3
      case RankingStatus.loser: // status == 5
        return _buildCommonButtons(languageId, controller);
      case RankingStatus.winner: // status == 4
        return _buildWinnerButtons(languageId, controller, state);
      case RankingStatus.userError: // status == 6
        return _buildUserErrorButtons(languageId, controller);
    }
  }

  /// 未开始状态的按钮 (status == 1)
  Widget _buildNotStartedButtons(
    final int languageId,
    final UserRankingController controller,
    final UserRankingState state,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 参加排行榜按钮
        SizedBox(
          width: 0.7.sw,
          child: _buildPrimaryButton(
            text: S.current.ranking_btn1, // "我要参加"
            onPressed: state.isJoining ? null : () => controller.joinRanking(),
            isLoading: state.isJoining,
          ),
        ),
      ],
    );
  }

  /// 进行中、已结束未获奖状态的按钮 (status == 2, 3, 5)
  Widget _buildCommonButtons(
    final int languageId,
    final UserRankingController controller,
  ) {
    return _buildPrimaryButton(
      text: S.current.ranking_btn2, // "订餐"
      onPressed: () => controller.goToPage(),
    );
  }

  /// 获奖状态的按钮 (status == 4)
  Widget _buildWinnerButtons(
    final int languageId,
    final UserRankingController controller,
    final UserRankingState state,
  ) {
    // 检查是否已确认地址
    final bool isConfirmed = state.rankingData?.ranking?.confirmed == true;

    // 如果还未确认地址，显示兑换按钮
    if (!isConfirmed) {
      return _buildPrimaryButton(
        text: S.current.ranking_btn3, // "兑换"
        onPressed: () => controller.goToRankingGive(),
      );
    }

    // 如果已确认地址，不显示按钮或显示其他状态
    return const SizedBox.shrink();
  }

  /// 用户错误状态的按钮 (status == 6)
  Widget _buildUserErrorButtons(
    final int languageId,
    final UserRankingController controller,
  ) {
    return _buildPrimaryButton(
      text: S.current.ranking_btn4, // "返回首页"
      onPressed: () => controller.goToHomePage(),
    );
  }

  /// 主要按钮
  Widget _buildPrimaryButton({
    required final String text,
    required final VoidCallback? onPressed,
    final bool isLoading = false,
  }) {
    return Container(
      width: double.infinity,
      height: 45.h,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFB479), Color(0xFFFFF4AB), Color(0xFFFFAA6E)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(50.r),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50.r),
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                text,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}
