import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/data/models/activity/ranking_model.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/activity/pages/user_ranking/user_ranking_controller.dart';

import 'ranking_prize_list.dart';
import 'ranking_user_list.dart';

/// 排行榜标签页组件
class RankingTabs extends ConsumerStatefulWidget {
  final List<RankingGift> giftList;
  final List<RankingUserInfo> rankingUsers;
  final int languageId;

  const RankingTabs({
    super.key,
    required this.giftList,
    required this.rankingUsers,
    required this.languageId,
  });

  @override
  ConsumerState<RankingTabs> createState() => _RankingTabsState();
}

class _RankingTabsState extends ConsumerState<RankingTabs>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 10.h),
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(12.r),
          ),
        ),
        child: Column(
          children: [
            // 标签页头部
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 20.r),
              child: Row(
                children: [
                  // 标签按钮 - 使用固定宽度
                  SizedBox(
                    width: 180.w, // 给TabBar固定宽度
                    height: 35.h,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFEFF1F6),
                        borderRadius: BorderRadius.circular(35.r),
                      ),
                      child: TabBar(
                        controller: _tabController,
                        labelColor: Colors.white,
                        unselectedLabelColor: const Color(0xFF9DA0C2),
                        indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(35.r),
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFF6F47), Color(0xFFFF4B44)],
                          ),
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        dividerColor: Colors.transparent,
                        labelStyle: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            fontFamily: AppConstants.mainFont),
                        unselectedLabelStyle: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.normal,
                            fontFamily: AppConstants.mainFont),
                        tabs: [
                          Tab(text: S.current.ranking_rank_text),
                          Tab(text: S.current.ranking_prize),
                        ],
                      ),
                    ),
                  ),

                  const Spacer(),

                  // 活动规则按钮
                  GestureDetector(
                    onTap: () => ref
                        .read(userRankingControllerProvider.notifier)
                        .showRankingRule(ref: ref),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.black,
                          size: 18.sp,
                        ),
                        SizedBox(width: 5.w),
                        Text(
                          S.current.ranking_rule_text,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 15.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 标签页内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // 排行榜列表
                  RankingUserList(
                    users: widget.rankingUsers,
                    languageId: widget.languageId,
                  ),
                  // 奖品列表
                  RankingPrizeList(
                    gifts: widget.giftList,
                    languageId: widget.languageId,
                  ),
                ],
              ),
            ),

            SizedBox(height: 20.h), // 底部间距
          ],
        ),
      ),
    );
  }
}
