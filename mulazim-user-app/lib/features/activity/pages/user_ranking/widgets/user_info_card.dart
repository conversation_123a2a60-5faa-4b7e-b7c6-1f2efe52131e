import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/generated/l10n.dart';

import '../user_ranking_controller.dart';
import '../user_ranking_state.dart';
import 'user_line.dart';
import 'middle_content.dart';
import 'countdown_widget.dart';
import 'action_buttons.dart';

/// 用户信息卡片组件
class UserInfoCard extends ConsumerWidget {
  const UserInfoCard({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final languageId = ref
        .watch(languageProvider.select((final lang) => lang == 'ug' ? 1 : 2));
    final state = ref.watch(userRankingControllerProvider);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(15.r),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFF4C45), Color(0xFFFF6F47), Color(0xFFFF4B44)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 顶部用户信息行（除了Status 4外都显示）
          if (_shouldShowTopUserLine(state.status)) ...[
            const UserLine(),
            SizedBox(height: 15.h),
          ],

          // 状态标题（仅在特定状态显示：1, 3, 5, 6）
          if (_shouldShowStatusTitle(state.status)) ...[
            Text(
              _getStatusTitle(state.status),
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20.h),
          ],

          // 中间内容（进度条等）- Status 2 和 4 时显示
          if (state.shouldShowProgressBar) ...[
            const MiddleContent(),
            SizedBox(height: 15.h),
          ],

          // 倒计时 - Status 1 和 2 时显示
          if (state.shouldShowCountdown) ...[
            const CountdownWidget(),
            SizedBox(height: 15.h),
          ],
          SizedBox(height: 15.h),

          // 操作按钮
          const ActionButtons(),

          // 底部用户信息行（仅Status 4时显示）
          if (state.status == RankingStatus.winner) ...[
             SizedBox(height: 15.h),
            const UserLine(),
          ],
        ],
      ),
    );
  }

  /// 是否显示顶部用户信息行
  /// 对应原逻辑：wx:if="{{ status == 1 || status == 2 || status == 3 || status == 5 || status == 6 }}"
  bool _shouldShowTopUserLine(final RankingStatus status) {
    return status == RankingStatus.notStarted ||
        status == RankingStatus.inProgress ||
        status == RankingStatus.ended ||
        status == RankingStatus.loser ||
        status == RankingStatus.userError;
  }

  /// 是否显示状态标题
  /// 对应原逻辑：只在 status == 1, 3, 5, 6 时显示，Status 2 和 4 不显示标题
  bool _shouldShowStatusTitle(final RankingStatus status) {
    return status == RankingStatus.notStarted ||
        status == RankingStatus.ended ||
        status == RankingStatus.loser ||
        status == RankingStatus.userError;
  }

  /// 获取状态标题
  String _getStatusTitle(final RankingStatus status) {
    // 根据状态显示对应的文案，与微信小程序保持一致
    switch (status) {
      case RankingStatus.notStarted:
        return S.current.ranking_card_title1;
      case RankingStatus.ended:
        return S.current.ranking_card_title3;
      case RankingStatus.loser:
        return S.current.ranking_card_title5;
      case RankingStatus.userError:
        return S.current.ranking_card_title6;
      default:
        return '';
    }
  }
}
