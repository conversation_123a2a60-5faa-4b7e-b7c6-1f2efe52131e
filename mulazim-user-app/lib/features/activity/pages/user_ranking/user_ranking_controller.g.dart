// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_ranking_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userRankingControllerHash() =>
    r'7e66cf56abc21d56c65a5a30a299f47ee251bf9c';

/// 用户排行榜控制器
///
/// Copied from [UserRankingController].
@ProviderFor(UserRankingController)
final userRankingControllerProvider = AutoDisposeNotifierProvider<
    UserRankingController, UserRankingState>.internal(
  UserRankingController.new,
  name: r'userRankingControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRankingControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserRankingController = AutoDisposeNotifier<UserRankingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
