import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'user_ranking_controller.dart';

import 'widgets/ranking_background.dart';
import 'widgets/winner_card.dart';
import 'widgets/user_info_card.dart';
import 'widgets/ranking_list_container.dart';

/// 用户排行榜页面参数
class RankingPageParams {
  final String? rankingId;
  final String? type;
  final String? restaurantId;
  final String? areaId;

  const RankingPageParams({
    this.rankingId,
    this.type,
    this.restaurantId,
    this.areaId,
  });
}

/// 用户排行榜页面
class UserRankingPage extends ConsumerStatefulWidget {
  /// 页面参数
  final Map<String, dynamic> params;

  const UserRankingPage({
    super.key,
    required this.params,
  });

  @override
  ConsumerState<UserRankingPage> createState() => _UserRankingPageState();
}

class _UserRankingPageState extends ConsumerState<UserRankingPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      ref.read(userRankingControllerProvider.notifier).initializeData(
            rankingId: widget.params['rankingId']?.toString(),
            areaId: widget.params['areaId']?.toString(),
            restaurantId: widget.params['restaurantId']?.toString(),
            type: widget.params['type']?.toString(),
          );
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Stack(
      children: [
        // 背景层
        const Positioned.fill(
          child: RankingBackground(),
        ),

        // 主要内容
        Scaffold(
          backgroundColor: Colors.transparent,
          extendBodyBehindAppBar: false,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            scrolledUnderElevation: 0,
            elevation: 0,
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(50.h),
              child: Container(
                color: Colors.red,
              ),
            ),
          ),
          body: NestedScrollView(
            controller: _scrollController,
            headerSliverBuilder: (final context, final innerBoxScrolled) {
              return [
                // 获奖卡片（仅在Status 4且有获奖信息时显示）
                SliverToBoxAdapter(
                  child: Consumer(
                    builder: (final context, final ref, final child) {
                      final shouldShowWinnerCard = ref.watch(
                        userRankingControllerProvider.select(
                          (final state) => state.shouldShowWinnerCard,
                        ),
                      );

                      if (shouldShowWinnerCard) {
                        return Padding(
                          padding: EdgeInsets.fromLTRB(10.r, 15.h, 10.r, 0),
                          child: const WinnerCard(),
                        );
                      }
                      return const SizedBox();
                    },
                  ),
                ),

                // 用户信息卡片
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(10.r, 0.h, 10.r, 0.h),
                    child: const UserInfoCard(),
                  ),
                ),
              ];
            },
            body: const RankingListContainer(),
          ),
        ),
      ],
    );
  }
}
