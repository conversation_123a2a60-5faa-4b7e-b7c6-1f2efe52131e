import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity/special_food_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/activity/activity_repository.dart';

///获取地址列表按定位数据
Future<List<SpecailFoodData>?> getSpecialData(Ref ref,
    {required int buildingId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'building_id': buildingId,
  };
  // 秒杀活动数据
  final activityRepository = ActivityRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await activityRepository.getSpecialData(param);
  return addressInfo?.data;
}

///地址列表按定位数据提供者类
class SpecialListProvider extends StateNotifier<AsyncValue<List<SpecailFoodData>?>> {
  SpecialListProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchSpecialData({required int buildingId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getSpecialData(ref, buildingId: buildingId);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final specialListProvider = StateNotifierProvider.autoDispose<
    SpecialListProvider, AsyncValue<List<SpecailFoodData>?>>(
  (ref) => SpecialListProvider(ref),
);

