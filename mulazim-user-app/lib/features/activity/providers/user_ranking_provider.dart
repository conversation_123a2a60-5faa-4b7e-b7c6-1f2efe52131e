import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/repositories/activity/activity_repository.dart';
import 'package:user_app/data/models/activity/rankin_history_model.dart';
import 'package:user_app/data/models/activity/ranking_info_model.dart';
import 'package:user_app/data/models/activity/ranking_user_address_model.dart';

part 'user_ranking_provider.g.dart';

/// 排行榜数据仓库提供者
@riverpod
ActivityRepository activityRepository(final Ref ref) {
  return ActivityRepository(apiClient: ref.read(apiClientProvider));
}

/// 排行榜信息提供者
@riverpod
class UserRankingNotifier extends _$UserRankingNotifier {
  @override
  Future<RankingInfoData?> build({
    required final int rankingId,
    required final int areaId,
    required final int restaurantId,
    required final int type,
  }) async {
    return await _fetchRankingInfo(
      rankingId: rankingId,
      areaId: areaId,
      restaurantId: restaurantId,
      type: type,
    );
  }

  /// 获取排行榜信息
  Future<RankingInfoData?> _fetchRankingInfo({
    required final int rankingId,
    required final int areaId,
    required final int restaurantId,
    required final int type,
  }) async {
    try {
      final repository = ref.read(activityRepositoryProvider);
      
      final Map<String, dynamic> params = {
        'ranking_id': rankingId,
        'area_id': areaId,
        'restaurant_id': restaurantId,
        'type': type,
      };
      
      final result = await repository.getRankingInfo(params);
      
      if (result.success && result.data != null) {
        return result.data;
      } else {
        throw Exception(result.msg);
      }
    } catch (e) {
      throw Exception('获取排行榜信息失败: $e');
    }
  }

  /// 获取排名用户地址
  Future<RankingUserAddressData?> fetchRankingUserAddress({
    required final int rankingId,
  }) async {
    try {
      final repository = ref.read(activityRepositoryProvider);

      final Map<String, dynamic> params = {
        'ranking_id': rankingId
      };

      final result = await repository.getRankingUserAddress(params);

      if (result.success && result.data != null) {
        return result.data;
      } else {
        throw Exception(result.msg);
      }
    } catch (e) {
      throw Exception('获取排名用户地址失败: $e');
    }
  }

  /// 确认排行榜地址
  Future<bool> confirmRankingAddress({
    required final int rankingId,
    required final int addressId,
  }) async {
    try {
      final repository = ref.read(activityRepositoryProvider);

      final Map<String, dynamic> params = {
        'ranking_id': rankingId,
        'address_id': addressId,
      };

      final result = await repository.confirmRankingAddress(params);

      if (result.success) {
        return true;
      } else {
        throw Exception(result.msg);
      }
    } catch (e) {
      throw Exception('确认地址失败: $e');
    }
  }

  /// 获取排行榜历史
  Future<List<RankingHistoryData>?> getRankingHistory() async {
      try { 
      final repository = ref.read(activityRepositoryProvider);
      final result = await repository.getRankingHistory();

      if (result.success && result.data != null) {
        return result.data;
      } else {
        throw Exception(result.msg);
      }
    } catch (e) {
      throw Exception('获取排行榜历史失败: $e');
    }
  }
}
