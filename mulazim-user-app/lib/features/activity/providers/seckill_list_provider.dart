import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/activity/seckill_food_model.dart';
import 'package:user_app/data/repositories/activity/activity_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

///获取地址列表按定位数据
Future<SecKillFoodData?> getSpecialData(Ref ref,
    {required int buildingId, String? beginTime}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    't': 1,
    'building_id': buildingId,
    'begin_time': beginTime,
  };
  // 秒杀活动数据
  final activityRepository = ActivityRepository(apiClient: ref.read(apiClientProvider));
  final addressInfo = await activityRepository.getSeckillData(param);
  return addressInfo.data;
}

///地址列表按定位数据提供者类
class SecKillListProvider extends StateNotifier<AsyncValue<SecKillFoodData?>> {
  SecKillListProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> fetchSecKillData({required int buildingId, String? beginTime}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getSpecialData(ref, buildingId: buildingId,beginTime: beginTime);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final seckillListProvider = StateNotifierProvider.autoDispose<
    SecKillListProvider, AsyncValue<SecKillFoodData?>>(
  (ref) => SecKillListProvider(ref),
);

