// import 'dart:math' as logger;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:user_app/core/utils/logger.dart';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/utils/gallery_util.dart';
import 'package:user_app/core/utils/permission_helper.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_state.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/order_ranking_preview_state.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/widgets/dialog/share_wx_moments_success_dialog.dart';
import 'package:user_app/features/mine/services/order_ranking_service.dart';
import 'package:user_app/generated/l10n.dart';

part 'order_ranking_preview_controller.g.dart';

/// 订单排名预览页面控制器
@riverpod
class OrderRankingPreviewController extends _$OrderRankingPreviewController {
  @override
  OrderRankingPreviewState build() {
    // 初始化加载数据
    Future.microtask(() => getOrderRanking());
    return const OrderRankingPreviewState();
  }

  /// 获取订单排名
  Future<void> getOrderRanking([final int currentId = 0]) async {
    state = state.copyWith(isLoading: true);

    try {
      // 获取区域信息
      final areaId = ref.watch(
        homeNoticeProvider
            .select((final state) => state.value?.location?.areaId ?? 0),
      );

      // 使用OrderRankingService获取数据
      final service = ref.read(orderRankingServiceProvider.notifier);
      final data = await service.getOrderRankingPage(areaId, currentId);

      if (data != null) {
        ActivityPreviewModel? activity;
        if (data["activity"] != null) {
          activity = ActivityPreviewModel.fromJson(data["activity"]);
        }

        // 构建头部菜单和高度
        final headerResult = _determineHeaderItems(data);

        // 获取用户预览
        if (activity != null && activity.id > 0) {
          _getFavoriteShareView(activity.id, 5);
        }

        // 更新状态
        state = state.copyWith(
          isLoading: false,
          activityObj: activity,
          headerItems: headerResult["headerItems"] as List<HeaderItem>,
          headerHeight: headerResult["headerHeight"] as String,
          currentId: currentId,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
        );
      }
    } catch (e) {
      Logger.e('OrderRankingPreview', '获取订单排名失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 确定当前ID和头部高度
  Map<String, dynamic> _determineHeaderItems(final Map<String, dynamic> data) {
    final s = S.current;
    final hasAreaActivity = data["has_area_activity"] as bool? ?? false;
    final hasPlatActivity = data["has_plat_activity"] as bool? ?? false;
    final areaName = data["area_name"] as String? ?? "";

    if (hasAreaActivity && hasPlatActivity) {
      // 两个都为true
      return {
        "headerItems": [
          HeaderItem(title: s.rankingTitle, id: "1"),
          HeaderItem(title: areaName, id: "2"),
        ],
        "headerHeight": "53", // 106rpx => 53.h
      };
    } else {
      return {
        "headerItems": <HeaderItem>[],
        "headerHeight": "0",
      };
    }
  }

  /// 获取用户分享数据和预览
  Future<void> _getFavoriteShareView(final int id, final int type) async {
    try {
      final service = ref.read(orderRankingServiceProvider.notifier);
      await service.shareActivity(id, type);
    } catch (e) {
      Logger.e('OrderRankingPreview', '获取用户分享数据失败: $e');
    }
  }

  /// 头部选项改变
  void onSegmentChange(final String id) {
    getOrderRanking(int.parse(id));
  }

  /// 倒计时结束
  void onTimeUp() {
    try {
      // 倒计时结束后显示提示
      BotToast.showText(text: S.current.activityIsOver);

      // 延迟1秒后返回上一页，与小程序保持一致
      Future.delayed(const Duration(seconds: 1), () {
        final context = AppContext().navigatorKey.currentContext;
        if (context != null && context.mounted) {
          // 返回上一页而不是导航到奖品页面
          Navigator.of(context).pop();
        }
      });
    } catch (e) {
      Logger.e('OrderRankingPreview', '倒计时结束处理失败: $e');
    }
  }

  /// 下载图片
  Future<void> downloadImage() async {
    if (state.activityObj == null) return;
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    final permission = androidInfo.version.sdkInt <= 32
        ? Permission.storage
        : Permission.photos;
    String explanation = S.current.photos_permission_explanation;
    // 在显示选择器之前，请求相机和相册权限
    final permissionResults = await PermissionHelper.requestPermission(
      permission: permission,
      explanation: explanation,
      title: S.current.profile_photo_permissions,
    );

    if (!permissionResults) {
      BotToast.showText(
        text: S.current.permissions_required
            .replaceAll('%s', S.current.profile_photo_permissions),
      );
      return;
    }

    final images = state.activityObj!.shareCoverImages;
    final s = S.current;

    if (images.isEmpty) {
      BotToast.showText(text: s.retry);
      return;
    }

    // 显示加载提示
    final context = AppContext().navigatorKey.currentContext;
    if (context == null) return;

    LoadingDialog().show();

    try {
      // 随机选择一个图片URL
      final randomIndex = DateTime.now().millisecondsSinceEpoch % images.length;
      final randomImageUrl = images[randomIndex];

      // 保存图片到相册
      final result = await GalleryUtil.saveImageToGallery(
        imageUrl: randomImageUrl,
      );

      if (result) {
        // 保存成功，显示对话框
        final dialogContext = AppContext().navigatorKey.currentContext;
        if (dialogContext != null && dialogContext.mounted) {
          // 隐藏加载提示
          LoadingDialog().hide();

          // 显示成功对话框
          ShareWxMomentsSuccessDialog.show(dialogContext);
        }
      }
    } catch (e) {
      LoadingDialog().hide();
      BotToast.showText(text: s.retry);
    }
  }

  /// 执行用户分享
  /// [id] 活动ID
  /// [type] 分享类型
  Future<void> shareActivity(final int type) async {
    // 获取当前上下文
    final context = AppContext().navigatorKey.currentContext;

    // 如果上下文为空，直接返回
    if (context == null) {
      BotToast.showText(text: S.current.about_share_failed);
      return;
    }

    final activityId = state.activityObj?.id ?? 0;
    if (activityId <= 0) {
      BotToast.showText(text: S.current.about_share_failed);
      return;
    }

    try {
      // 使用统一分享服务
      final service = ref.read(orderRankingServiceProvider.notifier);
      await service.share(
        activityId: activityId,
        context: context,
        shareType: type,
        defaultTitle: state.activityObj?.name ?? S.current.reward_level_order,
      );
    } catch (e) {
      Logger.e('OrderRankingPreview', '分享失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
    }
  }
}
