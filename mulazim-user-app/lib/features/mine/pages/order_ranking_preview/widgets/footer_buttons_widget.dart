import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 底部按钮组件
class FooterButtonsWidget extends StatelessWidget {
  /// 下载按钮回调
  final VoidCallback onDownload;

  /// 分享按钮回调
  final Function(int, int) onShare;

  /// 构造函数
  const FooterButtonsWidget({
    final Key? key,
    required this.onDownload,
    required this.onShare,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context) {
    return Column(
      children: [
        // 下载和分享按钮
        Row(
          children: [
            // 下载按钮 - 白色底红字
            Expanded(
              flex: 12, // 更多空间，与小程序保持一致
              child: Container(
                height: 40.h, // 高度固定为80rpx => 40.h
                decoration: BoxDecoration(
                  color: const Color(0xFFFF5C6A), // 红色背景，与小程序一致
                  borderRadius: BorderRadius.circular(40.r), // 与小程序一致
                ),
                child: TextButton.icon(
                  icon: CachedNetworkImage(
                    imageUrl:
                        'https://acdn.mulazim.com/wechat_mini/img/orderRanking/image.png',
                    width: 15.r,
                    height: 15.r,
                  ),
                  label: Text(
                    S.of(context).orderRankingPreviewBtn_2,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white, // 白色文字
                    ),
                  ),
                  onPressed: onDownload,
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(40.r),
                    ),
                  ),
                ),
              ),
            ),

            SizedBox(width: 10.r),

            // 分享按钮 - 黄色底黑字
            Expanded(
              flex: 8, // 比下载按钮小一些
              child: Container(
                height: 40.h, // 高度固定为80rpx => 40.h
                decoration: BoxDecoration(
                  color: const Color(0xFFFFE300), // 黄色背景，与小程序一致
                  borderRadius: BorderRadius.circular(40.r), // 与小程序一致
                ),
                child: TextButton.icon(
                  icon: CachedNetworkImage(
                    imageUrl:
                        'https://acdn.mulazim.com/wechat_mini/img/orderRanking/share_block.png',
                    width: 15.r,
                    height: 15.r,
                  ),
                  label: Text(
                    S.of(context).orderRankingPreviewBtn_1,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black, // 黑色文字
                    ),
                  ),
                  onPressed: () => onShare(1, 6),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(40.r),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
