import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/countdown_timer.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/rule_content_widget.dart';
import 'package:user_app/generated/l10n.dart';

class MainContentWidget extends ConsumerWidget {
  final Function() onReload;
  final String title;
  final String subtitle;
  final DateTime? endTime;
  final bool withCountdown;
  final String? previewImage;
  final String? loadingImage;
  final bool isLoading;
  final bool useHtmlRule;
  final VoidCallback? onTimeUp;

  /// 倒计时前缀文本 (对应小程序 beforeTitle)
  final String? countdownPrefixText;

  /// 倒计时后缀文本 (对应小程序 afterTitle)
  final String? countdownSuffixText;

  /// 是否为维吾尔语布局
  final bool isUg;

  const MainContentWidget({
    final Key? key,
    required this.onReload,
    required this.title,
    required this.subtitle,
    this.endTime,
    this.withCountdown = true,
    this.previewImage,
    this.loadingImage,
    this.isLoading = false,
    this.useHtmlRule = false,
    this.onTimeUp,
    this.countdownPrefixText,
    this.countdownSuffixText,
    this.isUg = false,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    return LayoutBuilder(
      builder: (final context, final constraints) {
        return Column(
          children: [
            // 倒计时组件
            if (withCountdown && endTime != null)
              _buildCountdown(context, endTime!),

            // 主内容区域
            Expanded(
              child: Container(
                margin: EdgeInsets.all(10.r),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // 预览图
                      if (previewImage != null)
                        Container(
                          padding: EdgeInsets.all(10.r),
                          child: CachedNetworkImage(
                            imageUrl: previewImage!,
                            fit: BoxFit.fitWidth,
                          ),
                        ),

                      // 规则内容
                      Container(
                        padding: EdgeInsets.all(10.r),
                        child: Column(
                          children: [
                            // 规则标题
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CachedNetworkImage(
                                  imageUrl:
                                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png',
                                  width: 20.r,
                                  height: 20.r,
                                ),
                                SizedBox(width: 10.r),
                                Text(
                                  S.of(context).activity_description,
                                  style: TextStyle(
                                    fontSize: 17.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                                SizedBox(width: 10.r),
                                CachedNetworkImage(
                                  imageUrl:
                                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png',
                                  width: 20.r,
                                  height: 20.r,
                                ),
                              ],
                            ),

                            SizedBox(height: 10.r),

                            // 规则内容 - 使用HTML格式显示或普通文本
                            if (useHtmlRule)
                              RuleContentWidget(
                                rule: subtitle,
                                padding: EdgeInsets.zero,
                              )
                            else
                              Text(
                                subtitle,
                                style: TextStyle(
                                  fontSize: 15.sp,
                                  color: Colors.grey[700],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCountdown(final BuildContext context, final DateTime endTime) {
    // 计算剩余秒数
    final now = DateTime.now();
    final remainSeconds = endTime.difference(now).inSeconds > 0
        ? endTime.difference(now).inSeconds
        : 0;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.r),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: RepaintBoundary(
        child: CountdownTimer(
          remainTime: remainSeconds,
          backgroundColor: Colors.transparent,
          timeItemBgColor: Colors.grey[200]!, // 使用红色与原组件保持一致
          padding: EdgeInsets.zero,
          onTimeUp: onTimeUp ?? onReload, // 优先使用传入的onTimeUp回调
          isUg: isUg,
          prefixText: S.current.seckill_list_time,
        ),
      ),
    );
  }
}
