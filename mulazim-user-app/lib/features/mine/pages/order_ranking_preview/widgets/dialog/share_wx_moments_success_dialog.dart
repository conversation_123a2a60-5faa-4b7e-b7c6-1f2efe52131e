import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 分享到朋友圈成功对话框
class ShareWxMomentsSuccessDialog extends StatelessWidget {
  /// 构造函数
  const ShareWxMomentsSuccessDialog({super.key});

  /// 显示分享成功对话框
  static Future<void> show(final BuildContext context) {
    return showDialog(
      context: context,
      builder: (final context) => const ShareWxMomentsSuccessDialog(),
    );
  }

  @override
  Widget build(final BuildContext context) {
    final s = S.current;
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Padding(
            padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
            child: Text(
              s.ok_hint,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ),

          // 内容
          Padding(
            padding: EdgeInsets.all(10.w),
            child: Text(
              s.orderRankingPreviewBtn_3,
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ),

          Divider(
            color: AppColors.dividerColor,
          ),

          // 确认按钮
          Padding(
            padding: EdgeInsets.only(bottom: 16.h, top: 8.h),
            child: InkWell(
              onTap: () => Navigator.of(context).pop(),
              child: Text(
                s.got_it,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
