import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

class ShareBottomSheetWidget extends StatelessWidget {
  final Function() onShareToFriend;
  final Function() onShareToCircle;

  const ShareBottomSheetWidget({
    Key? key,
    required this.onShareToFriend,
    required this.onShareToCircle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Container(
      height: 150.h,
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.share_title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildShareItem(
                context,
                icon: Icons.chat_bubble,
                label: s.share_friend,
                onTap: () {
                  onShareToFriend();
                  Navigator.pop(context);
                },
              ),
              _buildShareItem(
                context,
                icon: Icons.people,
                label: s.share_circle,
                onTap: () {
                  onShareToCircle();
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShareItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 25.sp),
          ),
          SizedBox(height: 8.h),
          Text(
            label,
            style: TextStyle(fontSize: 14.sp),
          ),
        ],
      ),
    );
  }
}
