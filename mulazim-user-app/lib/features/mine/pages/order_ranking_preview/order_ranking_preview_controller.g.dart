// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_ranking_preview_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRankingPreviewControllerHash() =>
    r'ec94001ae533ad483ce780ded5d1993b1ca83fbf';

/// 订单排名预览页面控制器
///
/// Copied from [OrderRankingPreviewController].
@ProviderFor(OrderRankingPreviewController)
final orderRankingPreviewControllerProvider = AutoDisposeNotifierProvider<
    OrderRankingPreviewController, OrderRankingPreviewState>.internal(
  OrderRankingPreviewController.new,
  name: r'orderRankingPreviewControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRankingPreviewControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderRankingPreviewController
    = AutoDisposeNotifier<OrderRankingPreviewState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
