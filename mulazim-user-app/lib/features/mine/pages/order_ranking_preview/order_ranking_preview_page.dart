import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/order_ranking_preview_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/widgets/footer_buttons_widget.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/widgets/header_background_widget.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/widgets/main_content_widget.dart';
import 'package:user_app/features/mine/pages/order_ranking_preview/widgets/share_bottom_sheet_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单排名预览页面
class OrderRankingPreviewPage extends ConsumerWidget {
  const OrderRankingPreviewPage({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final state = ref.watch(orderRankingPreviewControllerProvider);
    final controller = ref.read(orderRankingPreviewControllerProvider.notifier);

    // 判断当前是否为维吾尔语环境（通过当前的语言环境判断）
    final isUg = Localizations.localeOf(context).languageCode != 'zh';

    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        title: Text(S.of(context).snow_detail),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 顶部背景
          const HeaderBackgroundWidget(
            backgroundImage:
                'https://acdn.mulazim.com/wechat_mini/img/orderRanking/preview-header-tag.png',
          ),

          // 主内容区域 - 添加顶部边距使其部分覆盖在背景上
          Positioned(
            top: kToolbarHeight + 20.h, // 调整这个值使内容适当覆盖在背景上
            left: 0,
            right: 0,
            bottom: 0,
            child: Column(
              children: [
                // 主内容区域
                if (state.isLoading)
                  LoadingWidget()
                else if (state.activityObj != null)
                  Expanded(
                    child: MainContentWidget(
                      onReload: () {
                        // 只有在非加载状态时才重新加载
                        if (!state.isLoading) {
                          controller.getOrderRanking();
                        }
                      },
                      title: state.activityObj!.name,
                      subtitle: state.activityObj!.rule,
                      endTime: DateTime.now().add(
                        Duration(seconds: state.activityObj!.remainStartTime),
                      ),
                      previewImage: state.activityObj!.announcePage,
                      isLoading: state.isLoading,
                      useHtmlRule: true, // 使用HTML方式显示规则内容
                      // 时间结束时调用控制器的onTimeUp方法处理业务逻辑
                      onTimeUp: controller.onTimeUp,
                      // 自定义文案部分 - 参照微信小程序countdown组件
                      countdownPrefixText: '', // 对应微信小程序的beforeTitle
                      countdownSuffixText:
                          S.current.countdown_end, // 对应微信小程序的afterTitle
                      isUg: isUg, // 传递是否为维吾尔语布局
                    ),
                  )
                else
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(S.of(context).no_data),
                          SizedBox(height: 16.h),
                          ElevatedButton(
                            onPressed: () => controller.getOrderRanking(),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(S.of(context).retry),
                          ),
                        ],
                      ),
                    ),
                  ),

                // 底部按钮
                Padding(
                  padding:
                      EdgeInsets.only(bottom: 16.r, left: 10.w, right: 10.w),
                  child: FooterButtonsWidget(
                    onDownload: () => controller.downloadImage(),
                    onShare: (final activityId, final shareType) {
                      if (state.activityObj != null) {
                        // 记录分享事件
                        controller.shareActivity(shareType);

                        // // 显示分享底部菜单
                        // if (shareType == 6) {
                        //   _showShareBottomSheet(
                        //     context,
                        //     state.activityObj!.id,
                        //     (final id, final type) =>
                        //         controller.shareActivity(id, type),
                        //   );
                        // }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示分享底部菜单
  Future<void> _showShareBottomSheet(
    final BuildContext context,
    final int activityId,
    final Function(int, int) onShare,
  ) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.r)),
      ),
      builder: (final context) => ShareBottomSheetWidget(
        onShareToFriend: () {
          Navigator.pop(context);
          onShare(activityId, 1);
        },
        onShareToCircle: () {
          Navigator.pop(context);
          onShare(activityId, 2);
        },
      ),
    );
  }
}
