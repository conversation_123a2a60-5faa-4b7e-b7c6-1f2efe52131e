import 'package:user_app/data/models/activity_order/order_ranking_model.dart';

/// 订单排名页面状态
class OrderRankingMyState {
  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 当前选择的标签索引
  final int currentId;

  /// 活动数据
  final OrderRankingModel? orderRanking;

  /// 头部菜单项
  final List<HeaderItem> headerItems;

  /// 构造函数
  const OrderRankingMyState({
    this.isLoading = false,
    this.error,
    this.currentId = 0,
    this.orderRanking,
    this.headerItems = const [],
  });

  /// 拷贝方法
  OrderRankingMyState copyWith({
    bool? isLoading,
    String? error,
    int? currentId,
    OrderRankingModel? orderRanking,
    List<HeaderItem>? headerItems,
  }) {
    return OrderRankingMyState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      currentId: currentId ?? this.currentId,
      orderRanking: orderRanking ?? this.orderRanking,
      headerItems: headerItems ?? this.headerItems,
    );
  }
}

/// 头部菜单项
class HeaderItem {
  final String title;
  final String id;

  const HeaderItem({
    required this.title,
    required this.id,
  });
}
