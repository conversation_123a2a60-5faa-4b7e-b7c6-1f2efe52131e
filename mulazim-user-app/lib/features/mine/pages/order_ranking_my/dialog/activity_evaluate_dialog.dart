import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 评价弹窗，用于用户对活动进行评价
class ActivityEvaluateDialog extends StatefulWidget {
  /// 点击关闭按钮回调
  final VoidCallback? onClose;

  /// 提交评价回调，参数为评价类型（0：不喜欢，1：喜欢）和评价内容
  final void Function(int type, String text)? onSubmit;

  /// 构造函数
  const ActivityEvaluateDialog({
    final Key? key,
    this.onClose,
    this.onSubmit,
  }) : super(key: key);

  /// 显示评价对话框
  static Future<void> show(
    final BuildContext context, {
    final VoidCallback? onClose,
    final void Function(int type, String text)? onSubmit,
  }) async {
    return showDialog(
      context: context,
      builder: (final context) => ActivityEvaluateDialog(
        onClose: () {
          Navigator.of(context).pop();
          if (onClose != null) onClose();
        },
        onSubmit: (final type, final text) {
          Navigator.of(context).pop();
          if (onSubmit != null) onSubmit(type, text);
        },
      ),
    );
  }

  @override
  State<ActivityEvaluateDialog> createState() => _ActivityEvaluateDialogState();
}

class _ActivityEvaluateDialogState extends State<ActivityEvaluateDialog> {
  /// 文本编辑控制器
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    // 释放资源
    _textController.dispose();
    super.dispose();
  }

  /// 关闭弹窗
  void _close() {
    if (widget.onClose != null) {
      widget.onClose!();
    }
  }

  /// 提交评价
  void _submit(final int type) {
    if (widget.onSubmit != null) {
      widget.onSubmit!(type, _textController.text);
    }
  }

  @override
  Widget build(final BuildContext context) {
    // 获取本地化字符串
    final s = S.current;
    // 获取当前语言
    final lang = Localizations.localeOf(context).languageCode;
    // 确定文本方向
    final isRtl = lang == 'en';
    final textDirection = isRtl ? TextDirection.rtl : TextDirection.ltr;

    return Center(
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: (ScreenUtil().screenWidth - 40.w),
          padding: EdgeInsets.symmetric(horizontal: 25.r, vertical: 20.r),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 标题栏
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10.r),
                child: Center(
                  child: Text(
                    s.evaluate_title,
                    style: TextStyle(
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),

              // 评价输入框
              Container(
                padding: EdgeInsets.all(10.r),
                decoration: BoxDecoration(
                  color: Color(0xFFF1F1F1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: TextField(
                  controller: _textController,
                  textDirection: textDirection,
                  textAlign: isRtl ? TextAlign.right : TextAlign.left,
                  maxLines: 7,
                  style: TextStyle(
                    fontSize: 17.sp,
                    color: Colors.black,
                  ),
                  decoration: InputDecoration(
                    hintText: s.evaluate_placeholder,
                    hintStyle: TextStyle(
                      fontSize: 15.sp,
                      color: Colors.grey,
                    ),
                    border: InputBorder.none,
                  ),
                ),
              ),

              // 按钮区域
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10.r),
                child: Row(
                  children: [
                    // 不喜欢按钮
                    Expanded(
                      flex: 2,
                      child: InkWell(
                        onTap: () => _submit(2),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                          decoration: BoxDecoration(
                            color: Color(0xFFEDEDED),
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Center(
                            child: Text(
                              s.evaluate_no_satis,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 间隔
                    SizedBox(width: 10.w),
                    // 喜欢按钮
                    Expanded(
                      flex: 3,
                      child: InkWell(
                        onTap: () => _submit(1),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF5DD953),
                                Color(0xFF00C040),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Center(
                            child: Text(
                              s.evaluate_satis,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
