import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/dialog/activity_evaluate_dialog.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/evaluate_box.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/my_rank_box.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/dialog/order_ranking_rule_dialog.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/prize_list.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/segmented_control.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/winner_list.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单排名页面
class OrderRankingMyPage extends ConsumerWidget {
  const OrderRankingMyPage({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 获取国际化资源
    final s = S.current;

    // 监听加载状态，显示加载对话框
    _listenToLoadingState(context, ref);

    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      extendBodyBehindAppBar: true, // 允许内容延伸到AppBar下方
      appBar: CustomAppBar(
        title: s.prize_list_title,
        backgroundColor: Colors.transparent, // 透明背景
      ),
      body: Stack(
        children: [
          // 背景渐变和图片
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFFF5353),
                    Color(0xFFFFa0B7),
                    Color(0xFFFF0000),
                  ],
                ),
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(40.r),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(40.r),
                ),
                child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  imageUrl:
                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/preview-header-tag.png',
                ),
              ),
            ),
          ),

          // 主内容区域 - 添加顶部边距使其部分覆盖在背景上
          Positioned(
            top: kToolbarHeight + 20.h, // 调整这个值使内容适当覆盖在背景上
            left: 0,
            right: 0,
            bottom: 0,
            child: Column(
              children: [
                // 分段控制器 - 悬浮在背景上
                Consumer(
                  builder: (final context, final ref, final _) {
                    final headerItems = ref.watch(
                      orderRankingMyControllerProvider
                          .select((final state) => state.headerItems),
                    );

                    if (headerItems.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return SegmentedControl(
                      items: headerItems,
                      current: "0",
                      onChange: ref
                          .read(orderRankingMyControllerProvider.notifier)
                          .onSegmentChange,
                    );
                  },
                ),
                // 使用Expanded包裹主内容区域
                Expanded(
                  child: Consumer(
                    builder: (final context, final ref, final _) {
                      final orderRanking = ref.watch(
                        orderRankingMyControllerProvider
                            .select((final state) => state.orderRanking),
                      );

                      if (orderRanking == null) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      return SingleChildScrollView(
                        padding: EdgeInsets.all(10.w),
                        child: Column(
                          children: [
                            // 我的排名信息/下单记录
                            Consumer(
                              builder: (final context, final ref, final _) {
                                return MyRankBox(
                                  onShowRule: () =>
                                      _showRuleDialog(context, ref),
                                );
                              },
                            ),

                            // 根据是否有中奖记录选择显示哪个组件/奖品
                            Consumer(
                              builder: (final context, final ref, final _) {
                                final hasWinners = ref.watch(
                                  orderRankingMyControllerProvider.select(
                                    (final state) =>
                                        state.orderRanking?.userOrderPrize.any(
                                          (final prize) => prize.isLucky == 1,
                                        ) ??
                                        false,
                                  ),
                                );

                                return hasWinners
                                    ? const WinnerList()
                                    : const PrizeList();
                              },
                            ),

                            // 评价和分享区域
                            Consumer(
                              builder: (final context, final ref, final _) {
                                return EvaluateBox(
                                  onEvaluate: () =>
                                      _showEvaluateDialog(context, ref),
                                );
                              },
                            ),

                            // 底部间距
                            SizedBox(height: 20.h),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示规则对话框
  void _showRuleDialog(final BuildContext context, final WidgetRef ref) {
    final orderRanking = ref.read(
      orderRankingMyControllerProvider
          .select((final state) => state.orderRanking),
    );

    OrderRankingRuleDialog.show(
      context,
      rule: orderRanking?.rule ?? "",
      onClose: () {
        ref.read(orderRankingMyControllerProvider.notifier).closeRuleModal();
      },
    );
  }

  /// 显示评价对话框
  void _showEvaluateDialog(final BuildContext context, final WidgetRef ref) {
    ActivityEvaluateDialog.show(
      context,
      onClose: () {
        // 更新状态
        ref
            .read(orderRankingMyControllerProvider.notifier)
            .closeEvaluateModal();
      },
      onSubmit: (final type, final content) {
        // 提交评价
        ref
            .read(orderRankingMyControllerProvider.notifier)
            .submitEvaluate(type, content);
      },
    );
  }

  /// 监听加载状态，显示加载对话框
  void _listenToLoadingState(final BuildContext context, final WidgetRef ref) {
    ref.listen(
      orderRankingMyControllerProvider.select((final state) => state.isLoading),
      (final previous, final current) {
        if (current) {
          // 显示加载对话框
          LoadingDialog().show();
        } else if (previous == true && current == false) {
          // 隐藏加载对话框
          LoadingDialog().hide();
        }
      },
    );
  }
}
