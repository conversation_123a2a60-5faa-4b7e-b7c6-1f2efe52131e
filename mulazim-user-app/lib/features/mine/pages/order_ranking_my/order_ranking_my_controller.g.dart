// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_ranking_my_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRankingMyControllerHash() =>
    r'84e7a9ea289f4e0b865381fa69f49f171a2e4f9c';

/// See also [OrderRankingMyController].
@ProviderFor(OrderRankingMyController)
final orderRankingMyControllerProvider = AutoDisposeNotifierProvider<
    OrderRankingMyController, OrderRankingMyState>.internal(
  OrderRankingMyController.new,
  name: r'orderRankingMyControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRankingMyControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderRankingMyController = AutoDisposeNotifier<OrderRankingMyState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
