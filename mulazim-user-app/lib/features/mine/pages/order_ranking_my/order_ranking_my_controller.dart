import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/utils/logger.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_state.dart';
import 'package:user_app/features/mine/services/order_ranking_service.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';

part 'order_ranking_my_controller.g.dart';

@riverpod
class OrderRankingMyController extends _$OrderRankingMyController {
  @override
  OrderRankingMyState build() {
    Future.microtask(() => init());
    // 监听登录状态，登录成功后重新加载数据
    ref.listen(isLoggedInProvider, (final previous, final next) {
      if (next) {
        Future.microtask(() => init());
      }
    });
    return const OrderRankingMyState();
  }

  /// 初始化
  Future<void> init() async {
    state = state.copyWith(isLoading: true);
    await getLockHistory();
  }

  /// 获取奖品列表
  Future<void> getLockHistory([final int currentId = 0]) async {
    state = state.copyWith(
      isLoading: true,
      error: null,
      currentId: currentId,
    );

    try {
      // 获取区域信息
      final areaId = ref.watch(
        homeNoticeProvider
            .select((final state) => state.value?.location?.areaId ?? 0),
      );

      // 使用OrderRankingService获取数据
      final service = ref.read(orderRankingServiceProvider.notifier);
      final data = await service.getLuckyHistory(areaId, currentId);

      if (data != null) {
        // 构建头部菜单
        final headerItems = _determineHeaderItems(data);

        // 更新状态
        state = state.copyWith(
          isLoading: false,
          orderRanking: data,
          headerItems: headerItems,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 确定当前ID和头部高度
  List<HeaderItem> _determineHeaderItems(final OrderRankingModel data) {
    final s = S.current;
    if (data.hasAreaActivity && data.hasPlatActivity) {
      // 两个都为true
      return [
        HeaderItem(title: s.rankingTitle, id: "1"),
        HeaderItem(title: data.areaName, id: "2"),
      ];
    } else {
      return [];
    }
  }

  /// 头部选项改变
  void onSegmentChange(final String id) {
    getLockHistory(int.parse(id));
  }

  /// 点击查看规则 - 仅用于直接绑定事件
  void showRuleModal() {
    // 保留方法，用于MyRankBox组件默认调用
  }

  /// 关闭规则弹出框
  void closeRuleModal() {
    // 仅用于关闭对话框后的逻辑处理
  }

  /// 点击评价 - 仅用于直接绑定事件
  void showEvaluateModal() {
    // 保留方法，用于EvaluateBox组件默认调用
  }

  /// 关闭评价弹出框
  void closeEvaluateModal() {
    // 仅用于关闭对话框后的逻辑处理
  }

  /// 提交评价
  Future<void> submitEvaluate(final int type, final String content) async {
    if (state.orderRanking == null) return;

    state = state.copyWith(isLoading: true);

    try {
      final service = ref.read(orderRankingServiceProvider.notifier);
      final success = await service.submitActivityComment(
          state.orderRanking!.activityId, type, content);

      if (success) {
        state = state.copyWith(
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: "提交评价失败",
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 兑换奖品
  Future<void> exchangePrize(final int chanceId) async {
    state = state.copyWith(isLoading: true);

    try {
      final service = ref.read(orderRankingServiceProvider.notifier);
      final success = await service.exchangePrize(chanceId);

      if (success) {
        state = state.copyWith(isLoading: false);
        // 兑换成功后刷新数据
        getLockHistory(state.currentId);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: "兑换失败",
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 分享活动
  /// [prizeName] 奖品名称，可选
  /// [prizeImage] 奖品图片URL，可选
  Future<void> shareActivity({String? prizeName, String? prizeImage}) async {
    if (state.orderRanking == null) return;

    // 获取当前上下文
    final context = ref.context;

    // 如果上下文为空，直接返回
    if (context == null) {
      BotToast.showText(text: S.current.about_share_failed);
      return;
    }

    try {
      // 获取活动ID
      final activityId = state.orderRanking!.activityId;

      // 使用统一分享服务
      final service = ref.read(orderRankingServiceProvider.notifier);
      await service.share(
        activityId: activityId,
        context: context,
        prizeName: prizeName ?? state.orderRanking!.name,
        prizeImage: prizeImage,
        defaultTitle: S.current.reward_level_order,
      );
    } catch (e) {
      Logger.e('OrderRankingMy', '分享失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
    }
  }

  /// 倒计时结束
  void onTimeUp() {
    getLockHistory(state.currentId);
  }
}
