import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/user_prize_item.dart';

/// 中奖名单组件（已中奖状态）
class WinnerList extends ConsumerWidget {
  const WinnerList({final Key? key}) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final controller = ref.read(orderRankingMyControllerProvider.notifier);

    // 使用select精确监听需要的状态
    final winnerItems = ref.watch(orderRankingMyControllerProvider.select(
        (final state) =>
            state.orderRanking?.userOrderPrize
                .where((final prize) => prize.isLucky == 1)
                .toList() ??
            []));

    if (winnerItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: winnerItems.map((final item) {
        return UserPrizeItem(
          item: item,
          onExchange: controller.exchangePrize,
          onShare: controller.shareActivity,
        );
      }).toList(),
    );
  }
}
