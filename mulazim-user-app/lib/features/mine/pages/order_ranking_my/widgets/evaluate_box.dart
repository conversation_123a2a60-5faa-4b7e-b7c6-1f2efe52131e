import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 评价和分享区域组件
class EvaluateBox extends ConsumerWidget {
  /// 评价按钮点击回调
  final VoidCallback? onEvaluate;

  const EvaluateBox({
    super.key,
    this.onEvaluate,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final s = S.current;
    final controller = ref.read(orderRankingMyControllerProvider.notifier);

    // 使用select精确监听需要的状态
    final hasWinners = ref.watch(orderRankingMyControllerProvider.select(
        (final state) =>
            state.orderRanking?.userOrderPrize
                .any((final prize) => prize.isLucky == 1) ??
            false));

    return Container(
      padding: EdgeInsets.all(10.r),
      margin: EdgeInsets.only(bottom: 10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.network(
                "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
              SizedBox(width: 4.w),
              Text(
                s.about_share,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 4.w),
              Image.network(
                "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
            ],
          ),

          // 按钮区域
          Padding(
            padding: EdgeInsets.symmetric(vertical: 15.r),
            child: Row(
              children: [
                // 评价按钮
                Expanded(
                  child: GestureDetector(
                    onTap: onEvaluate ?? controller.showEvaluateModal,
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 9.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border:
                            Border.all(color: AppColors.redColor, width: 1.w),
                        borderRadius: BorderRadius.circular(25.r),
                      ),
                      child: Center(
                        child: Text(
                          s.evaluate_activity,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.redColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 10.w),

                // 分享按钮 - 只在没有中奖时显示
                if (!hasWinners)
                  Expanded(
                    child: GestureDetector(
                      onTap: controller.shareActivity,
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 9.h),
                        decoration: BoxDecoration(
                          color: AppColors.baseYellowColor,
                          borderRadius: BorderRadius.circular(25.r),
                        ),
                        child: Center(
                          child: Text(
                            s.about_share,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textPrimaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
