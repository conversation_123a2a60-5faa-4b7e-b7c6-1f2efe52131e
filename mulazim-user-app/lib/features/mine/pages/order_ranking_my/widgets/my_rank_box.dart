import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 我的排名信息组件
class MyRankBox extends ConsumerWidget {
  /// 显示规则按钮点击回调
  final VoidCallback? onShowRule;

  const MyRankBox({
    super.key,
    this.onShowRule,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final s = S.current;
    final controller = ref.read(orderRankingMyControllerProvider.notifier);

    // 使用select精确监听需要的状态
    final userOrderPrize = ref.watch(orderRankingMyControllerProvider
        .select((final state) => state.orderRanking?.userOrderPrize ?? []));

    return Container(
      padding: EdgeInsets.all(10.r),
      margin: EdgeInsets.only(bottom: 10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CachedNetworkImage(
                imageUrl:
                    "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
              SizedBox(width: 4.w),
              Text(
                s.order_status_during_activity,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 4.w),
              CachedNetworkImage(
                imageUrl:
                    "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
            ],
          ),

          // 规则按钮
          GestureDetector(
            onTap: onShowRule ?? controller.showRuleModal,
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 15.r),
              padding: EdgeInsets.symmetric(vertical: 4.r),
              width: 0.5.sw,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25.r),
                border: Border.all(
                  color: AppColors.redColor.withOpacity(0.1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    s.ranking_rule_title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.redColor,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Icon(
                    Icons.chevron_right,
                    size: 13.sp,
                    color: AppColors.redColor,
                  ),
                ],
              ),
            ),
          ),

          // 用户排名列表
          if (userOrderPrize.isNotEmpty)
            SizedBox(
              height: 40.h,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: userOrderPrize.length,
                separatorBuilder: (final context, final index) =>
                    SizedBox(width: 17.5.w),
                itemBuilder: (final context, final index) {
                  final item = userOrderPrize[index];
                  return Container(
                    padding: EdgeInsets.all(5.r),
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        if (item.isLucky == 1)
                          Image.network(
                            "https://acdn.mulazim.com/wechat_mini/img/orderRanking/oneLevel.png",
                            width: 20.w,
                            height: 20.h,
                          ),
                        Text(
                          item.luckyUserIndex,
                          style: TextStyle(
                            fontSize: 19.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}
