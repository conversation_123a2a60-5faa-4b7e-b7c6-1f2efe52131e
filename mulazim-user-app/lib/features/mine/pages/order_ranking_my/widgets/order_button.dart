import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/generated/l10n.dart';

/// 下单按钮组件
class OrderButton extends StatelessWidget {
  const OrderButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final s = S.current;

    return GestureDetector(
      onTap: () => // 跳转到订单标签页
          MainPageTabs.navigateToTab(context, MainPageTabs.home),
      child: Container(
        margin: EdgeInsets.only(top: 15.r),
        padding: EdgeInsets.symmetric(
          vertical: 9.h,
          horizontal: 10.w,
        ),
        decoration: BoxDecoration(
          color: AppColors.redColor,
          borderRadius: BorderRadius.circular(25.r),
        ),
        child: Center(
          child: Text(
            s.springFestivaOrder,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
