import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/data/models/activity_order/order_ranking_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 用户奖品项组件
class UserPrizeItem extends StatelessWidget {
  final UserOrderPrize item;
  final Function(int) onExchange;
  final Function() onShare;

  const UserPrizeItem({
    Key? key,
    required this.item,
    required this.onExchange,
    required this.onShare,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final s = S.current;
    final isUg = Localizations.localeOf(context).languageCode != 'zh';

    return Container(
      padding: EdgeInsets.all(10.r),
      margin: EdgeInsets.only(bottom: 10.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.network(
                "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
              SizedBox(width: 4.w),
              Text(
                s.winning_award,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 4.w),
              Image.network(
                "https://acdn.mulazim.com/wechat_mini/img/orderRanking/title.png",
                width: 15.w,
                height: 15.h,
              ),
            ],
          ),

          // 用户排名
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.r),
            child: Text(
              item.luckyUserIndex,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // 奖品图片
          Container(
            width: 200.w,
            height: 200.w,
            margin: EdgeInsets.symmetric(vertical: 10.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: item.prizeImage,
                    width: 200.w,
                    height: 200.w,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  child: Container(
                    height: 50.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 奖品名称
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.r),
            child: Text(
              item.prizeName,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // 按钮区域
          Padding(
            padding: EdgeInsets.symmetric(vertical: 10.r, horizontal: 20.w),
            child: Row(
              children: [
                // 兑换按钮
                Expanded(
                  child: GestureDetector(
                    onTap: item.state == 4
                        ? null
                        : () => onExchange(item.chanceId),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h),
                      decoration: BoxDecoration(
                        color: item.state == 4
                            ? Colors.grey.shade300
                            : AppColors.redColor,
                        borderRadius: BorderRadius.circular(25.r),
                        border: Border.all(
                          color: item.state == 4
                              ? Colors.grey.shade300
                              : AppColors.redColor,
                          width: 1.w,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          item.state == 4 ? s.prize_xchangeS : s.prize_xchange,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color:
                                item.state == 4 ? Colors.black : Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 10.w),

                // 分享按钮
                Expanded(
                  child: GestureDetector(
                    onTap: onShare,
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 10.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25.r),
                        border: Border.all(
                          color: AppColors.redColor,
                          width: 1.w,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          s.about_share,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.redColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
