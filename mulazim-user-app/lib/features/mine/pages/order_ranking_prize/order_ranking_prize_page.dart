import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/widgets/segmented_control.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/dialog/order_ranking_rule_dialog.dart';
import 'package:user_app/features/mine/pages/order_ranking_my/order_ranking_my_state.dart';
import 'package:user_app/features/mine/pages/order_ranking_prize/order_ranking_prize_controller.dart';
import 'package:user_app/features/mine/pages/order_ranking_prize/widgets/index.dart';

/// 订单排名奖品页面
class OrderRankingPrizePage extends ConsumerStatefulWidget {
  const OrderRankingPrizePage({super.key});

  @override
  ConsumerState<OrderRankingPrizePage> createState() =>
      _OrderRankingPrizePageState();
}

class _OrderRankingPrizePageState extends ConsumerState<OrderRankingPrizePage> {

  /// 处理滚动状态变化
  void _handleScrollStateChanged(bool isScrolling) {
    ref
        .read(orderRankingPrizeControllerProvider.notifier)
        .setScrolling(isScrolling);
  }

  /// 监听加载状态，显示加载对话框
  void _listenToLoadingState() {
    ref.listen(
      orderRankingPrizeControllerProvider
          .select((final state) => state.isLoading),
      (final previous, final current) {
        if (current) {
          // 显示加载对话框
          LoadingDialog().show();
        } else if (previous == true && current == false) {
          // 隐藏加载对话框
          LoadingDialog().hide();
        }
      },
    );
  }

  @override
  Widget build(final BuildContext context) {
    // 监听加载状态
    _listenToLoadingState();

    final controller = ref.read(orderRankingPrizeControllerProvider.notifier);

    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      extendBodyBehindAppBar: true, // 允许内容延伸到AppBar下方
      appBar: CustomAppBar(
        title: S.current.reward_level_order,
        backgroundColor: Colors.transparent, // 透明背景
      ),
      body: Stack(
        children: [
          // 背景渐变和图片
          const PrizeHeaderWidget(),

          // 主内容区域 - 添加顶部边距使其部分覆盖在背景上
          Positioned(
            top: kToolbarHeight + 20.h, // 调整这个值使内容适当覆盖在背景上
            left: 0,
            right: 0,
            bottom: 0,
            child: Column(
              children: [
                // 分段控制器 - 悬浮在背景上
                Consumer(
                  builder: (final context, final ref, final _) {
                    final headerState = ref.watch(
                      orderRankingPrizeControllerProvider
                          .select((final state) => state.headerItems),
                    );
                    final tabIndex = ref.watch(
                      orderRankingPrizeControllerProvider
                          .select((final state) => state.tabIndex),
                    );

                    if (headerState.isEmpty) return const SizedBox.shrink();

                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: 25.w),
                      child: SegmentedControl(
                        items: headerState
                            .map(
                              (final item) => HeaderItem(
                                title: item['title'] as String,
                                id: item['id'] as String,
                              ),
                            )
                            .toList(),
                        current: tabIndex.toString(),
                        onChange: controller.onSegmentChange,
                        backgroundColor: Colors.transparent,
                        borderColor: Colors.white,
                        sliderColor: Colors.white,
                      ),
                    );
                  },
                ),

                // 广告通知
                Consumer(
                  builder: (final context, final ref, final _) {
                    final notices = ref.watch(
                      orderRankingPrizeControllerProvider
                          .select((final state) => state.userOrderNotice),
                    );

                    if (notices.isEmpty) return const SizedBox.shrink();
                    return UserAdvert(notices: notices);
                  },
                ),

                // 主内容区域
                Expanded(
                  child: Consumer(
                    builder: (final context, final ref, final _) {
                      final prizeList = ref.watch(
                        orderRankingPrizeControllerProvider
                            .select((final state) => state.prizeList),
                      );
                      final activityRule = ref.watch(
                        orderRankingPrizeControllerProvider
                            .select((final state) => state.activityRule),
                      );

                      return PrizeContent(
                        prizeList: prizeList,
                        activityRule: activityRule,
                        onRuleClick: () => _showRuleDialog(context),
                        onScrollStateChanged: _handleScrollStateChanged,
                      );
                    },
                  ),
                ),

                // 底部用户信息
                Consumer(
                  builder: (final context, final ref, final _) {
                    final userPrize = ref.watch(
                      orderRankingPrizeControllerProvider
                          .select((final state) => state.userPrize),
                    );

                    if (userPrize == null) return const SizedBox.shrink();
                    return PrizeFooter(
                      userPrize: userPrize,
                      navigateToHome: controller.navigateToHome,
                      navigateToMyPrize: controller.navigateToMyPrize,
                    );
                  },
                ),
              ],
            ),
          ),

          // 分享按钮
          Consumer(
            builder: (final context, final ref, final _) {
              final isScrolling = ref.watch(
                orderRankingPrizeControllerProvider
                    .select((final state) => state.isScrolling),
              );

              return ShareButtonWidget(
                onTap: controller.share,
                isScrolling: isScrolling,
              );
            },
          ),
        ],
      ),
    );
  }

  // 显示规则对话框
  void _showRuleDialog(final BuildContext context) {
    OrderRankingRuleDialog.show(
      context,
      rule: ref.read(orderRankingPrizeControllerProvider).activityRule,
      onClose: () {
        // 无需特殊处理
      },
    );
  }
}
