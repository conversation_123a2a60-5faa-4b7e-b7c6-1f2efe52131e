// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_ranking_prize_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRankingPrizeControllerHash() =>
    r'b665d1560091fd57c22340c33cfa66c3e3ea7487';

/// 订单排名奖品
///
/// Copied from [OrderRankingPrizeController].
@ProviderFor(OrderRankingPrizeController)
final orderRankingPrizeControllerProvider = AutoDisposeNotifierProvider<
    OrderRankingPrizeController, OrderRankingPrizeState>.internal(
  OrderRankingPrizeController.new,
  name: r'orderRankingPrizeControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRankingPrizeControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderRankingPrizeController
    = AutoDisposeNotifier<OrderRankingPrizeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
