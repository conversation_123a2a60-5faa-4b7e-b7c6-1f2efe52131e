import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:user_app/features/index/pages/main_page.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/logger.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/mine/services/order_ranking_service.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/mine/pages/order_ranking_prize/order_ranking_prize_state.dart';

part 'order_ranking_prize_controller.g.dart';

/// 订单排名奖品
@riverpod
class OrderRankingPrizeController extends _$OrderRankingPrizeController {
  @override
  OrderRankingPrizeState build() {
    // 初始化加载数据
    Future.microtask(() {
      getPrizeList();
    });
    return const OrderRankingPrizeState();
  }

  /// 获取奖品列表
  Future<void> getPrizeList() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    try {
      // 获取区域信息
      final areaId = ref.read(homeNoticeProvider).value?.location?.areaId ?? 0;

      // 获取当前平台类型
      const platType = 0; // 0表示默认平台类型

      // 获取活动页面数据
      final service = ref.read(orderRankingServiceProvider.notifier);
      final data = await service.getOrderRankingPage(areaId, platType);

      if (data == null) {
        state = state.copyWith(isLoading: false);
        return;
      }

      // 按照小程序的逻辑解析headerItems
      List<Map<String, dynamic>> headerItems = [];
      if (data['has_area_activity'] == true &&
          data['has_plat_activity'] == true) {
        headerItems = [
          {'title': data['rankingTitle'] ?? '平台', 'id': '1'},
          {'title': data['area_name'] ?? '区域', 'id': '2'},
        ];
      }

      // 解析user_order_notice
      final List<dynamic> userOrderNotice =
          (data['user_order_notice'] as List?)?.map((final item) {
                final bool luckyState = item['lucky_state'] == 1;
                // 当前语言 - 在这里我们没法直接获取context，所以使用默认值
                final lang = ref.read(languageProvider); // 默认中文
                return {
                  'lucky_state': luckyState ? 1 : 0,
                  'user_name': item['user_name'] ?? '',
                  'mobile': item['mobile'] ?? '',
                  'user_lucky_index':
                      item['user_lucky_index']?.toString() ?? '',
                  'image': luckyState
                      ? 'https://acdn.mulazim.com/wechat_mini/img/orderRanking/advert_activie_$lang.png'
                      : 'https://acdn.mulazim.com/wechat_mini/img/orderRanking/advert_$lang.png',
                };
              }).toList() ??
              [];

      // 设置状态
      state = state.copyWith(
        isLoading: false,
        activityRule: data['activity']?['rule'] as String? ?? '',
        headerItems: headerItems,
        prizeList: data['prize'] as List? ?? [],
        userOrderNotice: userOrderNotice,
        userPrize: data['user'] as Map<String, dynamic>? ?? {},
        shareName: data['activity']?['name'] as String? ?? '',
        activityId: data['activity']?['id'] as int? ?? 0,
      );

      // 获取分享预览数据
      if (state.activityId > 0) {
        await _getFavoriteShareView(state.activityId, 5);
      }
    } catch (e) {
      Logger.e('OrderRankingPrize', '获取奖品列表失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 获取用户分享预览
  Future<void> _getFavoriteShareView(
      final int activityId, final int type) async {
    try {
      if (activityId == 0) return;

      final service = ref.read(orderRankingServiceProvider.notifier);
      await service.shareActivity(activityId, type);
    } catch (e) {
      Logger.e('OrderRankingPrize', '获取分享预览失败: $e');
    }
  }

  /// 分段选择变化
  void onSegmentChange(final String id) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, tabIndex: int.parse(id));

    try {
      // 获取区域信息
      final areaId = ref.read(homeNoticeProvider).value?.location?.areaId ?? 0;

      // 获取活动页面数据
      final service = ref.read(orderRankingServiceProvider.notifier);
      final response = await service.getOrderRankingPage(areaId, int.parse(id));

      if (response == null) {
        state = state.copyWith(isLoading: false);
        return;
      }

      // 提取真正的数据部分
      final data = response['data'];
      if (data == null) {
        state = state.copyWith(isLoading: false);
        return;
      }

      // 更新奖品列表
      state = state.copyWith(
        isLoading: false,
        prizeList: data['prize'] as List? ?? [],
      );
    } catch (e) {
      Logger.e('OrderRankingPrize', '切换标签失败: $e');
      state = state.copyWith(isLoading: false);
    }
  }

  /// 设置滚动状态
  void setScrolling(final bool isScrolling) {
    if (state.isScrolling != isScrolling) {
      state = state.copyWith(isScrolling: isScrolling);
    }
  }

  /// 设置头部高度
  void setHeaderHeight(final double height) {
    state = state.copyWith(headerHeight: height);
  }

  /// 设置通知高度
  void setNoticeHeight(final double height) {
    state = state.copyWith(noticeHeight: height);
  }

  /// 导航到我的奖品页面
  void navigateToMyPrize(final BuildContext context) {
    context.replace(AppPaths.orderRankingMy);
  }

  /// 导航到首页
  void navigateToHome(final BuildContext context) {
    // 跳转到订单标签页
    MainPageTabs.navigateToTab(context, MainPageTabs.home);
  }

  /// 分享
  Future<void> share() async {
    // 获取当前上下文
    final context = ref.context;

    // 如果上下文为空，直接返回
    if (context == null) {
      BotToast.showText(text: S.current.about_share_failed);
      return;
    }

    try {
      // 获取活动ID和分享名称
      final activityId = state.activityId;
      final shareName = state.shareName;

      // 如果活动ID有效，使用统一分享服务
      if (activityId > 0) {
        final service = ref.read(orderRankingServiceProvider.notifier);
        await service.share(
          activityId: activityId,
          context: context,
          shareType: 6,
          prizeName: shareName,
          defaultTitle: S.current.reward_level_order,
        );
      } else {
        BotToast.showText(text: S.current.about_share_failed);
      }
    } catch (e) {
      Logger.e('OrderRankingPrize', '分享失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
    }
  }
}
