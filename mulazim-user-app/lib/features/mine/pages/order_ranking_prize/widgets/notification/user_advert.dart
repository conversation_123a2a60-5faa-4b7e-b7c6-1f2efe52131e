import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/generated/l10n.dart';

/// 用户订单通知区域
class UserAdvert extends StatefulWidget {
  /// 构造函数
  const UserAdvert({
    final Key? key,
    required this.notices,
  }) : super(key: key);

  /// 通知列表
  final List<dynamic> notices;

  @override
  State<UserAdvert> createState() => _UserAdvertState();
}

class _UserAdvertState extends State<UserAdvert> {
  /// 页面控制器
  late final PageController _pageController;

  /// 定时器
  Timer? _timer;

  /// 当前页面索引
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // 开始自动轮播
    _startAutoScroll();
  }

  @override
  void dispose() {
    // 取消定时器
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  /// 启动自动轮播
  void _startAutoScroll() {
    // 每3秒切换一次页面
    _timer = Timer.periodic(const Duration(seconds: 3), (final timer) {
      if (widget.notices.isEmpty) return;

      // 计算下一页索引，循环显示
      final nextPage = (_currentPage + 1) % widget.notices.length;

      // 使用animateToPage进行平滑切换
      _pageController.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );

      // 更新当前页面索引
      _currentPage = nextPage;
    });
  }

  @override
  Widget build(final BuildContext context) {
    if (widget.notices.isEmpty) {
      return const SizedBox();
    }

    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        itemCount: widget.notices.length,
        onPageChanged: (final index) {
          _currentPage = index;
        },
        itemBuilder: (final context, final index) {
          final notice = widget.notices[index];
          final isLucky = notice['lucky_state'] == 1;

          return Container(
            margin: EdgeInsets.only(bottom: 10.h),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
            decoration: BoxDecoration(
              gradient: isLucky
                  ? const LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: [Color(0xFFFFD200), Color(0xFFFFF500)],
                    )
                  : null,
              color: isLucky ? null : Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                // 广告图片
                CachedNetworkImage(
                  imageUrl: notice['image'] ?? '',
                  width: 20.w,
                  height: 20.h,
                  fit: BoxFit.contain,
                  placeholder: (final context, final url) => Container(
                    width: 20.w,
                    height: 20.h,
                    color: Colors.grey[200],
                  ),
                  errorWidget: (final context, final url, final error) => Icon(
                    Icons.error,
                    size: 20.w,
                    color: Colors.grey[500],
                  ),
                ),

                // 如果是中奖，显示索引
                if (isLucky) ...[
                  Container(
                    height: 24.h,
                    alignment: Alignment.center,
                    margin: EdgeInsets.symmetric(horizontal: 5.w),
                    child: Text(
                      notice['user_lucky_index']?.toString() ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: "NumberFont",
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],

                // 用户信息
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 姓名与状态
                      Row(
                        children: [
                          Text(
                            notice['user_name'] ?? '',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: isLucky
                                  ? Colors.black
                                  : const Color(0xFFFF9E00),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          Text(
                            isLucky
                                ? S.of(context).won_the_prize
                                : S.of(context).order_the_prize,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14.sp,
                              color: isLucky
                                  ? Colors.black
                                  : const Color(0xFFFF9E00),
                            ),
                          ),
                        ],
                      ),

                      // 手机号
                      Text(
                        textDirection: TextDirection.ltr,
                        notice['mobile'] ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color:
                              isLucky ? Colors.black : const Color(0xFFFF9E00),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
