import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 奖品页面头部背景组件
class PrizeHeaderWidget extends StatelessWidget {
  /// 构造函数
  const PrizeHeaderWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        width: double.infinity,
        height: 200.h,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFF5353),
              Color(0xFFFFa0B7),
              Color(0xFFFF0000),
            ],
          ),
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(40.r),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(40.r),
          ),
          child: CachedNetworkImage(
            fit: BoxFit.cover,
            imageUrl:
                'https://acdn.mulazim.com/wechat_mini/img/orderRanking/preview-header-tag.png',
          ),
        ),
      ),
    );
  }
}
