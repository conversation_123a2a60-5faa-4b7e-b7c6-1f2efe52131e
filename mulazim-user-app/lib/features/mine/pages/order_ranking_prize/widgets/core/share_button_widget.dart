import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/generated/l10n.dart';

/// 分享按钮组件
class ShareButtonWidget extends StatefulWidget {
  /// 构造函数
  const ShareButtonWidget({
    final Key? key,
    required this.onTap,
    required this.isScrolling,
  }) : super(key: key);

  /// 点击回调
  final VoidCallback onTap;

  /// 是否正在滚动（滚动时显示小尺寸）
  final bool isScrolling;

  @override
  State<ShareButtonWidget> createState() => _ShareButtonWidgetState();
}

class _ShareButtonWidgetState extends State<ShareButtonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _previousScrollingState = false;

  @override
  void initState() {
    super.initState();
    _previousScrollingState = widget.isScrolling;

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // 如果初始状态不是滚动，启动动画
    if (!widget.isScrolling) {
      _animationController.repeat(reverse: true);
    }

    // 缩放动画
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void didUpdateWidget(final ShareButtonWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检测滚动状态变化
    if (_previousScrollingState != widget.isScrolling) {
      _previousScrollingState = widget.isScrolling;

      // 根据滚动状态控制动画
      if (widget.isScrolling) {
        _animationController.stop();
      } else {
        if (!_animationController.isAnimating) {
          _animationController.repeat(reverse: true);
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final s = S.of(context);
    final isUg = Localizations.localeOf(context).languageCode == "en";

    return Positioned(
      // 根据语言环境调整位置
      left: isUg ? null : 20.w,
      right: isUg ? 20.w : null,
      bottom: 70.h,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (final context, final child) {
          return Transform.scale(
            scale: widget.isScrolling ? 1.0 : _scaleAnimation.value,
            child: child,
          );
        },
        child: GestureDetector(
          onTap: widget.onTap,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            width: widget.isScrolling ? 40.w : 115.w,
            height: 40.w,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF5C6A), Color(0xFFFF8A98)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(40.r),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF5C6A).withOpacity(0.3),
                  blurRadius: 6.r,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CachedNetworkImage(
                  width: 15.w,
                  height: 15.h,
                  fit: BoxFit.contain,
                  imageUrl:
                      'https://acdn.mulazim.com/wechat_mini/img/orderRanking/share.png',
                  placeholder: (final context, final url) => Container(
                    width: 15.w,
                    height: 15.h,
                    color: Colors.white,
                  ),
                ),
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: widget.isScrolling
                      ? const SizedBox.shrink()
                      : Row(
                          children: [
                            SizedBox(width: 5.w),
                            Text(
                              s.about_share,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
