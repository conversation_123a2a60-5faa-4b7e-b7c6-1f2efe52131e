import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/generated/l10n.dart';

/// 订单排行奖品页底部组件
class PrizeFooter extends StatelessWidget {
  /// 用户奖品信息
  final Map<String, dynamic> userPrize;

  /// 导航到首页事件
  final Function(BuildContext) navigateToHome;

  /// 导航到我的奖品页事件
  final Function(BuildContext) navigateToMyPrize;

  const PrizeFooter({
    super.key,
    required this.userPrize,
    required this.navigateToHome,
    required this.navigateToMyPrize,
  });

  @override
  Widget build(final BuildContext context) {
    final s = S.of(context);

    // 定义头像尺寸
    final avatarSize = 47.w;

    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.r),
          topRight: Radius.circular(25.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 用户信息
          Expanded(
            flex: 2,
            child: Row(
              children: [
                // 使用Container确保头像是正圆形
                Container(
                  width: avatarSize,
                  height: avatarSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1.w,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(avatarSize / 2),
                    child: CachedNetworkImage(
                      imageUrl: userPrize['user_avatar'] ??
                          'https://acdn.mulazim.com/wechat_mini/img/lover/nan.png',
                      width: avatarSize,
                      height: avatarSize,
                      fit: BoxFit.cover,
                      placeholder: (final context, final url) => Container(
                        width: avatarSize,
                        height: avatarSize,
                        color: Colors.grey[200],
                      ),
                      errorWidget: (final context, final url, final error) =>
                          Container(
                        width: avatarSize,
                        height: avatarSize,
                        color: Colors.grey[300],
                        child: Icon(
                          Icons.person,
                          size: avatarSize / 2,
                          color: Colors.grey[500],
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userPrize['name'] ?? '',
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        userPrize['area_name'] ?? '',
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: const Color(0xFF747474),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 用户排名状态
          if (userPrize['lucky_state'] == 0)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w),
              child: InkWell(
                onTap: () => navigateToHome(context),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.5.w, vertical: 7.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF5C6A),
                    borderRadius: BorderRadius.circular(23.r),
                  ),
                  child: Text(
                    s.springFestivaOrder,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),
            ),

          if (userPrize['lucky_state'] == 1)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w),
              child: Text(
                s.notGetItAward,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF747474),
                ),
              ),
            ),

          if (userPrize['lucky_state'] == 2)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w),
              child: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        userPrize['lucky_index'].toString(),
                        style: TextStyle(
                          fontSize: 23.sp,
                          color: const Color(0xFFFF9E00),
                          fontWeight: FontWeight.bold,
                          fontFamily: 'YouSheBiaoTiHei',
                        ),
                      ),
                      Image.network(
                        'https://acdn.mulazim.com/wechat_mini/img/orderRanking/oneLevel.png',
                        width: 15.w,
                        height: 15.h,
                      ),
                    ],
                  ),
                  Text(
                    s.order_status,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF747474),
                    ),
                  ),
                ],
              ),
            ),

          // 获奖历史
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (userPrize['lucky_state'] == 0)
                  Text(
                    s.not_yet_placed_order,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: const Color(0xFF747474),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                InkWell(
                  onTap: () => navigateToMyPrize(context),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.network(
                        'https://acdn.mulazim.com/wechat_mini/img/orderRanking/record.png',
                        width: 15.w,
                        height: 15.h,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        s.ranking_history,
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: const Color(0xFFFF4B56),
                        ),
                      ),
                      Icon(
                        Icons.chevron_right,
                        size: 15.sp,
                        color: const Color(0xFFFF4B56),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
