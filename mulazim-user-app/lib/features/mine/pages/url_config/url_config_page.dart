import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/config/environment_config.dart';

/// URL配置页面
class UrlConfigPage extends ConsumerStatefulWidget {
  /// 构造函数
  const UrlConfigPage({super.key});

  @override
  ConsumerState<UrlConfigPage> createState() => _UrlConfigPageState();
}

class _UrlConfigPageState extends ConsumerState<UrlConfigPage> {
  /// 当前选中的环境
  Environment? _selectedEnvironment;

  @override
  void initState() {
    super.initState();
    // 初始化时选中当前环境
    _selectedEnvironment = EnvironmentConfig.currentEnvironment;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'URL配置',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimaryColor,
          ),
        ),
        backgroundColor: AppColors.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textPrimaryColor,
            size: 20.sp,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      backgroundColor: AppColors.backgroundColor,
      body: Padding(
        padding: EdgeInsets.all(16.sp),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 开发环境Panel
              _buildEnvironmentPanel(
                title: '开发环境 (Development)',
                icon: Icons.developer_mode,
                environment: Environment.development,
                isCurrentEnvironment: EnvironmentConfig.currentEnvironment == Environment.development,
                isSelected: _selectedEnvironment == Environment.development,
                onTap: () => _selectEnvironment(Environment.development),
              ),
              SizedBox(height: 16.sp),
              
              // 测试环境Panel
              _buildEnvironmentPanel(
                title: '测试环境 (Staging)',
                icon: Icons.bug_report,
                environment: Environment.staging,
                isCurrentEnvironment: EnvironmentConfig.currentEnvironment == Environment.staging,
                isSelected: _selectedEnvironment == Environment.staging,
                onTap: () => _selectEnvironment(Environment.staging),
              ),
              SizedBox(height: 16.sp),
              
              // 生产环境Panel
              _buildEnvironmentPanel(
                title: '生产环境 (Production)',
                icon: Icons.cloud,
                environment: Environment.production,
                isCurrentEnvironment: EnvironmentConfig.currentEnvironment == Environment.production,
                isSelected: _selectedEnvironment == Environment.production,
                onTap: () => _selectEnvironment(Environment.production),
              ),
              
              // 底部确认按钮
              SizedBox(height: 32.sp),
              _buildConfirmButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 选择环境
  void _selectEnvironment(Environment environment) {
    setState(() {
      _selectedEnvironment = environment;
    });
  }

  /// 确认环境切换
  void _confirmEnvironmentSwitch() async {
    if (_selectedEnvironment == null) {
      return;
    }

    // 如果选择的环境与当前环境相同，不需要切换
    if (_selectedEnvironment == EnvironmentConfig.currentEnvironment) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('当前已经是${_getEnvironmentName(_selectedEnvironment!)}环境'),
          backgroundColor: AppColors.primary,
        ),
      );
      return;
    }

    try {
      // 设置新的环境
      EnvironmentConfig.setForcedEnvironment(_getEnvironmentName(_selectedEnvironment!));

      
      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('环境切换成功！已切换到${_getEnvironmentName(_selectedEnvironment!)}环境'),
            backgroundColor: AppColors.primary,
            duration: const Duration(seconds: 2),
          ),
        );
        
        // 延迟退出页面
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        print(e);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('环境切换失败：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 获取环境名称
  String _getEnvironmentName(Environment environment) {
    switch (environment) {
      case Environment.development:
        return '开发';
      case Environment.staging:
        return '测试';
      case Environment.production:
        return '生产';
    }
  }

  Widget _buildEnvironmentPanel({
    required String title,
    required IconData icon,
    required Environment environment,
    required bool isCurrentEnvironment,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // 获取对应环境的配置数据
    final config = _getEnvironmentConfig(environment);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.sp),
          border: isSelected 
            ? Border.all(color: AppColors.primary, width: 2)
            : isCurrentEnvironment
              ? Border.all(color: AppColors.primary.withOpacity(0.5), width: 1)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Panel标题
            Container(
              padding: EdgeInsets.all(16.sp),
              decoration: BoxDecoration(
                color: isSelected 
                  ? AppColors.primary.withOpacity(0.1)
                  : isCurrentEnvironment
                    ? AppColors.primary.withOpacity(0.05)
                    : AppColors.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.sp),
                  topRight: Radius.circular(12.sp),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: isSelected 
                      ? AppColors.primary
                      : isCurrentEnvironment
                        ? AppColors.primary.withOpacity(0.7)
                        : AppColors.textPrimaryColor,
                    size: 20.sp,
                  ),
                  SizedBox(width: 8.sp),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: isSelected 
                          ? AppColors.primary
                          : isCurrentEnvironment
                            ? AppColors.primary.withOpacity(0.7)
                            : AppColors.textPrimaryColor,
                      ),
                    ),
                  ),
                  if (isCurrentEnvironment)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 4.sp),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12.sp),
                      ),
                      child: Text(
                        '当前环境',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  if (isSelected && !isCurrentEnvironment)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 4.sp),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12.sp),
                      ),
                      child: Text(
                        '已选择',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Panel内容
            Padding(
              padding: EdgeInsets.all(16.sp),
              child: Column(
                children: [
                  _buildConfigItem('API基础URL', config['apiBaseUrl']),
                  _buildConfigItem('Socket URL', config['socketUrl']),
                  _buildConfigItem('连接超时', '${config['connectTimeout']}ms'),
                  _buildConfigItem('接收超时', '${config['receiveTimeout']}ms'),
                  _buildConfigItem('最大重试', '${config['maxRetries']}次'),
                  _buildConfigItem('重试延迟', '${config['retryDelay']}ms'),
                  _buildConfigItem('日志状态', config['enableLogging'] ? '启用' : '禁用'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建确认按钮
  Widget _buildConfirmButton() {
    final isEnvironmentChanged = _selectedEnvironment != null && 
                                _selectedEnvironment != EnvironmentConfig.currentEnvironment;
    
    return Container(
      width: double.infinity,
      height: 48.sp,
      decoration: BoxDecoration(
        color: isEnvironmentChanged ? AppColors.primary : AppColors.grayColor,
        borderRadius: BorderRadius.circular(24.sp),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24.sp),
          onTap: isEnvironmentChanged ? _confirmEnvironmentSwitch : null,
          child: Center(
            child: Text(
              isEnvironmentChanged ? '确认切换环境' : '当前环境无需切换',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfigItem(String title, String value) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 16.sp),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondColor,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取指定环境的配置数据
  Map<String, dynamic> _getEnvironmentConfig(Environment environment) {
    final configs = {
      Environment.development: {
        'apiBaseUrl': 'https://smart.d.almas.biz/',
        'socketUrl': 'wss://chat-socket.d.almas.biz',
        'connectTimeout': 30000,
        'receiveTimeout': 15000,
        'enableLogging': true,
        'maxRetries': 2,
        'retryDelay': 1500,
      },
      Environment.staging: {
        'apiBaseUrl': 'https://smart.d.almas.biz/',
        'socketUrl': 'wss://chat-socket.d.almas.biz',
        'connectTimeout': 12000,
        'receiveTimeout': 6000,
        'enableLogging': true,
        'maxRetries': 3,
        'retryDelay': 1000,
      },
      Environment.production: {
        'apiBaseUrl': 'https://smart.mulazim.com/',
        'socketUrl': 'wss://chat-socket.mulazim.com',
        'connectTimeout': 30000,
        'receiveTimeout': 15000,
        'enableLogging': false,
        'maxRetries': 4,
        'retryDelay': 1500,
      },
    };
    
    return configs[environment]!;
  }
} 