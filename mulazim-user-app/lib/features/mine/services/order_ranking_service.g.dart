// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_ranking_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$orderRankingServiceHash() =>
    r'dcc2e16152c1e5397a2133b8764503cef1c0ac75';

/// 订单排名服务
///
/// Copied from [OrderRankingService].
@ProviderFor(OrderRankingService)
final orderRankingServiceProvider =
    AutoDisposeAsyncNotifierProvider<OrderRankingService, void>.internal(
  OrderRankingService.new,
  name: r'orderRankingServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderRankingServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderRankingService = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
