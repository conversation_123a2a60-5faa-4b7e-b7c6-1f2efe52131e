import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/mine/dots_data.dart';
import 'package:user_app/data/repositories/mine/dot_repository.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/main.dart';

part 'dots_provider.g.dart';

@riverpod
class Dots extends _$Dots {
  bool _isFirstBuild = false;
  int areaId = 0;
  DateTime? _lastRefreshTime;

  @override
  DotsData build() {
    // // 同时监听路由状态和页面生命周期状态，确保双重保证
    // final pageLifecycle = ref.watch(pageLifecycleProvider);

    // // 检查页面生命周期是否指向主页
    // final isMainPageByLifecycle =
    //     pageLifecycle.path == AppPaths.mainPage ||
    //     pageLifecycle.event == PageLifecycleEvent.back;

    // // 根据路由状态监测主页
    // if (isMainPageByLifecycle && _isFirstBuild) {
    //   _refreshIfNeeded();
    // }
    ref.listen(languageProvider, (final previous, final next) {
      _refreshIfNeeded();
    });
    final isLogin = ref.watch(isLoggedInProvider);
    if (isLogin) {
      _refreshIfNeeded();
    }

    // 首次构建处理
    if (!_isFirstBuild) {
      _isFirstBuild = true;
      _refreshIfNeeded();
    }
    areaId = ref.watch(
      homeNoticeProvider
          .select((final state) => state.value?.location?.areaId ?? 0),
    );

    if (areaId > 0) {
      _refreshIfNeeded();
    }

    return const DotsData();
  }

  /// 检查是否需要刷新，避免短时间内重复刷新
  void _refreshIfNeeded() {
    final now = DateTime.now();
    final shouldRefresh = _lastRefreshTime == null ||
        now.difference(_lastRefreshTime!).inMilliseconds > 1000;

    if (shouldRefresh) {
      _lastRefreshTime = now;
      Future.microtask(() => _loadDots());
    }
  }

  Future<void> _loadDots() async {
    if (ref.read(isLoggedInProvider) && areaId > 0) {
      final dotsRepository = ref.read(dotsRepositoryProvider);
      final dotsData = await dotsRepository.getDotsData(areaId);
      try {
        if (areaId > 0 && dotsData.data != null) {
          state = dotsData.data!;
        }
      } catch (e) {
        if (kDebugMode) {
          print('🔴 红点数据加载错误: $e');
        }
      }
    }
  }

  /// 手动刷新红点数据
  Future<void> refresh() async {
    _lastRefreshTime = DateTime.now();
    await _loadDots();
  }
}
