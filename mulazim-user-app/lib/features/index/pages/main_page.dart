import 'package:easy_app_installer/easy_app_installer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/utils/status_bar_util.dart';
import 'package:user_app/features/home/<USER>/index/home_page.dart';
import 'package:user_app/features/discount/pages/discount_page.dart';
import 'package:user_app/features/mine/pages/mine/mine_page.dart';
import 'package:user_app/features/mine/providers/dots_provider.dart';
import 'package:user_app/features/my_order/pages/index/my_order_page.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/app_router.dart';
import 'package:user_app/routes/paths.dart';

/// 定义主页面的标签索引常量
class MainPageTabs {
  /// 首页标签索引
  static const int home = 0;

  /// 订单标签索引
  static const int order = 1;

  /// 优惠标签索引
  static const int discount = 2;

  /// 我的标签索引
  static const int mine = 3;

  /// 导航到主页面并显示指定的标签页
  ///
  /// 使用全局状态管理，直接更新 mainTabIndexProvider
  static void navigateToTab(final BuildContext context, final int tabIndex) {
    // 更新全局状态
    final container = ProviderScope.containerOf(context);
    container.read(mainTabIndexProvider.notifier).state = tabIndex;

    // 导航到主页面（不需要传递参数，因为已经通过全局状态设置了标签索引）
    router.go(AppPaths.mainPage);
  }
}

class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  ConsumerState createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    // 确保主页面使用默认的浅色状态栏模式
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // StatusBarUtil.setLightMode();
    });
  }

  /// 构建主页面
  /// 使用ConsumerWidget，监听语言变化，避免其他状态变化导致整个页面重建
  @override
  Widget build(final BuildContext context) {
    // 使用select只监听语言变化，避免其他状态变化导致整个页面重建
    final currentLanguage =
        ref.watch(languageProvider.select((final lang) => lang));

    // 监听全局的标签索引状态
    final currentIndex = ref.watch(mainTabIndexProvider);

    // 使用语言作为key的一部分，确保语言变化时完全重建widget树
    return Scaffold(
      key: ValueKey('main_page_$currentLanguage'),
      body: Consumer(
        builder: (final context, final ref, final child) {
          return IndexedStack(
            key: ValueKey('indexed_stack_$currentLanguage'),
            index: currentIndex,
            children: [
              const HomePage(),
              const MyOrderPage(),
              const DiscountPage(),
              const MinePage(),
            ],
          );
        },
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: const Color(0xFF07C160),
        iconSize: 28.sp,
        unselectedItemColor: Colors.grey,
        selectedLabelStyle: TextStyle(fontSize: 17.sp),
        unselectedLabelStyle: TextStyle(fontSize: 16.sp),
        onTap: (final index) {
          // 更新全局状态而不是本地状态
          ref.read(mainTabIndexProvider.notifier).state = index;
          if (index == 1 && !ref.read(isLoggedInProvider)) {
            StatusBarUtil.setDarkMode();
          } else if (index == 0 || index == 2) {
            StatusBarUtil.setLightMode();
          }
        },
        items: [
          // 首页
          BottomNavigationBarItem(
            icon: Icon(IconFont.shouye, size: 26.sp),
            label: S.current.home,
          ),

          // 订单 - 带红点
          BottomNavigationBarItem(
            icon: Consumer(
              builder: (final context, final ref, final child) {
                final dotsData = ref.watch(dotsProvider);

                return Stack(
                  clipBehavior: Clip.none, // 允许子组件超出Stack边界
                  children: [
                    Icon(IconFont.wodedingdan, size: 26.sp),
                    if (dotsData.hasOrderDot)
                      Positioned(
                        right: -5.w,
                        top: -5.h,
                        child: Container(
                          alignment: Alignment.center,
                          constraints: BoxConstraints.tight(
                            dotsData.orderDotCount > 0
                                ? Size(18.w, 18.w)
                                : Size(12.w, 12.w),
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 1.h),
                          ),
                          child: dotsData.orderDotCount > 0
                              ? Text(
                                  dotsData.orderDotCount.toString(),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ),
                  ],
                );
              },
            ),
            label: S.current.orders,
          ),

          // 优惠
          BottomNavigationBarItem(
            icon: Icon(IconFont.quanbu, size: 26.sp),
            label: S.current.discount,
          ),

          // 我的 - 带红点
          BottomNavigationBarItem(
            icon: Consumer(
              builder: (final context, final ref, final child) {
                final dotsData = ref.watch(dotsProvider);

                return Stack(
                  clipBehavior: Clip.none, // 允许子组件超出Stack边界
                  children: [
                    Icon(IconFont.wode, size: 26.sp),
                    if (dotsData.hasDot)
                      Positioned(
                        right: -5.w,
                        top: -5.h,
                        child: Container(
                          width: 12.w,
                          height: 12.w,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 1.h),
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
            label: S.current.mine,
          ),
        ],
      ),
    );
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (!mounted) {
      // 确保组件仍然挂载在 widget 树上
      return;
    }
    // 应用回到前台，执行相应操作
    String downLoadFileLocation =
        await StorageService().read('downLoadFileLocation') ?? '';
    var currentVersion = await StorageService().read('currentVersion');
    var serviceVersion = await StorageService().read('serviceVersion');
    if (currentVersion != null && serviceVersion != null) {
      if (int.parse(serviceVersion.toString()) >
          int.parse(currentVersion.toString())) {
        if (downLoadFileLocation != '') {
          EasyAppInstaller.instance.installApk(downLoadFileLocation);
        }
      }
    }
  }
}

// class MainPage extends ConsumerWidget with WidgetsBindingObserver{
//   /// 创建主页面实例
//   const MainPage({super.key});
//
//
//
// }
