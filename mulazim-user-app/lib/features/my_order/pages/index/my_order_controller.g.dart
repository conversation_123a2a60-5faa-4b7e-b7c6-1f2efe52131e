// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_order_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myOrderControllerHash() => r'b4d6a17aefe0970ff63481dd072ebdc5c97c4709';

/// See also [MyOrderController].
@ProviderFor(MyOrderController)
final myOrderControllerProvider =
    AutoDisposeNotifierProvider<MyOrderController, MyOrderState>.internal(
  MyOrderController.new,
  name: r'myOrderControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myOrderControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MyOrderController = AutoDisposeNotifier<MyOrderState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
