import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/index.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/my_order/pages/index/widgets/order_item.dart';
import 'package:user_app/generated/l10n.dart';

/// 今日订单列表
class TodayOrderList extends ConsumerWidget {
  final VoidCallback onLoadMore;

  const TodayOrderList({
    final Key? key,
    required this.onLoadMore,
  }) : super(key: key);

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听今日订单数据
    final todayOrders = ref.watch(myOrderControllerProvider
        .select((final state) => state.cachedTodayOrders));

    // // 监听今日订单加载状态
    // final isLoading = ref.watch(myOrderControllerProvider
    //     .select((final state) => state.isLoading && state.type == 1));

    // 监听今日订单是否可加载更多
    final canLoadMore = ref.watch(myOrderControllerProvider
        .select((final state) => state.canLoadMoreToday));

    // // 如果正在加载，显示加载中
    // if (isLoading) {
    //   return const Center(child: CircularProgressIndicator());
    // }

    // 如果没有订单，显示空状态
    final error = ref
        .watch(myOrderControllerProvider.select((final state) => state.error));
    if (error != null) {
      return EmptyView(
        message: error,
        retryMessage: S.current.retry,
        onRetry: () =>
            ref.read(myOrderControllerProvider.notifier).refreshTodayOrders(),
      );
    }

    // 显示今日订单列表
    return CustomRefreshIndicator(
      onRefresh: () async {
        // 调用刷新今日订单方法
        await ref.read(myOrderControllerProvider.notifier).refreshTodayOrders();
        // 重置请求标志，允许再次请求数据
      },
      color: AppColors.primary,
      enablePullUp: true,
      onLoading: () async {
        onLoadMore();
      },
      hasMoreData: canLoadMore,
      child: todayOrders.isEmpty
          ? EmptyView(
              message: S.current.no_order,
            )
          : ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.only(top: 10.w),
              itemCount: todayOrders.length,
              itemBuilder: (final context, final index) {
                // 订单项
                return OrderItem(item: todayOrders[index]);
              }, // 添加性能优化选项
              addAutomaticKeepAlives: true,
              addRepaintBoundaries: true,
            ),
    );
  }
}
