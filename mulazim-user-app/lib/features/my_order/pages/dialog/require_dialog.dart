import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_controller.dart';
import 'package:user_app/features/my_order/providers/order_detail_provider.dart';
import 'package:user_app/generated/l10n.dart';

class RequireDialog extends Dialog {
  final String title;
  final String content;
  final double screenWidth;
  final Delayed? delayed;
  final int? orderId;
  final String? mobile;
  // 构造函数赋值
  RequireDialog(
      {Key? key,
      this.title = "",
      this.content = "",
      this.screenWidth = 0.0,
      this.delayed,
      this.orderId,
      this.mobile,
      })
      : super(key: key);

  @override
  Widget build(BuildContext context) {


    String originalBookingTime = delayed?.originalBookingTime ?? '';
    String newBookingTime = delayed?.newBookingTime ?? '';
    if(originalBookingTime.length > 5){
      originalBookingTime = originalBookingTime.substring(5,16);
    }
    if(newBookingTime.length > 5){
      newBookingTime = newBookingTime.substring(5,16);
    }

    // 调用方法
    // _showTimer(context);
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
      child: Material(
        textStyle: TextStyle(
          fontFamily: AppConstants.mainFont, // 添加维语字体
        ),
          type: MaterialType.transparency,
          child: Center(
            child: Consumer(
              builder: (context,ref,child) {
                return Directionality(
                  textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                  child: Container(
                    width: screenWidth - 50.w,
                    // height:Adapt.setPx(140),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.w),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          // height: 520.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.w),
                            // color: AppColors.redColor,
                            gradient: LinearGradient(
                              colors: [AppColors.secondGreenColor, Colors.white, Colors.white,AppColors.secondGreenColor], // 渐变的颜色列表
                              begin: Alignment.topRight, // 渐变开始位置
                              end: Alignment.bottomRight, // 渐变结束位置
                            ),
                          ),
                          child: Column(
                            children: [
                              Container(
                                // height: 300.w,
                                padding: EdgeInsets.symmetric(horizontal: 15.w),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topRight:Radius.circular(12.w),
                                      topLeft:Radius.circular(12.w),
                                    ),
                                ),
                                child: Column(
                                  children: [
                                    SizedBox(height: 30.w,),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        // Icon(Icons.accessibility,color: Colors.green,),
                                        Image.asset(
                                          'assets/images/order_left.png',
                                          fit: BoxFit.fill,
                                          width: 20.w,
                                          height: 12.w,
                                        ),
                                        SizedBox(width: 8.w,),
                                        Text(S.current.require_delay_title,style: TextStyle(color: Colors.black,fontSize: 17.5.sp, fontWeight: FontWeight.bold,fontFamily: 'UkijTuzTom'),),
                                        SizedBox(width: 8.w,),
                                        Image.asset(
                                          'assets/images/order_right.png',
                                          fit: BoxFit.fill,
                                          width: 20.w,
                                          height: 12.w,
                                        ),
                                        // Icon(Icons.accessibility,color: Colors.green,),
                                      ],
                                    ),
                                    SizedBox(height: 30.w,),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Text(S.current.reason,style: TextStyle(fontFamily: 'UkijTuzTom',fontSize: titleSize),),
                                      ],
                                    ),
                                    SizedBox(height: 15.w,),
                                    Container(
                                      alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                                      child: Text('${delayed?.reasonDelay ?? ''} ',style: TextStyle(fontFamily: 'UkijTuzTom',color: AppColors.textSecondaryColor,fontSize: 15.sp),textAlign: TextAlign.start,)
                                    ),
                                    SizedBox(height: 10.w,),
                                    Container(
                                      padding: EdgeInsets.symmetric(vertical: 12.w),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Colors.grey.shade100, width: 1.0),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(S.current.original_booking_time,style: TextStyle(fontFamily: 'UkijTuzTom',color: AppColors.textSecondColor,fontSize: mainSize),),
                                          Row(
                                            children: [
                                              Text('${delayed?.delayedDuration ?? ''}',style: TextStyle(color: Colors.orange,fontSize: 17.sp),),

                                              Text(' + ',style: TextStyle(color: Colors.orange,fontSize: 17.sp),),
                                              Directionality(
                                                  textDirection:TextDirection.ltr,
                                                  child: Text(originalBookingTime)
                                              ),

                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(vertical: 12.w),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          top: BorderSide(color: Colors.grey.shade100, width: 1.0),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(S.current.new_booking_time,style: TextStyle(fontFamily: 'UkijTuzTom',color: AppColors.textSecondColor,fontSize: mainSize),),
                                          Directionality(
                                              textDirection:TextDirection.ltr,
                                              child: Text(newBookingTime)
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 15.w,),

                                  ],
                                ),
                              ),
                              SizedBox(height: 12.w,),

                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [

                                  InkWell(
                                    onTap: (){
                                      ref.read(orderDetailProvider.notifier).confirmDelay(rid: delayed?.id ?? '');
                                      Navigator.of(context).pop();

                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8.w),
                                        color: AppColors.baseGreenColor,
                                      ),
                                      padding: EdgeInsets.symmetric(horizontal: 34.w,vertical: 10.w),
                                      child: Text(S.current.i_agree,textAlign: TextAlign.center,style: TextStyle(color: Colors.white,fontSize: titleSize,fontFamily: 'UkijTuzTom'),),
                                    ),
                                  ),
                                  SizedBox(width: 20.w,),
                                  InkWell(
                                    onTap: (){
                                      ref.read(myOrderDetailControllerProvider(orderId ?? 0).notifier).makePhoneCall(mobile ?? '');
                                      Navigator.of(context).pop();

                                    },
                                    child: Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(8.w),
                                          color: AppColors.couponBackground,
                                        ),
                                        padding: EdgeInsets.symmetric(horizontal: 16.w,vertical: 10.w),
                                        child: Row(
                                          children: [
                                            Text(S.current.connect_to,textAlign: TextAlign.center,style: TextStyle(color: AppColors.baseGreenColor,fontSize: titleSize,fontFamily: 'UkijTuzTom'),),
                                            SizedBox(width: 5.w,),
                                            Icon(Icons.phone,color: AppColors.baseGreenColor,size: 20.sp,)
                                          ],
                                        )
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20.w,),

                            ],
                          ),
                          // child: _swiperPart(popupAdver!),
                        ),
                        SizedBox(
                          height: 20.w,
                        ),
                        InkWell(
                          child: Container(
                              margin: EdgeInsets.only(left: 5.w),
                              alignment: Alignment.center,
                              width: 45.w,
                              height: 45.w,
                              child: Image.asset('assets/images/message_exit.png')),
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          )
      ),
    );
  }
}
