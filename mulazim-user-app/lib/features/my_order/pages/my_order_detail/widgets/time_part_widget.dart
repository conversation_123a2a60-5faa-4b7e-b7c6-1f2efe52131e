import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';

class TimePartWidget extends ConsumerWidget {
  final OrderDetailData data;
  final num restSecond;
  final Function onTap;

  const TimePartWidget({
    Key? key,
    required this.data,
    required this.restSecond,
    required this.onTap,
  }) : super(key: key);

  Map<String, dynamic> millisecondsToTime(num milliseconds) {
    int totalSeconds = milliseconds ~/ 1000;
    int hours = totalSeconds ~/ 3600;
    int remainingSecondsAfterHours = totalSeconds % 3600;
    int minutes = remainingSecondsAfterHours ~/ 60;
    int seconds = remainingSecondsAfterHours % 60;

    String hoursStr = hours.toString().padLeft(2, '0');
    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = seconds.toString().padLeft(2, '0');

    return {'hour': hoursStr, 'minute': minutesStr, 'second': secondsStr};
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Map<String, dynamic> timeMap = millisecondsToTime(restSecond);
    String hour = timeMap['hour'];
    String minute = timeMap['minute'];
    String second = timeMap['second'];

    return InkWell(
      onTap: () => onTap(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.w),
        margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w)
        ,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: 10.w,
                ),
                Text(
                  '${data.orderStateLog?[0].name}',
                  style: TextStyle(color: Colors.orange, fontSize: titleSize),
                ),
              ],
            ),
            Directionality(
              textDirection: TextDirection.rtl,

              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(
                    width: 10.w,
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                    decoration: BoxDecoration(
                        color: AppColors.baseBackgroundColor,
                        borderRadius: BorderRadius.circular(4.w)),
                    alignment: Alignment.center,
                    child: Text(
                      second,
                      style: TextStyle(
                          color: AppColors.baseGreenColor,
                          fontSize: soBigSize,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  Text(
                    ' : ',
                    style: TextStyle(
                        color: AppColors.baseGreenColor,
                        fontSize: soBigSize,
                        fontWeight: FontWeight.bold),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                    decoration: BoxDecoration(
                        color: AppColors.baseBackgroundColor,
                        borderRadius: BorderRadius.circular(4.w)),
                    alignment: Alignment.center,
                    child: Text(
                      minute,
                      style: TextStyle(
                          color: AppColors.baseGreenColor,
                          fontSize: soBigSize,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  Text(
                    ' : ',
                    style: TextStyle(
                        color: AppColors.baseGreenColor,
                        fontSize: soBigSize,
                        fontWeight: FontWeight.bold),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                    decoration: BoxDecoration(
                        color: AppColors.baseBackgroundColor,
                        borderRadius: BorderRadius.circular(4.w)),
                    alignment: Alignment.center,
                    child: Text(
                      hour,
                      style: TextStyle(
                          color: AppColors.baseGreenColor,
                          fontSize: soBigSize,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
