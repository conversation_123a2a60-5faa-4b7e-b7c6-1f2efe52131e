import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image_height.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/generated/l10n.dart';


class MarketingPartWidget extends ConsumerStatefulWidget {
  const MarketingPartWidget({super.key,required this.orderDetailData,});
  final OrderDetailData orderDetailData;

  @override
  ConsumerState createState() => _MarketingPartWidgetState();
}

class _MarketingPartWidgetState extends ConsumerState<MarketingPartWidget> {
  List<Marketing> marketing = [];
  List<ShipmentSteps> shipmentSteps = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    shipmentSteps = widget.orderDetailData.shipmentSteps ?? [];
    marketing.addAll(widget.orderDetailData.marketing ?? []);
    Coupon? coupon = widget.orderDetailData.coupon;
    if(coupon != null){
      Marketing couponMarketing = Marketing(
        detail: S.current.courtesy_page_title,
        image:'assets/images/courtesy-icon.svg',
        marketingType: 99,
        price: num.parse(coupon.price ?? '0')  ,
      );
      marketing.add(couponMarketing);
    }
  }

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: EdgeInsets.only(top: 8.w,bottom: 6.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Column(
          children: List.generate(
            marketing.length,
                (index) => _labelItem(
                marketing[index].image ?? '',
                marketing[index].detail ?? '',
                marketing[index].marketingType ?? 0,
                marketing[index].price ?? 0,
                index
                ),
          )),
    );
  }

  Widget _labelItem(String image, String label, int marketingType, num val, int index) {
    return InkWell(
      onTap: (){
        if(marketingType == 2){
          List<Tag> tags = [];
          for(int i = 0; i < shipmentSteps.length; i++){
            Tag thisTag = Tag(
              minDeliveryPrice:shipmentSteps[i].minDeliveryPrice,
              distanceEnd:shipmentSteps[i].distanceEnd,
              price:shipmentSteps[i].price,
            );
            tags.add(thisTag);
          }
          showShipmentReductionDialog(
              tags,context,ref);
        }
      },
      child: Column(
        children: [
          if(index != 0)
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _buildIcon(image),
                    SizedBox(
                      width: 5.w,
                    ),
                    Text(label),
                  ],
                ),
                Directionality(
                  textDirection: TextDirection.rtl,
                  child: Text(
                    '￥$val-',
                    style: TextStyle(fontSize: mainSize, color: Colors.red),
                  ),
                ),

              ],
            ),
          ),
        ],
      )
    );
  }

  /// 构建图标，根据不同类型优化
  Widget _buildIcon(String iconPath) {
    // 网络图片
    if (iconPath.startsWith('http')) {
      return PrefectImageHeight(
        imageUrl: iconPath,
        height: 24.w,
        fit: BoxFit.fitHeight,
      );
    }

    // SVG图片
    if (iconPath.toLowerCase().endsWith('.svg')) {
      return SvgPicture.asset(
        iconPath,
        width: 24.w,
        height: 24.h,
      );
    }

    // 本地图片
    return Image.asset(
      'assets/images/empty.png',
      width: 24.w,
      height: 24.h,
    );
  }
}
