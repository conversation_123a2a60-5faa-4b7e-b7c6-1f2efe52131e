import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/my_order/providers/order_detail_provider.dart';
import 'package:user_app/generated/l10n.dart';

class MapPartWidget extends ConsumerStatefulWidget {
  final OrderDetailData data;

  const MapPartWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  ConsumerState<MapPartWidget> createState() => _MapPartWidgetState();
}

class _MapPartWidgetState extends ConsumerState<MapPartWidget> {
  final GlobalKey _mapKey = GlobalKey();
  Map<String, dynamic> creationParams = {};
  late MethodChannel _channel;
  Timer? _timer;
  num distance = 0;

  @override
  void initState() {
    super.initState();
    _setupMap();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      startShipperPosition();
    });

  }

  void _setupMap() {
    String buildingLat =
        (widget.data.shipper?.buildingLat ?? 43.768194).toString();
    String buildingLng =
        (widget.data.shipper?.buildingLng ?? 87.62642).toString();
    String resLat =
        (widget.data.shipper?.resLat ?? 43.778861232771376).toString();
    String resLng =
        (widget.data.shipper?.resLng ?? 87.61465979154373).toString();
    String shipperLat = widget.data.shipper?.shipperLat ?? '43.768131';
    String shipperLng = widget.data.shipper?.shipperLng ?? '87.626129';
    String resLogo = widget.data.shipper?.resLogo ??
        'https://acdn.mulazim.com/upload/restaurant/logo/202204/02/4de10f1bc9c45abeb24552cf6139bbe5.jpg@400w_300h';

    creationParams = {
      'shipper_lat': shipperLat,
      'shipper_lng': shipperLng,
      'res_lat': resLat,
      'res_lng': resLng,
      'building_lat': buildingLat,
      'building_lng': buildingLng,
      'res_logo': resLogo,
    };
  }

  // 判断手势操作是否在规划图之内
  bool isPointerOnMap(Offset localPosition) {
    if (_mapKey.currentContext?.findRenderObject() is RenderBox) {
      // 获取规划图区域在屏幕上的位置和大小
      RenderBox mapBox =
          _mapKey.currentContext?.findRenderObject() as RenderBox;
      Offset mapPosition = mapBox.localToGlobal(Offset.zero);
      double mapLeft = mapPosition.dx;
      double mapTop = mapPosition.dy;
      double mapWidth = mapBox.size.width;
      double mapHeight = mapBox.size.height;
      if (localPosition.dx >= mapLeft &&
          localPosition.dx <= mapLeft + mapWidth &&
          localPosition.dy >= (mapTop / 1.5) &&
          localPosition.dy <= (mapTop / 1.5) + mapHeight) {
        // 触摸点在规划图区域内
        return true;
      } else {
        // 触摸点不在规划图区域内
        return false;
      }
    } else {
      return false;
    }
  }


  @override
  void dispose() {
    _timer!.cancel();
    _timer = null;
    super.dispose();
  }


  /// 每5秒更新一次配送员位置
  void startShipperPosition() {
    _timer = Timer.periodic(const Duration(seconds: 3), (final timer) async {
      // 如果配送员状态不是6（配送中），则取消定时器
      if ((ref.read(orderDetailProvider).valueOrNull?.shipper?.state ?? 0) != 6) {
        timer.cancel();
        _timer = null;
        return;
      }
      try {

        // 获取配送员位置信息
        await ref
            .read(shipperPositionProvider.notifier)
            .shipperPostionInfo(orderId: widget.data.id ?? 0);

        // 获取配送员经纬度
        String shipperLat = ref
            .read(shipperPositionProvider)
            .valueOrNull
            ?.shipper
            ?.shipperLat ??
            '';
        String shipperLng = ref
            .read(shipperPositionProvider)
            .valueOrNull
            ?.shipper
            ?.shipperLng ??
            '';
        // 调用原生方法更新配送员位置
        Map<String, dynamic> creationParams = {
          'shipper_lat': shipperLat,
          'shipper_lng': shipperLng,
        };
        distance = await _channel.invokeMethod('updateShipperPosition', creationParams);
        setState(() {});
      } catch (e) {
        print('compute with shipper distance error --> $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          key: _mapKey,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: Colors.white,
          ),
          margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
          padding: EdgeInsets.all(10.w),
          height: 250, // 根据键盘高度调整
          child: Center(
            child: Platform.isIOS
                ? UiKitView(
                    viewType: "plugin/order-map",
                    creationParams: creationParams,
                    creationParamsCodec: const StandardMessageCodec(),
                    onPlatformViewCreated: (int id) {
                      _channel = MethodChannel('order_line_channel');
                    },
                  )
                : AndroidView(
                    viewType: "<order-line-one>",
                    creationParams: creationParams,
                    creationParamsCodec: const StandardMessageCodec(),
                    onPlatformViewCreated: (int id) {
                      _channel = MethodChannel('order_line_channel');
                    },
                  ),
          ),
        ),
        if(distance != 0)
        Positioned(
          bottom: 10.w,
          right: 10.w,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.w),
                bottomRight: Radius.circular(10.w),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${S.current.distance_with_shipper}:${distance}M',
                  style: TextStyle(
                      color: AppColors.baseGreenColor,
                      fontSize: mainSize,
                      fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
