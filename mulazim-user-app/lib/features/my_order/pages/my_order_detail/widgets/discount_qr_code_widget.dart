import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/home/<USER>/parts/discount_code_page.dart';

class DiscountQRCodeWidget extends ConsumerWidget {
  const DiscountQRCodeWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    final appConfig = ref.read(localStorageRepositoryProvider).getAppConfig();
    final isShowEnterpriseWechatQrCode = appConfig?.app_show_enterprise_wechat_qr_code == 1;
    if (!isShowEnterpriseWechatQrCode) {
      return const SizedBox.shrink();
    }
    return Container(
        margin: EdgeInsets.only(top: 10.w, right: 10.w, left: 10.w),
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: Colors.white,
        ),
        child: Row(children: [
          InkWell(
            onTap: (){
              showImageViewer(
                context,
                imageUrls: ['https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png'],
                heroTagPrefix: 'chat_image',
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl:
                    'https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png',
                fit: BoxFit.cover,
                width: 108.w,
                height: 108.w,
              ),
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          InkWell(
            onTap: (){
              Navigator.of(context).push(MaterialPageRoute(builder: (final context) => DiscountCodePage(),));
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl:
                    'https://acdn.mulazim.com/wechat_mini/img/work/work-wechat-${ref.watch(languageProvider)}.png',
                fit: BoxFit.fill,
                height: 108.w,
                width: MediaQuery.of(context).size.width - 108.w - 50.w,
              ),
            ),
          ),
        ]));
  }
}
