class MyOrderDetailState {
  /// 构造函数
  const MyOrderDetailState({
    this.isLoading = false,
    this.error,
    this.canScroll = true,
    this.distance = 0,
    this.restSecond = 600000,
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 是否可以滚动
  final bool canScroll;

  /// 距离
  final num distance;

  /// 剩余时间（毫秒）
  final num restSecond;

  /// 拷贝方法
  MyOrderDetailState copyWith({
    final bool? isLoading,
    final String? error,
    final bool? canScroll,
    final num? distance,
    final num? restSecond,
  }) {
    return MyOrderDetailState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      canScroll: canScroll ?? this.canScroll,
      distance: distance ?? this.distance,
      restSecond: restSecond ?? this.restSecond,
    );
  }
}
