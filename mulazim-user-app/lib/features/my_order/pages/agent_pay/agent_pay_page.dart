import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';

import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/models/my_order/AgentPayModel.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/my_order/providers/agent_pay_provider.dart';
import 'package:user_app/features/order/providers/payment_provider.dart';
import 'package:user_app/generated/l10n.dart';

class AgentPayPage extends ConsumerStatefulWidget {
  AgentPayPage({
    super.key,
    this.param,
  });
  final Map<String, dynamic>? param;

  @override
  ConsumerState createState() => _AgentPayPageState();
}

//https://smart.mulazim.com/ug/v1/order/order-detail-another-new?order_id=13678185&user_id=370021

//{"data":{"restaurant_id":12264,"shipper_id":null,"restaurant_name":"ئاق لەيلى تائاملىرى","restaurant_address":"شىنجاڭ ئۈرۇمچى تىيانشان رايونى يەنئەن يولى شامال باغ كوچىسى 11-دۇكان","restaurant_phone":"09916111003","restaurant_state":1,"restaurant_lng":87.627959,"is_commented":false,"restaurant_logo":"https://acdn.mulazim.com/upload/restaurant/logo/202502/19/527c464d52015ae87c570d47ca323db3.png@400w_300h","restaurant_lat":43.76812,"id":13678185,"order_id":"baishukuicanyin02504211752209123","name":"ئېلشات1","mobile":"18599132990","category_id":1,"order_address":"سىناق سىناق1","pay_type":0,"pay_type_name":"","pay_platform":1,"pay_platform_label":"lakala","pay_type_list":[],"description":"(چوكا قوشۇق 2 كىشلىك)","booking_time":"18:40","timezone":8,"booking_time_cst":"04-21 18:40","booking_date_time_cst":"04-21 18:40","created_at":"2025-04-21 17:52","expired":0,"expired_time":576000,"work_wechat_state":1,"work_wechat_qrcode":"https://acdn.mulazim.com/upload/work-wechat/202402/29/5ff29640a2bbc193486fa175889ca864.png@400w_300h","service_phone":"0991-2854886","admin_name":"","admin_phone":"","is_score":0,"state":1,"shipment":4,"total_discount_amount":0,"original_shipment":4,"reduce_shipment":0,"lunch_box_fee":1,"city_id":1,"city_name":"ئۈرۈمچى","area_id":1,"area_name":"تىيانشان رايونى","street_id":93,"street_name":"يەنئەن يولى","building_id":1520,"building_name":"شىنجاڭ بىلكۈچ كۇتۇپخانىسى","delivery_type":1,"self_take_number":null,"order_detail":{"original_price":54,"price":54,"foods":[{"id":21446370,"food_id":504954,"name":"ئەنجان پولۇ","img":"https://acdn.mulazim.com/upload/restaurantfoods/202502/19/aeec05cd11beeffae1fccc2fe66b9ff2.png@400w_300h","original_price":26,"price":26,"number":1,"lunch_box_id":1,"lunch_box_count":1,"lunch_box_fee":1,"multi_discount_id":0,"multi_discount_detail_id":0},{"id":21446371,"food_id":504944,"name":"بەرەڭگە كالاگۆشى مىفەن","img":"https://acdn.mulazim.com/upload/restaurantfoods/202502/19/89ec1cee874d470c5edb30e5f223e05b.png@400w_300h","original_price":28,"price":28,"number":1,"lunch_box_id":0,"lunch_box_count":1,"lunch_box_fee":0,"multi_discount_id":0,"multi_discount_detail_id":0}]},"actual_paid":59,"order_state_log":[{"order_state":1,"name":"پۇل تۆلەشنى ساقلاۋاتىدۇ","icon":"https://acdn.mulazim.com/images/order-state-icons/new.png","color":"139d59","fail_reason":null,"created_at":"2025-04-21 17:52"}],"msg_count":0,"shipper":null,"marketing":[],"coupon":null,"other_marketing":[],"shipment_steps":[],"delayed":null,"order_state_log_new":{"items":[],"step":0},"order_detail_ranking_activity_title":"","part_refund_id":0,"part_refund_type":0,"part_refund_amount":0},"lang":"ug","msg":"مەشغۇلات مۇۋەپپەقىيەتلىك بولدى","status":200,"time":"2025-04-21 17:52:44"}

class _AgentPayPageState extends ConsumerState<AgentPayPage> {
  num restSecond = 600000;
  Timer? _expiredTimer;
  int userId = 0;
  int orderId = 0;
  int iterationCount = 0; // 防止重复查询的计数器
  bool orderHasExpired = false; // 订单是否已过期
  final GlobalKey _repaintBoundaryKey = GlobalKey(); // 用于页面截图

  /// 计算原价 - 按照微信小程序的逻辑
  /// original_price = actual_paid + 所有优惠金额
  num calculateOriginalPrice(AgentPayData? data) {
    if (data == null) return 0;

    final totalPrice = data.actualPaid ?? 0;
    num preferential = 0;

    // 计算营销活动优惠
    if (data.marketing != null) {
      for (var markt in data.marketing!) {
        if (markt is Map<String, dynamic> && markt['price'] != null) {
          preferential += num.tryParse(markt['price'].toString()) ?? 0;
        }
      }
    }

    // 计算其他营销优惠
    if (data.otherMarketing != null) {
      for (var otherMarket in data.otherMarketing!) {
        if (otherMarket is Map<String, dynamic> &&
            otherMarket['price'] != null) {
          preferential += num.tryParse(otherMarket['price'].toString()) ?? 0;
        }
      }
    }

    // 计算优惠券优惠（优惠券会被添加到marketing中，但这里单独处理以防万一）
    if (data.coupon != null && data.coupon!['price'] != null) {
      preferential += num.tryParse(data.coupon!['price'].toString()) ?? 0;
    }

    // 原价 = 实付金额 + 所有优惠金额（保留两位小数）
    final originalPrice = ((totalPrice + preferential) * 100).round() / 100;
    return originalPrice;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) async {
      final param = widget.param;
      orderId = param?['orderId'] ?? 0;
      userId = ref.read(localStorageRepositoryProvider).getUserInfo()?.id ?? 0;
      await loadAgentPayInfo();
      LoadingDialog().hide();
    });
  }

  /// 加载代理付款信息 - 与微信小程序getLoad逻辑一致
  Future<void> loadAgentPayInfo() async {
    try {
      await ref
          .read(agentPayProvider.notifier)
          .agentPayInfo(orderId: orderId, userId: userId);

      final data = ref.read(agentPayProvider).value;
      if (data == null) {
        // 数据为空表示订单已过期
        setState(() {
          orderHasExpired = true;
        });
        return;
      }

      // 检查订单过期时间
      final expiredTime = data.expiredTime ?? 0;
      if (expiredTime > 0 && iterationCount <= 1) {
        setState(() {
          restSecond = expiredTime.toDouble();
          orderHasExpired = false;
        });
        startCountdown();
      } else {
        // 订单已过期
        setState(() {
          orderHasExpired = true;
        });
      }
    } catch (e) {
      // 加载失败，可能订单已过期
      setState(() {
        orderHasExpired = true;
      });
      print('加载代理付款信息失败: $e');
    }
  }

  /// 开始倒计时 - 与微信小程序setInterval逻辑一致
  void startCountdown() {
    // 清除之前的定时器
    _expiredTimer?.cancel();

    _expiredTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (restSecond <= 0) {
          // 倒计时结束，重新加载数据检查订单状态
          timer.cancel();
          iterationCount++; // 增加计数器，防止重复查询
          _handleCountdownEnd();
        } else {
          restSecond = restSecond - 1000;
        }
      });
    });
  }

  /// 处理倒计时结束 - 与微信小程序倒计时结束逻辑一致
  void _handleCountdownEnd() async {
    print('倒计时结束，重新加载订单信息');

    // 重新加载订单信息检查状态
    try {
      await ref
          .read(agentPayProvider.notifier)
          .agentPayInfo(orderId: orderId, userId: userId);

      final data = ref.read(agentPayProvider).value;
      if (data == null || data.expired == 1) {
        // 订单已过期
        setState(() {
          orderHasExpired = true;
        });
      } else if (data.state != null && data.state! >= 3) {
        // 订单状态已变更，跳转到订单详情
        _navigateToOrderDetail();
      } else {
        // 继续等待，但标记为过期状态
        setState(() {
          orderHasExpired = true;
        });
      }
    } catch (e) {
      // 加载失败，标记为过期
      setState(() {
        orderHasExpired = true;
      });
    }
  }

  /// 跳转到订单详情 - 与微信小程序toOrder逻辑一致
  void _navigateToOrderDetail() {
    // 清除全局购物车数据（如果有的话）
    // ref.read(shoppingCartProvider.notifier).clearCardFoods();

    // 返回到首页并跳转到订单详情
    Navigator.of(context).popUntil((route) => route.isFirst);
    // 这里可以添加跳转到订单详情的逻辑
    // 例如：context.push('/orderDetail/$orderId');
  }

  /// 返回首页 - 与微信小程序backHome逻辑一致
  void _backHome() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        title: Text(
          S.current.order_detail,
          style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
        ),
      ),
      body: ref.watch(agentPayProvider).when(
        data: (final data) {
          // 如果订单已过期，显示过期UI
          if (orderHasExpired) {
            return RepaintBoundary(
              key: _repaintBoundaryKey,
              child: _buildExpiredOrderUI(),
            );
          }

          return RepaintBoundary(
            key: _repaintBoundaryKey,
            child: Stack(
              children: [
                _topPart(),
                Positioned(
                  top: 20.w,
                  right: 15.w,
                  child: Image.asset(
                    'assets/images/agentPay.png',
                    fit: BoxFit.fill,
                    width: 126.w,
                    height: 130.w,
                  ),
                ),
                _eventPanel(data),
              ],
            ),
          );
        },
        error: (final error, final stackTrace) {
          // 数据加载失败，显示错误信息或过期UI
          return orderHasExpired
              ? _buildExpiredOrderUI()
              : Center(
                  child: Column(
                    children: [
                      Text('Page Error: $error'),
                      Text('Page stackTrace: $stackTrace'),
                    ],
                  ),
                );
        },
        loading: () {
          // 正在加载，显示加载指示器
          return Center(
            child: CircularProgressIndicator(),
          );
        },
      ),
    );
  }

  Widget _eventPanel(final AgentPayData? data) {
    return Positioned(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SizedBox(
            height: 150.w,
          ),
          Container(
            margin: EdgeInsets.all(15.w),
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.w),
              color: Colors.white,
            ),
            child: Column(
              children: [
                Container(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${data?.actualPaid ?? 0}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 32.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 6.w),
                        child: Text(
                          '￥',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 20.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 10.w,
                ),
                Container(
                  child: Text(
                    S.current.actual_pay,
                    style: TextStyle(
                      color: AppColors.textSecondColor,
                      fontSize: titleSize,
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.w,
                ),
                // 只有在订单未过期时才显示倒计时
                if (!orderHasExpired) _timePart(),
                SizedBox(
                  height: 15.w,
                ),
                // 根据订单状态显示不同的按钮
                if (orderHasExpired)
                  // 订单已过期，只显示返回首页按钮
                  InkWell(
                    onTap: _backHome,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 10.w,
                        horizontal: 40.w,
                      ),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50.w),
                        color: AppColors.baseGreenColor,
                      ),
                      child: Text(
                        S.current.buy_again,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: titleSize,
                        ),
                      ),
                    ),
                  )
                else
                  // 订单未过期，显示分享和支付按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () async {
                          await shareToFriendPay();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 10.w,
                            horizontal: 40.w,
                          ),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(50.w),
                            color: AppColors.baseGreenColor,
                          ),
                          child: Text(
                            S.current.agent_share,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: titleSize,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 20.w,
                      ),
                      InkWell(
                        onTap: () {
                          // 使用微信小程序相同的原价计算逻辑
                          // 原价 = 实付金额 + 所有营销优惠金额
                          final calculatedOriginalPrice =
                              calculateOriginalPrice(data);

                          // 直接使用订单确认页面的 navigateToPaymentPage 方法
                          // 确保与订单确认页面完全相同的跳转逻辑和参数格式
                          ref
                              .read(paymentProvider.notifier)
                              .navigateToPaymentPage(
                                orderId: data?.id,
                                state: data?.state,
                                totalPrice: data?.actualPaid ?? 0,
                                originalPrice: calculatedOriginalPrice,
                              );
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 10.w,
                            horizontal: 30.w,
                          ),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(50.w),
                            color: AppColors.baseBackgroundColor,
                          ),
                          child: Text(
                            S.current.i_pay,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: titleSize,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                SizedBox(
                  height: 30.w,
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          S.current.see_order_detail,
                          style: TextStyle(
                            color: AppColors.textSecondColor,
                            fontSize: mainSize,
                          ),
                        ),
                        SizedBox(
                          width: 5.w,
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 16.sp,
                          color: AppColors.textSecondColor,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          ),
          _foodsPart(data),
        ],
      ),
    );
  }

  Widget _topPart() {
    return Container(
      alignment: Alignment.center,
      padding:
          EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w, bottom: 70.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(25.w),
          bottomLeft: Radius.circular(25.w),
        ),
        color: AppColors.baseGreenColor,
      ),
      height: 215.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            child: Text(
              S.current.order_generated,
              style: TextStyle(
                color: Colors.white,
                fontSize: titleSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(
            height: 18.w,
          ),
          Container(
            child: Text(
              S.current.please_share,
              style: TextStyle(
                color: Colors.white,
                fontSize: 17.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _foodsPart(final AgentPayData? data) {
    int allCount = 0;
    for (int i = 0; i < (data?.orderDetail?.foods ?? []).length; i++) {
      allCount = allCount + (data?.orderDetail?.foods?[i].number ?? 0);
    }
    List<Foods>? thisFood = data?.orderDetail?.foods ?? [];
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 15.w, left: 15.w, bottom: 10.w),
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.w),
          color: Colors.white,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  // color: Colors.pink,
                  border: Border(
                    bottom: BorderSide(width: 1.w, color: Color(0xffe5e5e5)),
                  ),
                ),
                padding: EdgeInsets.symmetric(vertical: 12.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      S.current.total_food,
                      style:
                          TextStyle(color: Colors.black, fontSize: titleSize),
                    ),
                    Text(
                      '$allCount',
                      style:
                          TextStyle(color: Colors.black, fontSize: titleSize),
                    ),
                    Text(
                      'x',
                      style:
                          TextStyle(color: Colors.black, fontSize: titleSize),
                    ),
                  ],
                ),
              ),
              Column(
                children: List.generate(
                  thisFood.length,
                  (final index) => _foodItem(thisFood[index], index),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _foodItem(final Foods foods, final int index) {
    return Container(
      decoration: BoxDecoration(
        // color: Colors.pink,
        border: index == 0
            ? null
            : Border(top: BorderSide(width: 1.w, color: Color(0xffe5e5e5))),
      ),
      padding: EdgeInsets.symmetric(vertical: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10.w),
            child: PrefectImage(
              imageUrl: foods.img ?? '',
              width: 80.w,
              height: 70.w,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        '${foods.name}',
                        style:
                            TextStyle(fontSize: titleSize, color: Colors.black),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${foods.price}',
                          style: TextStyle(
                            fontSize: titleSize,
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 3.w),
                          child: Text(
                            '￥',
                            style: TextStyle(
                              fontSize: secondSize,
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.w,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      '${foods.number}',
                      style: TextStyle(
                        color: AppColors.textSecondColor,
                        fontSize: mainSize,
                      ),
                    ),
                    Text(
                      'x',
                      style: TextStyle(
                        color: AppColors.textSecondColor,
                        fontSize: mainSize,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _timePart() {
    Map<String, dynamic> timeMap = millisecondsToTime(restSecond);
    String hour = timeMap['hour'];
    String minute = timeMap['minute'];
    String second = timeMap['second'];
    return InkWell(
      onTap: () async {
        // await _orderStateBottomPanel(context,data.orderStateLog,data.orderStateLog?[0].name ?? '');
      },
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 10.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                decoration: BoxDecoration(
                  color: AppColors.baseBackgroundColor,
                  borderRadius: BorderRadius.circular(4.w),
                ),
                alignment: Alignment.center,
                child: Text(
                  second,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: soBigSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                ' : ',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: soBigSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                decoration: BoxDecoration(
                  color: AppColors.baseBackgroundColor,
                  borderRadius: BorderRadius.circular(4.w),
                ),
                alignment: Alignment.center,
                child: Text(
                  minute,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: soBigSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                ' : ',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: soBigSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 2.w, horizontal: 6.w),
                decoration: BoxDecoration(
                  color: AppColors.baseBackgroundColor,
                  borderRadius: BorderRadius.circular(4.w),
                ),
                alignment: Alignment.center,
                child: Text(
                  hour,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: soBigSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 分享代付订单给朋友
  Future<void> shareToFriendPay() async {
    LoadingDialog().show();

    try {
      final data = ref.read(agentPayProvider).value;
      if (data == null) {
        BotToast.showText(text: S.current.about_share_failed);
        LoadingDialog().hide();
        return;
      }

      // 获取参数
      int agentType = 2;
      int langId = ref.watch(languageProvider) == 'zh' ? 2 : 1;

      // 截取当前页面作为缩略图
      Uint8List? pageScreenshot = await _capturePageScreenshot();

      final result = await WechatUtil().shareMiniProgram(
        thumbData: pageScreenshot ??
            await ImageUtil.getImageFromUrl(
              data.restaurantLogo ??
                  'https://cdns.mulazim.com/wechat_mini/share.jpg',
            ),
        path:
            "/pages/commitOrder/agentPay?langId=${langId}&order_id=${orderId}&user_id=${userId}&agentType=${agentType}",
        title: "Hi，帮我付款吧",
        description: '我正在点餐，请帮我付款',
      );

      if (result) {
        BotToast.showText(text: S.current.about_share_success);
      } else {
        BotToast.showText(text: S.current.about_share_failed);
      }
    } catch (e) {
      print('分享失败: $e');
      BotToast.showText(text: S.current.about_share_failed);
    }

    LoadingDialog().hide();
  }

  /// 截取页面内容为图片
  Future<Uint8List?> _capturePageScreenshot() async {
    try {
      // 获取RepaintBoundary的RenderObject
      RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        print('无法获取RepaintBoundary');
        return null;
      }

      // 转换为图片
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);

      // 转换为字节数据
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        print('图片转换失败');
        return null;
      }

      final pngBytes = byteData.buffer.asUint8List();

      // 压缩图片以适应微信分享的要求（最大32KB）
      return await ImageUtil.createWeChatThumbnail(pngBytes);
    } catch (e) {
      print('页面截图失败: $e');
      return null;
    }
  }

  Map<String, dynamic> millisecondsToTime(final num milliseconds) {
    int totalSeconds = milliseconds ~/ 1000;
    int hours = totalSeconds ~/ 3600;
    int remainingSecondsAfterHours = totalSeconds % 3600;
    int minutes = remainingSecondsAfterHours ~/ 60;
    int seconds = remainingSecondsAfterHours % 60;

    String hoursStr = hours.toString().padLeft(2, '0');
    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = seconds.toString().padLeft(2, '0');

    return {'hour': hoursStr, 'minute': minutesStr, 'second': secondsStr};
  }

  /// 构建订单过期UI - 与微信小程序orderHasExpired逻辑一致
  Widget _buildExpiredOrderUI() {
    return Stack(
      children: [
        _topPart(),
        Positioned(
          top: 20.w,
          right: 15.w,
          child: Image.asset(
            'assets/images/agentPay.png',
            fit: BoxFit.fill,
            width: 126.w,
            height: 130.w,
          ),
        ),
        Positioned(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(height: 150.w),
              Container(
                margin: EdgeInsets.all(15.w),
                padding: EdgeInsets.all(30.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.w),
                  color: Colors.white,
                ),
                child: Column(
                  children: [
                    // 过期警告图标
                    Icon(
                      Icons.error,
                      size: 120.w,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 20.w),
                    // 过期提示文字
                    Text(
                      S.current.order_expired, // 订单已过期文字
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: titleSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 30.w),
                    // 返回首页按钮
                    InkWell(
                      onTap: _backHome,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          vertical: 12.w,
                          horizontal: 40.w,
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50.w),
                          color: AppColors.baseGreenColor,
                        ),
                        child: Text(
                          S.current.buy_again, // 重新点餐
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: titleSize,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _expiredTimer?.cancel();
    super.dispose();
  }
}
