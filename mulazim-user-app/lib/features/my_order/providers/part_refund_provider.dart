import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/my_order/part_refund_detail_model.dart';
import 'package:user_app/data/repositories/home/<USER>';
///获取部分退款
Future<PartRefundDetailData?> getPartRefundInfo(Ref ref,{required int id}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'id': id
  };
  // 订单详情
  final partRefundRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final partRefundDetail = await partRefundRepository.partRefundInfo(param);
  return partRefundDetail.data;
}


///部分退款数据提供者类
class PartRefundProvider extends StateNotifier<AsyncValue<PartRefundDetailData?>> {
  PartRefundProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> partRefundInfo({required int id}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getPartRefundInfo(ref, id: id);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

}

final partRefundProvider = StateNotifierProvider.autoDispose<
    PartRefundProvider, AsyncValue<PartRefundDetailData?>>(
  (ref) => PartRefundProvider(ref),
);

