import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/data/models/my_order/shipper_position_model.dart';
import 'package:user_app/features/my_order/pages/index/my_order_controller.dart';
import 'package:user_app/features/my_order/services/my_order_service.dart';

/// 订单详情提供者类
class OrderDetailProvider extends StateNotifier<AsyncValue<OrderDetailData?>> {
  OrderDetailProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref;

  Future<void> orderDetailInfo({required int orderId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service获取订单详情
      final orderService = ref.read(myOrderServiceProvider);
      final response = await orderService.getOrderDetail(orderId: orderId);

      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> cancelOrder({required int orderId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service取消订单 (unsubscribe)
      final orderService = ref.read(myOrderServiceProvider);
      await orderService.cancelOrder(orderId: orderId);
      // 更新我的订单列表
      ref.read(myOrderControllerProvider.notifier).refreshTodayOrders();
    } catch (error, stackTrace) {
      // 错误处理已经在Service层完成
    }
  }

  Future<void> closeOrder({required int orderId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service关闭订单 (close)
      final orderService = ref.read(myOrderServiceProvider);
      await orderService.closeOrder(orderId: orderId);
      // 更新我的订单列表
      ref.read(myOrderControllerProvider.notifier).refreshTodayOrders();
    } catch (error, stackTrace) {
      // 错误处理已经在Service层完成
    }
  }

  Future<void> delOrder({required int orderId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service取消订单
      final orderService = ref.read(myOrderServiceProvider);
      await orderService.delOrder(orderId: orderId);
    } catch (error, stackTrace) {
      // 错误处理已经在Service层完成
    }
  }

  Future<void> confirmDelay({required String rid}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service取消订单
      final orderService = ref.read(myOrderServiceProvider);
      await orderService.confirmDelay(rid: rid);
    } catch (error, stackTrace) {
      // 错误处理已经在Service层完成
    }
  }
}

final orderDetailProvider = StateNotifierProvider.autoDispose<
    OrderDetailProvider, AsyncValue<OrderDetailData?>>(
  (ref) => OrderDetailProvider(ref),
);

/// 配送员位置信息提供者
class ShipperPositionProvider
    extends StateNotifier<AsyncValue<ShipperPositionData?>> {
  ShipperPositionProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref;

  Future<void> shipperPostionInfo({required int orderId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;

      // 使用Service获取配送员位置信息
      final orderService = ref.read(myOrderServiceProvider);
      final response =
          await orderService.getShipperPositionInfo(orderId: orderId);

      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final shipperPositionProvider = StateNotifierProvider<ShipperPositionProvider,
    AsyncValue<ShipperPositionData?>>(
  (ref) => ShipperPositionProvider(ref),
);
