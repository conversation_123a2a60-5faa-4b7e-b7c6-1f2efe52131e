import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/models/my_order/AgentPayModel.dart';
import 'package:user_app/data/repositories/home/<USER>';
///获取订单详情
Future<AgentPayData?> getAgentPayInfo(Ref ref,{required int orderId, required int userId}) async {
  // 构建请求参数
  final Map<String, dynamic> param = {
    'order_id': orderId,
    'user_id': userId,
  };
  // 订单详情
  final homeRepository = HomeRepository(apiClient: ref.read(apiClientProvider));
  final agentPayDetail = await homeRepository.getAgentPayInfo(param);
  return agentPayDetail?.data;
}


///地址列表按定位数据提供者类
class AgentPayProvider extends StateNotifier<AsyncValue<AgentPayData?>> {
  AgentPayProvider(this.ref) : super(const AsyncValue.loading());
  final Ref ref; // 添加一个 ref 字段
  Future<void> agentPayInfo({required int orderId, required int userId}) async {
    try {
      // 进行异步请求前先检查是否 mounted
      if (!mounted) return;
      final response = await getAgentPayInfo(ref, orderId: orderId,userId: userId);
      state = AsyncValue.data(response);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

}

final agentPayProvider = StateNotifierProvider.autoDispose<
    AgentPayProvider, AsyncValue<AgentPayData?>>(
  (ref) => AgentPayProvider(ref),
);

