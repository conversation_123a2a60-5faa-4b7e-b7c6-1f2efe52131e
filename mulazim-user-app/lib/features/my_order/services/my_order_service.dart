import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/core/network/result/api_result.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/data/models/my_order/my_order_model.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/data/models/my_order/shipper_position_model.dart';
import 'package:user_app/data/repositories/my_order/my_order_repository.dart';

part 'my_order_service.g.dart';

/// 订单服务，处理业务逻辑
@riverpod
MyOrderService myOrderService(MyOrderServiceRef ref) {
  return MyOrderService(
    repository: MyOrderRepository(apiClient: ref.watch(apiClientProvider)),
  );
}

class MyOrderService {
  final MyOrderRepository _repository;

  MyOrderService({required MyOrderRepository repository})
      : _repository = repository;

  /// 获取我的订单列表
  /// [type] - 订单类型，1:今日订单，2:所有订单
  /// [page] - 页码
  /// [limit] - 每页数量
  Future<ApiResult<MyOrderData>> getMyOrder({
    required int type,
    required int page,
    int limit = 10,
  }) async {
    // 构建请求参数
    final Map<String, dynamic> params = {
      'type': type,
      'page': page,
      'limit': limit,
    };

    // 发起请求获取订单数据
    return await _repository.getMyOrder(params);
  }

  /// 检查是否可以加载更多
  bool canLoadMore(List<MyOrderItems>? items) {
    return (items?.length ?? 0) >= 10;
  }

  /// 取消订单 (unsubscribe - 用户主动取消，state_id == 3时使用)
  Future<bool> cancelOrder({required int orderId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'order_id': orderId,
      };

      // 发起请求取消订单
      final response = await _repository.cancelOrder(params);
      final success = (response?.status ?? 0) == 200;

      // 显示提示信息
      if (success) {
        BotToast.showText(text: "مەشغۇلات مۇۋاپىقىيەتلىك بولدى");
      } else {
        BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      }

      return success;
    } catch (e) {
      // 显示错误提示
      BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      return false;
    }
  }

  /// 关闭订单 (close - 商家可关闭，can_close == 1时使用)
  Future<bool> closeOrder({required int orderId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'order_id': orderId,
      };

      // 发起请求关闭订单
      final response = await _repository.closeOrder(params);
      final success = (response?.status ?? 0) == 200;

      // 显示提示信息
      if (success) {
        BotToast.showText(text: "مەشغۇلات مۇۋاپىقىيەتلىك بولدى");
      } else {
        BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      }

      return success;
    } catch (e) {
      // 显示错误提示
      BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      return false;
    }
  }

  /// 删除订单
  Future<bool> delOrder({required int orderId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'order_id': orderId,
      };

      // 发起请求取消订单
      final response = await _repository.delOrder(params);
      final success = (response?.status ?? 0) == 200;

      // 显示提示信息
      if (success) {
        BotToast.showText(text: "مەشغۇلات مۇۋاپىقىيەتلىك بولدى");
      } else {
        BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      }

      return success;
    } catch (e) {
      // 显示错误提示
      BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      return false;
    }
  }

  /// 同意要求
  Future<bool> confirmDelay({required String rid}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'rid': rid,
      };

      // 发起请求取消订单
      final response = await _repository.confirmDelay(params);
      final success = (response.status) == 200;

      // 显示提示信息
      if (success) {
        BotToast.showText(text: "مەشغۇلات مۇۋاپىقىيەتلىك بولدى");
      } else {
        BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      }

      return success;
    } catch (e) {
      // 显示错误提示
      BotToast.showText(text: "مەشغۇلات مەغلۇپ بولدى");
      return false;
    }
  }

  /// 获取订单详情
  Future<OrderDetailData?> getOrderDetail({required int orderId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'order_id': orderId,
      };

      // 发起请求获取订单详情
      final response = await _repository.getOrderDetail(params);
      return response?.data;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取配送员位置信息
  Future<ShipperPositionData?> getShipperPositionInfo(
      {required int orderId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        'order_id': orderId,
      };

      // 发起请求获取配送员位置信息
      final response = await _repository.getShipperPositionInfo(params);
      return response?.data;
    } catch (e) {
      rethrow;
    }
  }
}
