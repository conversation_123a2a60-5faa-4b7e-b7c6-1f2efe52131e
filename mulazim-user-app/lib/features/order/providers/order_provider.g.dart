// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$submitOrderInfoHash() => r'dcdf4a9c15ae4ab2f5ea90f727330940c15f2d0a';

/// 提交订单页面数据提供者
///
/// Copied from [SubmitOrderInfo].
@ProviderFor(SubmitOrderInfo)
final submitOrderInfoProvider = AutoDisposeAsyncNotifierProvider<
    SubmitOrderInfo, TakeTimeListData>.internal(
  SubmitOrderInfo.new,
  name: r'submitOrderInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$submitOrderInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SubmitOrderInfo = AutoDisposeAsyncNotifier<TakeTimeListData>;
String _$currentAddressHash() => r'897fc0b2d2afc2b04be8d9ddf18fd79a3b378d04';

/// 当前选中的地址
///
/// Copied from [CurrentAddress].
@ProviderFor(CurrentAddress)
final currentAddressProvider =
    AutoDisposeNotifierProvider<CurrentAddress, CurrentSelectAddress>.internal(
  CurrentAddress.new,
  name: r'currentAddressProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAddressHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAddress = AutoDisposeNotifier<CurrentSelectAddress>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
