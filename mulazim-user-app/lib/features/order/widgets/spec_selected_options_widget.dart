import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 规格选择选项显示组件
/// 用于显示商品的已选择规格信息，与微信小程序逻辑一致
class SpecSelectedOptionsWidget extends StatelessWidget {
  /// 规格选择选项列表
  final List<Map<String, dynamic>> specSelectedOptions;

  /// 是否显示背景容器
  final bool showBackground;

  /// 字体大小
  final double? fontSize;

  /// 文字颜色
  final Color? textColor;

  /// 背景颜色
  final Color? backgroundColor;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 构造函数
  const SpecSelectedOptionsWidget({
    super.key,
    required this.specSelectedOptions,
    this.showBackground = true,
    this.fontSize,
    this.textColor,
    this.backgroundColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (specSelectedOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    List<Widget> specWidgets = [];

    for (int index = 0; index < specSelectedOptions.length; index++) {
      final spec = specSelectedOptions[index];
      final specName = spec['name']?.toString() ?? '';

      if (specName.isNotEmpty) {
        // 添加规格名称
        specWidgets.add(
          Text(
            specName,
            style: TextStyle(
              fontSize: fontSize ?? 14.sp,
              color: textColor ?? const Color(0xFF8D8C8C),
              fontWeight: FontWeight.w600,
            ),
          ),
        );

        // 如果不是最后一个，添加分隔符
        if (index < specSelectedOptions.length - 1) {
          specWidgets.add(
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(
                '|',
                style: TextStyle(
                  fontSize: fontSize ?? 14.sp,
                  color: textColor ?? const Color(0xFF8D8C8C),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        }
      }
    }

    final content = Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      runSpacing: 2.h,
      children: specWidgets, // 行间距
    );

    if (showBackground) {
      return Container(
        width: double.infinity,
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 8.w, vertical: 5.h),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.grey.shade100,
          borderRadius: BorderRadius.all(Radius.circular(5.r)),
        ),
        child: content,
      );
    } else {
      return content;
    }
  }

  /// 便捷构造函数 - 带背景样式（用于订单页面）
  factory SpecSelectedOptionsWidget.withBackground({
    required List<Map<String, dynamic>> specSelectedOptions,
    double? fontSize,
    Color? textColor,
    Color? backgroundColor,
    EdgeInsetsGeometry? padding,
  }) {
    return SpecSelectedOptionsWidget(
      specSelectedOptions: specSelectedOptions,
      showBackground: true,
      fontSize: fontSize,
      textColor: textColor,
      backgroundColor: backgroundColor,
      padding: padding,
    );
  }

  /// 便捷构造函数 - 无背景样式（用于订单详情页面）
  factory SpecSelectedOptionsWidget.withoutBackground({
    required List<Map<String, dynamic>> specSelectedOptions,
    double? fontSize,
    Color? textColor,
  }) {
    return SpecSelectedOptionsWidget(
      specSelectedOptions: specSelectedOptions,
      showBackground: false,
      fontSize: fontSize,
      textColor: textColor,
    );
  }
}
