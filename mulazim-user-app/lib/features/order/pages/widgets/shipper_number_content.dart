import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/widgets/dash_painter.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/order/pages/dialogs/shipper_mobile_dialog.dart';
import 'package:user_app/features/order/providers/order_provider.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/generated/l10n.dart';

/// 骑手编号内容组件
class ShipperNumberContent extends ConsumerWidget {
  /// 配送模式 - 用于判断是否显示该组件
  final int deliveryMode;

  /// 构造函数
  const ShipperNumberContent({
    super.key,
    required this.deliveryMode,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 获取骑手信息
    final hasShipperNumber = ref.watch(hasShipperNumberProvider);
    final shipperMobile = ref.watch(shipperMobileProvider);

    // 判断是否为配送模式
    final isDeliveryMode =
        deliveryMode == OrderCalculationConstants.deliveryMode;

    // 只在配送模式下显示骑手选择功能
    if (!isDeliveryMode) {
      return SizedBox.shrink();
    }

    // 已添加骑手的情况
    if (hasShipperNumber) {
      return _buildSelectedShipperContent(context, ref, shipperMobile);
    }

    // 未添加骑手的情况
    return _buildAddShipperContent(ref, context);
  }

  /// 构建已选择骑手的内容
  Widget _buildSelectedShipperContent(final BuildContext context,
      final WidgetRef ref, final String shipperMobile) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h, top: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          // 骑手标题和删除按钮
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.shipper_selected_title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // 删除骑手信息
                    ref.read(hasShipperNumberProvider.notifier).state = false;
                    ref.read(shipperMobileProvider.notifier).state = "";
                  },
                  child: Text(
                    S.current.shipper_del_number,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.redColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          CustomPaint(
            painter: DashPainter(
              color: Colors.grey.shade200,
              strokeWidth: 1.h,
              borderRadius: 0,
              dashPattern: [1, 1],
            ),
            size: Size(double.infinity, 1.h),
          ),
          // 骑手电话号码和修改按钮
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.shipper_mobile,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondaryColor,
                  ),
                ),
                SizedBox(width: 20.w),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7.r), // 14rpx
                      color: const Color(0xFFEFF1F6), // #eff1f6
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: 13.w, vertical: 10.h), // 26rpx, 20rpx
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          shipperMobile,
                          style: TextStyle(
                            fontSize: 17.sp,
                            color: AppColors.textPrimaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            // 修改骑手信息
                            ShipperMobileDialog.show(context);
                          },
                          child: Text(
                            S.current.change,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建添加骑手的内容
  Widget _buildAddShipperContent(
      final WidgetRef ref, final BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 获取骑手信息
        final shipperMobile =
            ref.read(localStorageRepositoryProvider).getShipperNumber();
        if (shipperMobile == null) {
          ShipperMobileDialog.show(context);
        } else {
          ref.read(hasShipperNumberProvider.notifier).state = true;
          ref.read(shipperMobileProvider.notifier).state = shipperMobile;
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 70.h, top: 10.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        width: double.infinity,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/order/addShipper.png',
              width: 20.w,
              height: 20.w,
            ),
            SizedBox(width: 10.w),
            Text(
              S.current.shipper_select_title,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
