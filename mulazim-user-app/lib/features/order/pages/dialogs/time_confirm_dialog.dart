import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/widgets/dialogs/confirm_dialog.dart';
import 'package:user_app/generated/l10n.dart';

/// 时间确认对话框
/// 当订单配送时间与当前时间的差值大于等于120分钟时显示
class TimeConfirmDialog extends StatelessWidget {
  final String timeTitle;
  final String deliveryDay;
  final String hourMinute;
  final bool isUg;

  /// 构造函数
  const TimeConfirmDialog({
    required this.timeTitle,
    required this.deliveryDay,
    required this.hourMinute,
    required this.isUg,
    super.key,
  });

  /// 显示对话框
  static Future<bool?> show({
    required final BuildContext context,
    required final String timeTitle,
    required final String deliveryDay,
    required final String hourMinute,
  }) async {
    final isUg = Localizations.localeOf(context).languageCode == 'en';

    // 构建对话框内容
    String content = '';
    if (isUg) {
      // 维语内容
      content =
          'سىز چۈشۈرگەن زاكاز $deliveryDay <span style="color:#15C55C">$hourMinute</span> مىنۇت ئۆتكەندە يەتكۈزۈلىدۇ. يەتكۈزۈش ۋاقتىغا يەنە <span style="color:#15C55C">$timeTitle</span> قالدى، جەزملەشتۈرەمسىز؟';
    } else {
      // 中文内容
      content =
          '您的订单$deliveryDay<span style="color:#15C55C">$hourMinute送达。</span> 离送达时间还剩<span style="color:#15C55C">$timeTitle</span>是否确定？';
    }

    // 创建富文本内容
    final richText = RichText(
      text: TextSpan(
        children: _parseTextSpan(content),
        style: TextStyle(
          fontSize: 20.sp,
          color: Colors.black87,
          height: 1.5,
          fontFamily: AppConstants.mainFont,
        ),
      ),
      textAlign: TextAlign.center,
    );

    // 使用ConfirmDialog显示对话框
    return ConfirmDialog.show(
      context,
      title: S.current.dialog_title_info,
      content: richText,
      confirmText: S.current.dialog_text_yes,
      cancelText: S.current.dialog_text_no,
    );
  }

  @override
  Widget build(final BuildContext context) {
    // 这个方法不会被直接使用，我们通过静态show方法来显示对话框
    return Container(); // 永远不会被渲染
  }

  /// 解析带有HTML标签的文本为TextSpan列表
  static List<TextSpan> _parseTextSpan(final String htmlText) {
    final List<TextSpan> spans = [];

    // 简单解析含有<span>标签的文本
    final RegExp regExp = RegExp(r'<span style="color:(.*?)">(.*?)</span>');

    int lastIndex = 0;

    // 找出所有匹配的标签
    for (Match match in regExp.allMatches(htmlText)) {
      // 添加标签前的普通文本
      if (match.start > lastIndex) {
        spans.add(TextSpan(
          text: htmlText.substring(lastIndex, match.start),
        ));
      }

      // 添加带有颜色的文本
      final String color = match.group(1) ?? '#000000';
      final String text = match.group(2) ?? '';

      spans.add(TextSpan(
        text: text,
        style: TextStyle(
          color: _parseColor(color),
          fontWeight: FontWeight.bold,
          fontFamily: AppConstants.mainFont,
        ),
      ));

      lastIndex = match.end;
    }

    // 添加最后一段普通文本
    if (lastIndex < htmlText.length) {
      spans.add(TextSpan(
        text: htmlText.substring(lastIndex),
      ));
    }

    return spans;
  }

  /// 将颜色字符串解析为Color对象
  static Color _parseColor(final String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceFirst('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      // 基本颜色名称支持
      if (colorString == '#15C55C') {
        return const Color(0xFF15C55C);
      }
      return Colors.black;
    } catch (e) {
      return Colors.black;
    }
  }
}
