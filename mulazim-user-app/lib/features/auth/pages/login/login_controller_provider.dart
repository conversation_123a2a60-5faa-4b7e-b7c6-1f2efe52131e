import 'dart:async';

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/address/providers/address_provider.dart';

import 'package:user_app/features/auth/pages/login/login_state.dart';
import 'package:user_app/features/auth/providers/auth_provider.dart';
import 'package:user_app/generated/l10n.dart';

part 'login_controller_provider.g.dart';

/// 登录控制器提供者
///
/// 负责管理登录页面的状态
@riverpod
class LoginController extends _$LoginController {
  /// 用于计时的定时器
  Timer? _timer;

  /// 手机号输入控制器
  final TextEditingController phoneController = TextEditingController();

  /// 验证码输入控制器
  final TextEditingController codeController = TextEditingController();

  @override
  LoginState build() {
    ref.onDispose(() {
      _timer?.cancel();
      phoneController.dispose();
      codeController.dispose();
    });

    return LoginState();
  }

  void setChangeAgreeState(bool? value) {
    _updateState(state.copyWith(isAgreed: value));
  }

  /// 发送短信验证码
  Future<void> sendSmsCode() async {
    final phone = phoneController.text;

    if (state.isSending) return;

    try {
      _updateState(state.copyWith(isLoading: true));

      // 调用认证服务发送验证码
      final auth = ref.read(authProvider.notifier);
      final result = await auth.sendSmsCode(phone);

      if (result.success) {
        _updateState(state.copyWith(
          isSending: true,
          isLoading: false,
          hasSentCode: true,
        ));
        _startTimer();
      } else {
        _updateState(state.copyWith(
          isLoading: false,
          errorMessage: result.msg,
        ));
      }
    } catch (e) {
      _updateState(state.copyWith(
        isLoading: false,
        errorMessage: S.current.sms_code_send_retry,
      ));
    }
  }

  /// 验证码登录
  Future<void> smsLogin(final BuildContext context) async {
    final phone = phoneController.text;
    final code = codeController.text;

    try {
      _updateState(state.copyWith(isLoading: true));

      // 调用认证服务登录
      final auth = ref.read(authProvider.notifier);
      final result = await auth.smsLogin(phone, code);

      if (result.success && result.data != null) {
        final token = result.data!.tokens?.accessToken ?? "";
        final userInfo = result.data!.user;

        _updateState(state.copyWith(
          isLoggedIn: true,
          isLoading: false,
          token: token,
          user: userInfo,
          isSending: false,
          countDown: 60,
        ));

        // 清空输入
        phoneController.clear();
        codeController.clear();

        // 取消定时器
        _timer?.cancel();
        _timer = null;
        await Future.delayed(Duration(milliseconds: 300));
        await ref
            .read(historyAddressListProvider.notifier)
            .fetchHistoryAddressData();
        // 登录成功后的导航在LoginPage中处理
      } else {
        _updateState(state.copyWith(
          isLoading: false,
          errorMessage: result.msg,
        ));
      }
    } catch (e) {
      _updateState(state.copyWith(
        isLoading: false,
        errorMessage: S.current.login_retry,
      ));
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      _updateState(state.copyWith(isLoading: true));

      // 调用认证服务登出
      final auth = ref.read(authProvider.notifier);
      await auth.logout();

      _updateState(state.copyWith(
        isLoggedIn: false,
        isLoading: false,
        token: '',
        user: null,
      ));
    } catch (e) {
      _updateState(state.copyWith(
        isLoading: false,
        errorMessage: S.current.logout_failed,
      ));
    }
  }

  /// 检查登录状态
  Future<void> checkLoginStatus() async {
    try {
      // 调用认证服务检查登录状态
      final auth = ref.read(authProvider.notifier);
      final userInfo = await auth.checkLoginStatus();

      if (userInfo != null) {
        final isLoggedIn = ref.read(authProvider);
        _updateState(state.copyWith(
          isLoggedIn: isLoggedIn,
          user: userInfo,
        ));
      } else {
        ref.read(localStorageRepositoryProvider).clearUserInfo();
        _updateState(state.copyWith(
          isLoggedIn: false,
          token: '',
          user: null,
        ));
      }
    } catch (e) {
      _updateState(state.copyWith(
        isLoggedIn: false,
        token: '',
        user: null,
      ));
    }
  }

  /// 更新状态
  void _updateState(final LoginState newState) {
    state = newState;
  }

  /// 重置登录状态
  void resetLoginState() {
    _timer?.cancel();
    _timer = null;

    _updateState(state.copyWith(
      isSending: false,
      countDown: 60,
      hasSentCode: false,
    ));
  }

  /// 验证码倒计时
  void _startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (final timer) {
      if (state.countDown <= 1) {
        timer.cancel();
        _updateState(state.copyWith(
          isSending: false,
          countDown: 60,
        ));
      } else {
        _updateState(state.copyWith(
          countDown: state.countDown - 1,
        ));
      }
    });
  }
}
