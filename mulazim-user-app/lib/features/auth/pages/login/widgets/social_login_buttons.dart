import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 微信登录按钮
        GestureDetector(
          onTap: () {
            BotToast.showText(text: "微信登录");
            // TODO: 实现微信登录
          },
          child: Image.asset(
            "assets/images/basic/wechat_login.png", 
            width: 48.w
          )
        ),
        
        SizedBox(width: 60.w),
        
        // 苹果登录按钮
        GestureDetector(
          onTap: () {
            BotToast.showText(text: "苹果登录");
            // TODO: 实现苹果登录
          },
          child: Image.asset(
            "assets/images/basic/apple_login.png", 
            width: 48.w
          )
        ),
      ],
    );
  }
} 