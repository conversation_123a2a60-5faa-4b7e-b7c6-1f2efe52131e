
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';


class TextFieldWidget extends StatefulWidget {
  final TextEditingController controller;
  final int length;
  final String? hintText;
  final bool isNumber;
  final String iconImage;
  final  bool enabledBorder;
  final bool isPassword;
  final bool focusedBorder;

  const TextFieldWidget({
    super.key,
    required this.controller,
    this.length = 20,
    this.hintText,
    this.isNumber = true,
    this.enabledBorder = true,
    required this.iconImage,
    this.focusedBorder = true,
    this.isPassword = false,
  });

  @override
  _TextFieldWidgetState createState() => _TextFieldWidgetState();
}



class _TextFieldWidgetState extends State<TextFieldWidget> {
  bool showPassword = true;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      decoration: BoxDecoration(
        color: const Color(0xffEFF1F6),
        borderRadius: BorderRadius.circular(44.0),
      ),
      child: TextField(
        controller: widget.controller,
        style: TextStyle(fontSize: 16.sp),
        textDirection: TextDirection.ltr,
        cursorColor:  AppColors.primary, 
        obscureText: (widget.isPassword && showPassword),
        decoration: InputDecoration(
          prefixIcon: Container(
            padding: const EdgeInsets.all(15),
            child: ImageIcon(
              AssetImage(widget.iconImage),
              color: AppColors.textHintColor, 
            ),
          ),
          contentPadding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 20.w),
          hintText: widget.hintText,
          hintStyle: TextStyle(fontSize: 14.sp, color: AppColors.textHintColor), 
          enabledBorder: const OutlineInputBorder(borderSide: BorderSide.none),
          focusedBorder: const OutlineInputBorder(borderSide: BorderSide.none),
          suffixIcon: widget.isPassword
              ? GestureDetector(
                  onTap: (){
                    setState(() {
                      showPassword = !showPassword;
                    });
                  },
                  child: Icon(
                    Icons.remove_red_eye,
                    color: !showPassword ? Colors.red : Colors.grey,
                  ),
                )
              : null,
        ),
        keyboardType: widget.isNumber ? TextInputType.number : null,
        inputFormatters: [
          FilteringTextInputFormatter.deny(RegExp(r'[.]{2,}')), // Disallow multiple decimal points
          LengthLimitingTextInputFormatter(widget.length),
        ],
      ),
    );
  }
}

   Widget loginPageButton({required VoidCallback onTap, required String title}) {
    return InkWell(
      onTap: ()=> onTap.call(),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 30.h),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: AppColors.primary, borderRadius: BorderRadius.circular(44)),
        child: Text(
          title,
          style: TextStyle(fontSize: 18.sp, color: Colors.white),
        ),
      ),
    );
  }