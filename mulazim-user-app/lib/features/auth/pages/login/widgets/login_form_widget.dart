import 'dart:math';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/features/auth/pages/login/login_controller_provider.dart';
import 'package:user_app/features/auth/pages/login/widgets/agreement_widget.dart';
import 'package:user_app/features/auth/pages/login/widgets/login_form.dart';
import 'package:user_app/features/auth/pages/login/widgets/login_button.dart';
// import 'package:user_app/features/auth/pages/login/widgets/social_login_buttons.dart';
import 'package:user_app/generated/l10n.dart';

class LoginFormWidget extends ConsumerStatefulWidget {
  final ScrollController scrollController;
  const LoginFormWidget({super.key, required this.scrollController});

  @override
  ConsumerState createState() => _LoginFormWidgetState();
}

class _LoginFormWidgetState extends ConsumerState<LoginFormWidget>
    with TickerProviderStateMixin {
  // 动画相关
  final shakeDuration = const Duration(milliseconds: 500); // 动画播放长度
  int shakeCount = 4; // 震动次数
  late final AnimationController _shakeController = // 震动动画控制器
      AnimationController(vsync: this, duration: shakeDuration);

  @override
  Widget build(final BuildContext context) {
    // 只读取控制器，而不监听状态变化
    final loginController = ref.read(loginControllerProvider.notifier);
    // 只监听登录按钮需要的状态
    final isLoading =
        ref.watch(loginControllerProvider.select((s) => s.isLoading));
    final isAgreed =
        ref.watch(loginControllerProvider.select((s) => s.isAgreed));
    return Column(
      children: [
        // 登录表单 - 已经在内部监听需要的状态
        LoginForm(
          phoneController: loginController.phoneController,
          codeController: loginController.codeController,
          onSendCode: () {
            loginController.sendSmsCode();
            // 滚动到底部
            widget.scrollController.animateTo(
              widget.scrollController.position.maxScrollExtent,
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
        ),

        SizedBox(
          height: 20.w,
        ),

        Container(
          // color: Colors.yellow,
          alignment: Alignment.center,
          // margin: EdgeInsets.symmetric(horizontal: 24.w),
          padding: EdgeInsets.symmetric(vertical: 12.w),
          child: // 协议
              AnimatedBuilder(
                  animation: _shakeController,
                  builder: ((context, child) {
                    final sineValue =
                        sin(shakeCount * 2 * pi * _shakeController.value);
                    return Transform.translate(
                      offset: Offset(sineValue * 10, 0),
                      child: AgreementWidget(
                        agreement: isAgreed!,
                        lang: ref.watch(languageProvider),
                        checkSubmit: (e) {
                          setState(() {
                            loginController.setChangeAgreeState(e);
                          });
                        },
                      ),
                    );
                  })),

          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     RoundCheckBox(
          //         size: 18,
          //         checkedWidget: const Icon(Icons.check, color: Colors.white, size: 14),
          //         checkedColor: Theme.of(context).primaryColor,
          //         border: Border.all(color: isAgreed! ? AppColors.baseGreenColor : AppColors.textSecondaryColor, width: 1.3.w),
          //         isChecked: isAgreed,
          //         onTap: (selected) {
          //            loginController.setChangeAgreeState(selected);
          //         }
          //     ),
          //     SizedBox(width: 10.w,),
          //     if(ref.watch(languageProvider) != 'ug')
          //     Text(S.current.agree_privacy,style: TextStyle(color: Colors.black,fontSize: mainSize),),
          //     InkWell(
          //         onTap: (){
          //           router.push(AppPaths.webViewPage, extra: {
          //             'url': 'https://cer.mulazim.com/privacy',
          //             'title': S.current.app_name,
          //           });
          //         },
          //         child: Text(S.current.mulazim_privacy,style: TextStyle(color: AppColors.baseGreenColor,fontSize: mainSize),)
          //     ),
          //     if(ref.watch(languageProvider) == 'ug')
          //       Text(S.current.agree_privacy,style: TextStyle(color: Colors.black,fontSize: mainSize),),
          //   ],
          // )
        ),

        // 登录按钮
        Container(
          margin: EdgeInsets.symmetric(vertical: 50.w),
          child: LoginButton(
              isLoading: isLoading,
              onLogin: () {
                if (isAgreed!) {
                  loginController.smsLogin(context);
                } else {
                  // _shake();
                  BotToast.showText(text: S.current.privacy_alert);
                }
              }),
        ),

        // Divider(color: Colors.grey.shade100),
        // SizedBox(height: 15.h),

        // // 社交登录按钮
        // const SocialLoginButtons(),
      ],
    );
  }

  /// 执行震动动画
  // void _shake() {
  //   _shakeController.forward();
  //   return;
  // }
}
