# ApiResult 重构方案（最终版）

## 背景

当前项目中使用 `ApiResult` 类（sealed class）和 `when` 方法处理 API 响应，这种方式导致代码冗长且难以理解。同时，项目中还存在 `ApiResponse` 和 `ApiError` 类，增加了代码的复杂性和混淆性。我们需要简化 API 响应处理，统一使用一个类来表示 API 响应。

## 当前问题

1. **使用 `when` 方法处理 ApiResult 导致代码冗长**
2. **多个响应模型（ApiResult、ApiResponse、ApiError）导致混淆**
3. **错误处理不一致**
4. **代码重复**
5. **错误码没有明确的含义**

## 目标

1. 简化 API 响应处理，去掉 `when` 方法
2. 统一使用一个类（ApiResult）表示 API 响应，移除 ApiResponse 和 ApiError 类
3. 统一错误处理策略，使用有意义的错误码
4. 避免代码重复
5. 提高代码可读性和可维护性

## 重构方案

### 1. 定义错误码常量

```dart
// 定义错误码常量
class ApiErrorCode {
  // 成功状态码
  static const int SUCCESS = 200;

  // 错误状态码
  static const int BUSINESS_ERROR = -1000; // 业务错误
  static const int NETWORK_ERROR = 1001;   // 网络连接错误
  static const int TIMEOUT_ERROR = 1002;   // 请求超时
  static const int PARSING_ERROR = 1003;   // 数据解析错误

  // 常用HTTP状态码
  static const int UNAUTHORIZED = 401;
  static const int NOT_FOUND = 404;
  static const int SERVER_ERROR = 500;
}

class ApiResult<T> {
  // 响应数据
  final T? data;

  // 响应消息
  final String msg;

  // 响应状态码
  final int status;

  // 响应时间戳
  final String time;

  // 响应语言
  final String lang;

  // 构造函数
  ApiResult({
    this.data,
    required this.msg,
    required this.status,
    this.time = '',
    this.lang = 'ug',
  });

  // 是否成功
  bool get isSuccess => status == ApiErrorCode.SUCCESS;

  // 是否是网络错误
  bool get isNetworkError => status == ApiErrorCode.NETWORK_ERROR;

  // 是否是超时错误
  bool get isTimeoutError => status == ApiErrorCode.TIMEOUT_ERROR;

  // 创建成功结果
  factory ApiResult.success(T data, {String msg = '', String time = '', String lang = 'ug'}) {
    return ApiResult(
      data: data,
      msg: msg,
      status: ApiErrorCode.SUCCESS,
      time: time.isEmpty ? DateTime.now().toString() : time,
      lang: lang,
    );
  }

  // 创建错误结果（统一的错误工厂方法）
  factory ApiResult.error({
    required String msg,
    int status = ApiErrorCode.BUSINESS_ERROR,
    String time = '',
    String lang = 'ug',
  }) {
    return ApiResult(
      data: null,
      msg: msg,
      status: status,
      time: time.isEmpty ? DateTime.now().toString() : time,
      lang: lang,
    );
  }

  // 从异常创建失败结果
  factory ApiResult.fromException(dynamic exception) {
    if (exception is DioError) {
      // 处理Dio错误
      return _handleDioError(exception);
    } else if (exception is SocketException) {
      // 处理Socket异常
      return ApiResult.error(
        msg: S.current.no_network_please_check,
        status: ApiErrorCode.NETWORK_ERROR,
      );
    } else if (exception is TimeoutException) {
      // 处理超时异常
      return ApiResult.error(
        msg: S.current.connection_timeout,
        status: ApiErrorCode.TIMEOUT_ERROR,
      );
    } else if (exception is FormatException) {
      // 处理格式异常
      return ApiResult.error(
        msg: '${S.current.err_msg}: ${exception.message}',
        status: ApiErrorCode.PARSING_ERROR,
      );
    } else {
      // 处理其他异常
      return ApiResult.error(
        msg: exception.toString(),
      );
    }
  }

  // 处理Dio错误
  static ApiResult<T> _handleDioError<T>(DioError error) {
    // 根据错误类型返回不同的错误信息
    switch (error.type) {
      case DioErrorType.connectTimeout:
      case DioErrorType.sendTimeout:
      case DioErrorType.receiveTimeout:
        return ApiResult.error(
          msg: S.current.connection_timeout,
          status: ApiErrorCode.TIMEOUT_ERROR,
        );
      case DioErrorType.response:
        // 处理HTTP错误
        final statusCode = error.response?.statusCode;
        final responseData = error.response?.data;

        if (responseData is Map<String, dynamic>) {
          // 如果响应数据包含status字段，说明是业务错误
          if (responseData.containsKey('status')) {
            return ApiResult(
              data: null,
              msg: responseData['msg'] ?? S.current.network_err_response,
              status: responseData['status'] ?? statusCode ?? ApiErrorCode.BUSINESS_ERROR,
              time: responseData['time'] ?? '',
              lang: responseData['lang'] ?? 'ug',
            );
          }
        }

        // 否则是HTTP错误
        if (statusCode != null) {
          return ApiResult.error(
            msg: _getErrorMessage(statusCode),
            status: statusCode,
          );
        } else {
          return ApiResult.error(
            msg: S.current.network_err_response,
          );
        }
      case DioErrorType.cancel:
        return ApiResult.error(
          msg: S.current.request_canceled,
        );
      case DioErrorType.other:
        if (error.error is SocketException) {
          return ApiResult.error(
            msg: S.current.no_network_please_check,
            status: ApiErrorCode.NETWORK_ERROR,
          );
        }
        return ApiResult.error(
          msg: S.current.net_work_error,
        );
    }
  }

  // 简化的错误消息获取方法
  static String _getErrorMessage(int statusCode) {
    switch (statusCode) {
      case ApiErrorCode.UNAUTHORIZED:
        return S.current.network_err_not_auth;
      case ApiErrorCode.NOT_FOUND:
        return S.current.network_err_cat_not_find;
      case ApiErrorCode.SERVER_ERROR:
        return S.current.network_err_server_error;
      default:
        return '${S.current.err_msg} ($statusCode)';
    }
  }

  // 获取错误信息
  String getErrorMessage() {
    return msg;
  }

  // 获取状态码
  int getStatusCode() {
    return status;
  }
}
```

### 2. 修改 ApiClient 类

```dart
// 在 ApiClient 类中添加泛型支持，但不使用工厂模式
Future<ApiResult<T>> get<T>(
  String path, {
  Map<String, dynamic>? params,
  required T Function(Map<String, dynamic>) fromJson, // 必须提供fromJson函数
}) async {
  try {
    // 检查网络连接
    if (!await _checkNetworkConnection()) {
      return ApiResult.error(
        msg: S.current.no_network_please_check,
        status: ApiErrorCode.NETWORK_ERROR,
      );
    }

    // 发送请求
    final response = await _dio.get(
      path,
      queryParameters: params,
    );

    // 处理响应
    return _processResponse<T>(response, fromJson);
  } catch (e) {
    // 处理异常
    return ApiResult.fromException(e);
  }
}

// 在 ApiClient 类中添加泛型支持
class ApiClient {
  final Dio _dio;

  ApiClient(this._dio);

  // GET请求
  Future<ApiResult<T>> get<T>(
    String path, {
    Map<String, dynamic>? params,
    required T Function(Map<String, dynamic>) fromJson,
  }) async {
    try {
      // 检查网络连接
      if (!await _checkNetworkConnection()) {
        return ApiResult.error(
          msg: S.current.no_network_please_check,
          status: ApiErrorCode.NETWORK_ERROR,
        );
      }

      // 发送请求
      final response = await _dio.get(
        path,
        queryParameters: params,
      );

      // 处理响应
      return _processResponse<T>(response, fromJson);
    } catch (e) {
      // 处理异常
      return ApiResult.fromException(e);
    }
  }

  // 简化的响应处理方法
  ApiResult<T> _processResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      if (response.statusCode == ApiErrorCode.SUCCESS) {
        if (response.data is Map<String, dynamic>) {
          final Map<String, dynamic> responseData = response.data;
          final status = responseData['status'] ?? ApiErrorCode.SUCCESS;

          // 检查业务状态码
          if (status == ApiErrorCode.SUCCESS) {
            // 如果数据字段存在，使用fromJson函数转换数据
            if (responseData['data'] != null) {
              try {
                final T data = fromJson(responseData['data']);
                return ApiResult.success(
                  data,
                  msg: responseData['msg'] ?? '',
                  time: responseData['time'] ?? '',
                  lang: responseData['lang'] ?? 'ug',
                );
              } catch (e) {
                return ApiResult.error(
                  msg: '解析数据失败: $e',
                  status: ApiErrorCode.PARSING_ERROR,
                );
              }
            } else {
              // 如果数据字段不存在，返回错误
              return ApiResult.error(
                msg: '响应中没有数据字段',
                status: ApiErrorCode.PARSING_ERROR,
              );
            }
          } else {
            // 业务错误
            return ApiResult.error(
              msg: responseData['msg'] ?? S.current.network_err_response,
              status: status,
              time: responseData['time'] ?? '',
              lang: responseData['lang'] ?? 'ug',
            );
          }
        } else {
          // 如果响应不是Map类型，返回错误
          return ApiResult.error(
            msg: '响应格式不正确',
            status: ApiErrorCode.PARSING_ERROR,
          );
        }
      }

      // 处理非200状态码
      return ApiResult.error(
        msg: _getErrorMessage(response.statusCode ?? 0),
        status: response.statusCode ?? 0,
      );
    } catch (e) {
      return ApiResult.error(
        msg: '处理响应失败: $e',
        status: ApiErrorCode.PARSING_ERROR,
      );
    }
  }

  // POST请求
  Future<ApiResult<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    required T Function(Map<String, dynamic>) fromJson,
  }) async {
    try {
      // 检查网络连接
      if (!await _checkNetworkConnection()) {
        return ApiResult.error(
          msg: S.current.no_network_please_check,
          status: ApiErrorCode.NETWORK_ERROR,
        );
      }

      // 发送请求
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );

      // 处理响应
      return _processResponse<T>(response, fromJson);
    } catch (e) {
      // 处理异常
      return ApiResult.fromException(e);
    }
  }

  // 检查网络连接
  Future<bool> _checkNetworkConnection() async {
    try {
      final result = await InternetAddress.lookup('baidu.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
```

### 3. 修改 Repository 层

```dart
class UserRepository {
  final ApiClient _apiClient;

  UserRepository({required ApiClient apiClient}) : _apiClient = apiClient;

  // 简单直接的API调用，提供fromJson函数
  Future<ApiResult<UserModel>> getUserInfo() async {
    return await _apiClient.get(
      '/user/info',
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  // 处理不同格式的API响应示例
  Future<ApiResult<List<ProductModel>>> getProducts() async {
    return await _apiClient.get(
      '/products',
      fromJson: (json) {
        // 处理特殊的数据格式，例如数据在items字段中
        if (json.containsKey('items') && json['items'] is List) {
          final List<dynamic> items = json['items'];
          return items.map((item) => ProductModel.fromJson(item)).toList();
        }
        // 处理标准的数据格式，直接是列表
        if (json is List) {
          return json.map((item) => ProductModel.fromJson(item)).toList();
        }
        // 如果数据格式不符合预期，抛出异常
        throw FormatException('Unexpected data format');
      },
    );
  }

  // POST请求示例
  Future<ApiResult<OrderModel>> createOrder(Map<String, dynamic> orderData) async {
    return await _apiClient.post(
      '/orders',
      data: orderData,
      fromJson: (json) => OrderModel.fromJson(json),
    );
  }
}
```

### 4. 修改 Service 层

```dart
class UserService {
  final UserRepository _repository;

  UserService({required UserRepository repository}) : _repository = repository;

  // 直接传递 Repository 返回的结果
  Future<ApiResult<UserModel>> getUserInfo() async {
    return await _repository.getUserInfo();
  }

  // 如果需要处理特定业务逻辑，可以在这里添加
  // 但不需要重复转换模型，直接返回Repository的结果即可
  Future<ApiResult<UserModel>> getUserWithBusinessLogic() async {
    final result = await _repository.getUserInfo();

    // 这里可以添加一些业务逻辑，如日志记录、缓存等
    // 但不需要重复转换模型

    return result;
  }
}
```

### 5. 修改 Controller/Provider 层

```dart
class UserController extends StateNotifier<UserState> {
  final UserService _userService;

  UserController({required UserService userService})
      : _userService = userService,
        super(UserState.initial());

  Future<void> getUserInfo() async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _userService.getUserInfo();

    if (result.isSuccess && result.data != null) {
      state = state.copyWith(
        isLoading: false,
        user: result.data,
        error: null,
        showRetry: false,
      );
    } else {
      // 根据错误类型提供不同的用户体验
      final bool showRetry = result.status == ApiErrorCode.NETWORK_ERROR ||
                            result.status == ApiErrorCode.TIMEOUT_ERROR;

      state = state.copyWith(
        isLoading: false,
        error: result.msg,
        showRetry: showRetry,
      );
    }
  }
}
```

### 6. 修改拦截器

```dart
class ResponseInterceptor extends Interceptor {
  @override
  void onResponse(
    final Response response,
    final ResponseInterceptorHandler handler,
  ) {
    // 处理未登录状态（401）
    if (response.statusCode == 200 &&
        response.data is Map &&
        response.data['status'] == 401) {
      _handleUnauthorized();
      return;
    }

    // 继续处理响应
    handler.next(response);
  }

  // 处理未登录状态（401）
  void _handleUnauthorized() {
    // 退出登录
    globalContainer.read(authProvider.notifier).logout();

    // 显示提示
    BotToast.showText(text: S.current.network_err_not_auth);

    // 跳转到登录页面
    if (AppContext().currentContext != null) {
      AppContext().currentContext!.push('/login');
    }
  }
}

class ErrorInterceptor extends Interceptor {
  @override
  void onError(final DioError err, final ErrorInterceptorHandler handler) {
    // 显示错误提示
    _showErrorToast(err);

    // 继续传递错误
    handler.next(err);
  }

  // 显示错误提示
  void _showErrorToast(final DioError err) {
    final apiResult = ApiResult.fromException(err);

    // 显示错误提示
    BotToast.showText(
      text: apiResult.msg,
      duration: const Duration(seconds: 2),
    );
  }
}
```

## 重构步骤

1. **定义错误码常量**：创建 ApiErrorCode 类，定义所有错误码常量。
2. **创建新的 ApiResult 类**：实现上述设计的 ApiResult 类，使用错误码常量。
3. **移除 ApiResponse 和 ApiError 类**：删除这两个类，并修改所有引用这些类的代码。
4. **修改 ApiClient 类**：更新 ApiClient 类以使用新的 ApiResult 类和错误码常量。
5. **修改拦截器**：更新拦截器以使用新的 ApiResult 类和错误码常量。
6. **修改 Repository 层**：修改所有 Repository 方法以使用新的 ApiResult 类。
7. **修改 Service 层**：修改 Service 方法以使用新的 ApiResult 类。
8. **修改 Controller/Provider 层**：修改状态管理逻辑以使用新的 ApiResult 类，根据错误类型提供不同的用户体验。
9. **修改 UI 层**：确保 UI 正确显示加载状态、错误和数据，根据错误类型显示不同的UI（如重试按钮）。
10. **添加测试**：为每一层添加单元测试，确保重构不会引入新的问题。
11. **创建错误码文档**：创建一个文档，详细说明所有错误码的含义和使用方式。

## 优势

1. **简化代码**：去掉 when 方法和多余的类，使代码更加简洁。
2. **提高可读性**：直接使用 isSuccess 属性和 data 属性，使代码更加直观。
3. **统一错误处理**：所有 API 调用都返回统一的 ApiResult 格式，错误处理更加一致。
4. **避免混淆**：只有一个类表示 API 响应，避免混淆。
5. **明确的错误码**：使用常量而不是硬编码的数字表示错误码，提高代码的可读性和可维护性。
6. **根据错误类型提供不同的用户体验**：根据错误类型显示不同的UI，提高用户体验。
7. **易于维护**：代码结构更加清晰，易于理解和维护。

## 注意事项

1. 所有 API 调用都必须返回 ApiResult，保持一致性。
2. Service 层如果没有特殊处理，应直接传递 Repository 返回的 ApiResult。
3. 错误处理应该根据错误类型提供不同的用户体验：
   - 网络错误（NETWORK_ERROR）：显示重试按钮，提示用户检查网络连接。
   - 超时错误（TIMEOUT_ERROR）：显示重试按钮，提示用户稍后再试。
   - 解析错误（PARSING_ERROR）：记录日志，显示友好的错误消息，不显示重试按钮。
   - 业务错误（BUSINESS_ERROR）：显示错误消息，不显示重试按钮。
4. 在 UI 层应该提供友好的错误提示，并根据错误类型考虑重试功能。
5. 由于移除了 ApiResponse 和 ApiError 类，需要修改所有引用这些类的代码，这可能是一个较大的工作量。
6. 使用常量而不是硬编码的数字表示错误码，提高代码的可读性和可维护性。
7. 创建一个错误码文档，详细说明所有错误码的含义和使用方式，方便其他开发人员理解和使用。

## 结论

通过这个重构方案，我们可以简化 API 响应处理，统一使用一个类（ApiResult）表示 API 响应，去掉 when 方法，避免代码重复和混淆。这将大大提高代码的可维护性和可读性，使其他开发人员更容易理解和使用。
