# 用户端App开发要求

### 一、核心目标对齐

**功能一致性**

*   明确 **Flutter App 的差异化需求**（如是否需适配平板、是否需要额外原生功能）。
    
*   优先复用小程序的 **后端接口/数据结构**，避免重复设计。
    

**二、代码规范要求**

#### 1. 编码规范

*   **Dart/Flutter 规范**：强制执行官方 [Effective Dart](https://dart.dev/effective-dart) 指南。
    
*   **命名规则**：统一 `snake_case`（文件）、`PascalCase`（类）、`camelCase`（变量/方法）， 类名称以分层架构名称结尾。
    
*   **注释规范**：关键算法、复杂业务逻辑必须添加文档注释（`///`），公共方法需标注参数/返回值。
    
*   **空安全规范：**
    
    *   必须启用空安全（null safety），使用 `late` 需明确初始化路径
        
    *   可空参数必须显式标注 `?` 符号（如 `String? errorMsg`）
        
    *   禁用隐式类型转换，使用 `as` 必须配合 `is` 检查
        
*   **国际化规范：**
    
    *   使用 `intl` 包管理多语言资源，建立 `/l10n` 目录存放 ARB 文件
        
    *   禁止 UI 层直接使用硬编码文字（如 `Text('Cancel')` 需改为 `Text(L10n.of(context).cancel)`)
        
*   **响应式设计规范：**
    
    *   平板适配使用 `LayoutBuilder` + `ScreenType` 判断（如手机/平板布局分离）
        
    *   尺寸单位统一用 `MediaQuery` 动态计算，禁止硬编码像素值，建议使用Flutter类的扩展特性。
        
    *   建立统一管理布局切换点。
        

#### 2. 代码模块化设计

*   **分层架构**：使用Clean Architecture 原则，明确划分`entities`（实体层）、`data`（数据层）、`domain`（业务逻辑）、`presentation`（UI层），每一层级都有明确定义的职责，以实现代码的可维护性、可测试性和可扩展性，**禁止层间逆向依赖**。
    
*   **架构设计模式**：使用MVVM模式，禁止在视图层处理UI交互以外的逻辑操作，配合状态管理器。
    

|  层级  |  职责  |  Riverpod 实现方式  |
| --- | --- | --- |
|  Model  |  数据模型定义- 数据源操作（API/DB）  |  普通 Dart 类 + Repository  |
|  ViewModel  |  业务逻辑处理- 状态管理- 事件响应- 数据转换  |  StateNotifier +StateNotifierProvider  |
|  View  |  UI 渲染- 用户交互监听- 状态订阅  |  ConsumerWidget/HookWidget  |

*   **组件化**：高复用 UI 组件封装为独立 Widget，通过参数化配置（避免硬编码）。
    
*   **状态管理**：统一使用**Riverpod**，禁止全局变量跨层传递。
    
    *   状态管理选型：
        
        *   采用 **Riverpod 2.0**，理由：
            

1.  更适合多模块协作（Provider 无全局作用域管理）
    
2.  强类型安全减少运行时错误（比 BLoC 更简洁）
    
3.  自带依赖注入能力（可替代 get\_it）
    
4.  未来支持元编程（代码生成效率提升）
    

*   **依赖注入**：使用`riverpod` 管理服务类，避免直接 `new` 对象。
    
    *   riverpod依赖注入和状态管理区分概念，避免混用：
        

|  维度  |  依赖注入  |  状态管理  |
| --- | --- | --- |
|  主要目的  |  管理对象创建和依赖关系  |  管理数据变化和跨组件同步  |
|  关注点  |  "谁创建对象"、"对象如何共享"  |  "数据如何变化"、"变化如何传播"  |
|  典型场景  |  服务类（API Client、Repository）注入  |  用户登录状态、主题切换、表单数据  |

*   **路由管理规范:** GoRouter 实现声明式路由, 官网维护并推荐。
    
*   **数据持久化**：Hive 与 SharedPreferences 混合使用或者按业务需求扩展。
    
    *   Hive：
        
        *   需要存储复杂对象（如用户资料、商品信息） 高频读写操作（如聊天记录缓存） 数据量超过 10KB 需要本地加密存储（如敏感信息）
            
    *   SharedPreferences：
        
        *   存储简单的用户偏好（主题模式、语言设置） 需要快速接入的小型配置数据（如首次启动标识） 数据量 < 100KB 且结构扁平
            

#### 3. 代码可维护性

*   **单一职责原则**：每个文件/类仅承担单一职责（如 `user_repository.dart` 仅处理用户数据）。
    
*   **Git 规范**：分支策略（如 Git Flow）、Commit 信息模板（关联 Teambition 任务号）。
    
*   **代码审查**：合并请求（MR）必须经过 **1人以上 Review**，重点检查内存泄漏、性能隐患。
    
    *   配置`analysis_options.yaml`Dart/Flutter项目分析配置，严格约束编码规范。（https://dart.dev/tools/analysis）
        

**三、性能与流畅性要求**

#### 1. 页面性能优化

*   **帧率保障**：
    
    *   核心性能指标
        

|  指标  |  标准值  |  检测方法  |  场景适配要点  |
| --- | --- | --- | --- |
|  帧率（FPS）  |  ≥58 帧（持续 5s 以上  |  DevTools 性能面板  |  华为/小米中端机型需专项测试  |
|  冷启动时间  |  ≤1.5s（Android/iOS  |  Firebase Perf  |   |
|  内存占用  |  ≤300MB（常规页面）  |  Android Profiler/Xcode  |  OPPO/Vivo 低端机专项优化  |
|  包体积增量  |  周版本 ≤5% 增长  |  CI/CD 集成检查  |  国内应用市场分渠道打包要求  |
|  列表滚动丢帧率  |  ≤5%（快速滑动场景）  |  Flutter Driver 自动化测试  |  需考虑高刷新率屏幕适配  |

*   **列表优化**：长列表必须使用 `ListView.builder` + `itemExtent`，结合 `AutomaticKeepAlive` 避免重复渲染。
    
    *   列表渲染规范
        

|  场景  |  技术方案  |  典型应用案例  |
| --- | --- | --- |
|  长列表（>50项）  |  ListView.builder + itemExtent  |  美团外卖商品列表  |
|  复杂项（含图片/视频  |  RepaintBoundary 包裹  |  抖音瀑布流  |
|  吸顶/分组列表  |  Sliver 系列组件  |  支付宝账单列表  |

*   **图片加载**：使用 `cached_network_image` 缓存网络图片，预加载关键资源。
    
*   **内存管理**：禁止在 `build()` 方法内创建大量对象（如循环内 `new Widget`）。
    
    *   **构建优化：**
        
        *   初始化时机：\_cachedChild 在 OptimizedWidget 实例化时创建，而非每次 build
            
        *   缓存机制：StatelessWidget 在参数不变时可能被框架复用
            
        *   GC 压力：避免频繁创建/销毁对象导致垃圾回收压力
            
        *   尽量定义const常量优化
            
    *   **案例：**
        

```dart
// ❌ 禁止写法
Widget build() {
  return Container(
    child: HeavyWidget(), // 每次 build 都会新建实例
  );
}

// ✅ 正确写法
class OptimizedWidget extends StatelessWidget {
  // 类初始化阶段创建，整个生命周期只执行一次
  final Widget _cachedChild = HeavyWidget();
  
  @override
  Widget build() {
    // 复用已有实例
    return Container(child: _cachedChild);
  }
}
```

*   **内存泄漏红线**
    

|  泄漏类型  |  检测工具  |  处置时限  |
| --- | --- | --- |
|  Stream 未关闭  |  flutter\_leakcanary  |  24小时  |
|  全局状态残留  |  Dart DevTools 内存面板  |  48小  |
|  原生资源未释放  |  Xcode Memory Grap  |  紧急处理  |

*   **异步和Isolate使用建议**：
    
    *   异步优先：默认使用 async/await，仅对耗时任务用 Isolate
        
        *   超时控制：Future设置超时限制，防止无限等待
            
        *   取消操作：Dio使用CancelToken，页面销毁时取消进行中的请求
            
    *   轻量通信：Isolate 间传递小数据（<1MB），用 Transferable 优化大数据传输
        
    *   资源管理：确保所有 StreamSubscription 和 Isolate 及时释放
        
    *   错误兜底：全局捕获 + 关键异步链的 try/catch
        
    *   性能监控：集成 DevTools 到 CI/CD 流程
        

#### 2. 启动速度

*   **冷启动时间**：Android/iOS 均需控制在 1.5 秒内（通过移除未使用的插件、延迟初始化非关键服务）。
    
    *   按需初始化案例：
        

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();// 仅核心服务初始化
  runApp(MyApp(plugins: _lazyLoadPlugins())); // 非核心功能延迟加载
}
```

*   **Splash Screen**：使用原生启动图（`flutter_native_splash` 包），避免白屏。
    

#### 3. 包体积控制

*   **APK/IPA 大小**：通过 `--split-debug-info` 移除调试符号，启用代码混淆（ProGuard/R8）。
    
    *   使用 dependency\_overrides 剔除重复库
        
    *   SVG 转字体图标、WebP 格式转换
        
    *   （待考虑）分发包架构（armeabi-v7a + arm64-v8a）
        
*   **按需加载**：动态加载非核心功能模块（如使用 `flutter_dynamic_components`）。
    

### 四、稳定性保障

#### 1. 异常处理

*   **全局捕获**：通过 `FlutterError.onError` 和 `PlatformDispatcher.onError` 拦截未处理异常。
    
*   **错误上报**：集成 Sentry/Firebase Crashlytics，记录堆栈、设备信息、用户操作路径。
    
*   **降级策略**：网络异常时展示友好提示，本地缓存关键数据保证基础功能可用。
    

#### 2. 测试覆盖

*   **单元测试**：核心业务逻辑（如数据处理、状态计算）覆盖率 ≥ 30%。
    
*   **Widget 测试**：验证 UI 组件在不同状态下的渲染正确性。Golden Toolkit视觉回归测试（像素级比对）。
    
*   **集成测试**：使用 `integration_test` 包模拟用户完整操作流程。
    
*   **Monkey Test**：针对复杂页面进行随机操作压力测试。Flutter Driver 滚动列表压测（10,000+ 项）。
    

### 五、交付与协作流程

1.  **CI/CD 流程**
    
    *   自动化构建（GitHub Actions/Codemagic），每日构建开发版，关键分支触发 Lint/测试。