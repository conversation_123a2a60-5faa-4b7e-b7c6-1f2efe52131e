<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-80.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="amap_flutter_location">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_location-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-7.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer_plugin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer_plugin-0.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="battery_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/battery_plus-6.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="battery_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/battery_plus_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="bot_toast">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/bot_toast-4.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.4.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-8.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="card_swiper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/card_swiper-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="chewie">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/chewie-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ci">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ci-0.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cli_util-0.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="custom_lint">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint-0.7.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="custom_lint_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_builder-0.7.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="custom_lint_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_core-0.7.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="custom_lint_visitor">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_visitor-1.0.0+7.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-4.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="event_bus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/event_bus-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_easyloading">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.27/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_riverpod">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_riverpod-2.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_screenutil">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_spinkit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_sticky_header">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_sticky_header-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_svg-2.0.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="fluwx">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluwx-5.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed-3.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed_annotation-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="go_router">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/go_router-14.8.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="hotreloader">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hotreloader-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+22/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.19.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl_utils">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl_utils-2.8.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_serializable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.9.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="posix">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod-2.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod_analyzer_utils">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_analyzer_utils-0.5.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_annotation-2.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_generator-2.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="riverpod_lint">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_lint-2.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shimmer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shimmer-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_helper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="state_notifier">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/state_notifier-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="toggle_switch">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/toggle_switch-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="upower">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/upower-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="value_layout_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/value_layout_builder-0.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics-1.1.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player-2.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_android-2.8.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_avfoundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_platform_interface-6.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_web-2.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-14.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus-1.2.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus_platform_interface-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket-0.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter-4.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_android-4.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_platform_interface-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_wkwebview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.18.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-80.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_location-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-7.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer_plugin-0.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.12.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/battery_plus-6.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/battery_plus_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/bot_toast-4.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.4.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-8.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/card_swiper-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/chewie-1.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ci-0.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cli_util-0.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint-0.7.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_builder-0.7.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_core-0.7.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/custom_lint_visitor-1.0.0+7.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-4.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/event_bus-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-5.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.27/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_riverpod-2.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_sticky_header-0.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_svg-2.0.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluwx-5.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed-3.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed_annotation-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/go_router-14.8.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hotreloader-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+22/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.19.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl_utils-2.8.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.9.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-5.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod-2.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_analyzer_utils-0.5.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_annotation-2.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_generator-2.6.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/riverpod_lint-2.6.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shimmer-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/state_notifier-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/toggle_switch-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/upower-0.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.15/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/value_layout_builder-0.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics-1.1.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player-2.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_android-2.8.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_platform_interface-6.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_web-2.3.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-14.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus-1.2.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus_platform_interface-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket-0.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter-4.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_android-4.3.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_platform_interface-2.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.18.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.12.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
      <root url="file://$USER_HOME$/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_localizations/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_test/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>