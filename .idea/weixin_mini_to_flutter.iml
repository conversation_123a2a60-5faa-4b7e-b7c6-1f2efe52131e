<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/amap_flutter_location/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/battery_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/connectivity_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/fluwx/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/image_picker_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/path_provider_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/permission_handler_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/shared_preferences_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/sqflite_darwin/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/url_launcher_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/video_player_avfoundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/wakelock_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/ios/.symlinks/plugins/webview_flutter_wkwebview/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/battery_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/file_selector_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/image_picker_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/mulazim-user-app/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>