<wxs src="/pages/discount/discountwxs.wxs" module="tools" />
<view class="spec-modal {{show ? 'show' : ''}} {{langId == 1 ?'specRtl':'specLtr'}}">
    <!-- 遮罩层 -->
    <view class="spec-mask-layer" bind:tap="closeModal"></view>
    <!-- 规格信息 -->
    <view class="spec-container">
        <view class="spec-header">
            <view class="spec-title">{{foodItem.name}}</view>
        </view>

        <scroll-view class="spec-content" scroll-y>
            <block wx:for="{{specData}}" wx:key="index">
                <view class="spec-item">
                    <view class="spec-item-title">{{item.name}}</view>
                    <view class="spec-options">
                        <view wx:for="{{item.spec_options}}" wx:for-item="option" wx:for-index="optionIndex"
                            wx:key="optionIndex"
                            class="spec-option {{tools.isSpecSelected(selectedSpecs, item.id, option.id) ? 'active' : ''}} {{option.state === 0 ? 'disabled' : ''}}"
                            data-group-id="{{item.id}}" data-option-id="{{option.id}}"
                            bindtap="{{option.state === 1 ? 'selectOption' : ''}}">
                            <view class="spec-option-name">{{option.name}}</view>
                            <view class="spec-option-divider" wx:if="{{option.price > 0}}">|</view>
                            <view class="price" wx:if="{{option.price > 0}}">
                                <text>{{option.price}}</text>
                                <text class="price-unit">￥</text>
                                <text wx:if="{{index > 0}}">+</text>
                            </view>
                        </view>
                    </view>
                </view>
            </block>

        </scroll-view>
        <!-- 规格选择 -->
        <view class="spec-footer">
            <view class="spec-selected-options">
                <text class="spec-selected-options-text">{{lang.spec_selected}}</text>
                <rich-text nodes="{{selectedOptions}}"></rich-text>
            </view>
            <div class="spec-body">
                <view class="total-price-container">
                    <view class="total-price">
                        <text>{{currentPrice}}</text>
                        <text class="total-price-unit">¥</text>
                    </view>
                    <view class="price-and-max-count">
                        <view class="old-total-price" wx:if="{{originalPrice != totalPrice}}">
                            <text>{{originalPrice}}</text>
                            <text>¥</text>
                        </view>
                        <view class="max-count-info" wx:if="{{hasPreferential || hasSeckill}}">
                            <text>{{hasPreferential ? lang.discount_max_count :
                                lang.seckill_max_order_count}}:</text>
                            <text>{{hasPreferential ? maxOrderCount : seckillMaxOrderCount}}</text>
                        </view>
                    </view>
                </view>
                <view class="confirm-btn" bind:tap="specAdd" wx:if="{{minCount == 1 &&count == 0}}">
                    <text>{{lang.spec_card_title}}</text>
                </view>
                <!-- 显示最小购买数量 -->
                <view class="minCountBtn" wx:if="{{minCount > 1 && count == 0}}" bind:tap="specAdd">
                    <text class="minCountLang">{{lang.minCount}}</text>
                    <text class="min-count-color">{{minCount}}</text>
                    <image class="minCountBg" src="/resources/shipment-reduce/minCount.png" mode="aspectFit" />

                </view>
                <view class="number-control" wx:if="{{count > 0}}">
                    <view class="item-control" catch:tap="specMinus">
                        <image src="/resources/shipment-reduce/minus.png" />
                    </view>
                    <view class="orderText">{{count}}</view>
                    <view class="item-control" catch:tap="specAdd">
                        <image src="/resources/shipment-reduce/add.png" />
                    </view>
                </view>
            </div>
        </view>

        <view class="spec-close" bind:tap="closeModal">
            <image class="spec-img" src="/resources/img/close.png" />
        </view>
    </view>
</view>