// 优化后的规格选择组件
const app = getApp();
Component({
  properties: {
    foodItem: Object, // 商品信息
    specData: Array, // 规格数据
    lang: Object, // 语言配置
    langId: Number, // 语言ID
  },
  data: {
    selectedSpecs: [], // 改为数组存储规格选择
    originalPrice: 0, // 原价
    totalPrice: 0, // 总价
    show: false, // 是否显示
    count: 0, // 数量
    seckillInfo: null, // 秒杀信息
    prefInfo: null, // 优惠信息
    marketInfo: null, // 营销信息
    maxOrderCount: 0, // 最大订单数量
    seckillMaxOrderCount: 0, // 秒杀最大订单数量
    hasPreferential: false, // 是否有优惠
    hasSeckill: false, // 是否有秒杀
    minCount: 1, // 最小购买数量
    currentPrice: 0, // 当前价格
  },
  // 组件生命周期
  attached() {
    this.setData({ show: true }, () => {
      this.initSpecs();
    });
  },
  // 监听规格数据变化
  observers: {
    specData(specData) {
      if (Array.isArray(specData) && specData.length) {
        this.initSpecs();
      }
    },
    // 监听foodItem变化，更新界面显示
    foodItem(item) {
      if (item) {
        this.setData({
          minCount: item.min_count || 1, // 设置最小购买数量
        });
      }
    },
  },
  methods: {
    // 初始化规格
    initSpecs() {
      const { specData, foodItem } = this.data;
      if (!specData?.length) return;

      // 查找匹配的购物车项目
      const cartItem = this._findMatchedCartItem(
        foodItem.id,
        foodItem.spec_unique_id
      );
      const selectedSpecs = [];
      const selectedOptions = [];
      let additionalPrice = 0;

      // 遍历规格数据，初始化选中的规格
      specData.forEach((group) => {
        if (!group.spec_options?.length) return;
        const matched = cartItem?.spec_selected_options?.find(
          (spec) => spec.spec_type_id == group.id
        );
        let selectedOption = matched
          ? group.spec_options.find(
              (opt) => opt.id == matched.spec_option_id && opt.state == 1
            )
          : group.spec_options.find(
              (opt) => opt.is_selected == 1 && opt.state == 1
            ) || group.spec_options.find((opt) => opt.state == 1);

        // 如果选中了规格选项
        if (selectedOption) {
          selectedSpecs.push({
            groupId: group.id,
            optionId: selectedOption.id,
          });
          additionalPrice += selectedOption.price || 0;
          selectedOptions.push({
            name: selectedOption.name,
            price: selectedOption.price,
            spec_type_id: group.id,
            spec_option_id: selectedOption.id,
          });
        }
      });
      // 设置数量，考虑最小购买数量
      let count = cartItem?.count || 0;

      // 计算促销价格
      const { promoInfo } = this._calculatePromotionPrice(selectedSpecs);
      // 计算最终价格
      const { finalPrice, promotionCount, currentPrice } =
        this._calculateFinalPrice(count, additionalPrice, promoInfo);
      // 创建要更新的数据对象
      const updateData = {
        selectedOptions: this._formatSelectedOptions(selectedOptions),
        selectedSpecs,
        originalPrice: this._formatPrice(additionalPrice),
        totalPrice: this._formatPrice(finalPrice),
        currentPrice: this._formatPrice(currentPrice),
        count,
      };

      this._buildSpecFoodItem(
        selectedSpecs,
        selectedOptions,
        count,
        additionalPrice,
        promoInfo,
        promotionCount
      );
      // 只有当值不为undefined时才添加到更新数据中
      if (promoInfo.seckillInfo !== undefined) {
        updateData.seckillInfo = promoInfo.seckillInfo;
      }
      if (promoInfo.prefInfo !== undefined) {
        updateData.prefInfo = promoInfo.prefInfo;
      }
      if (promoInfo.marketInfo !== undefined) {
        updateData.marketInfo = promoInfo.marketInfo;
      }
      this.setData(updateData);
    },

    // 选择规格选项
    selectOption(e) {
      const { groupId, optionId } = e.currentTarget.dataset;
      const { specData } = this.data;
      const group = specData.find((g) => g.id == groupId);
      const option = group?.spec_options.find((opt) => opt.id == optionId);
      if (!option || option.state !== 1) {
        return;
      }

      // 更新选中的规格
      const selectedSpecs = [...this.data.selectedSpecs];
      const existingIndex = selectedSpecs.findIndex(
        (spec) => spec.groupId == groupId
      );
      if (existingIndex != -1) {
        selectedSpecs[existingIndex].optionId = optionId;
      } else {
        selectedSpecs.push({ groupId, optionId });
      }

      // 计算价格和选项
      const { selectedOptions, price } =
        this._calculatePriceAndOptions(selectedSpecs);
      // 查找匹配的购物车项目
      const cartItem = this._findMatchedCartItemBySpecs(selectedSpecs);
      const count = cartItem?.count || 0;
      // 计算促销价格
      const { promoInfo } = this._calculatePromotionPrice(selectedSpecs);
      // 计算最终价格
      const { finalPrice, promotionCount, currentPrice } =
        this._calculateFinalPrice(count, price, promoInfo);
      // 创建要更新的数据对象
      const updateData = {
        selectedSpecs,
        selectedOptions: this._formatSelectedOptions(selectedOptions),
        originalPrice: this._formatPrice(price),
        totalPrice: this._formatPrice(finalPrice),
        currentPrice: this._formatPrice(currentPrice),
        count,
      };
      this._buildSpecFoodItem(
        selectedSpecs,
        selectedOptions,
        count,
        price,
        promoInfo,
        promotionCount
      );
      // 只有当值不为undefined时才添加到更新数据中
      if (promoInfo.seckillInfo !== undefined) {
        updateData.seckillInfo = promoInfo.seckillInfo;
      }
      if (promoInfo.prefInfo !== undefined) {
        updateData.prefInfo = promoInfo.prefInfo;
      }
      if (promoInfo.marketInfo !== undefined) {
        updateData.marketInfo = promoInfo.marketInfo;
      }

      this.setData(updateData);
    },

    // 增加数量
    specAdd() {
      const { count, minCount } = this.data;
      // 如果当前数量为0，则直接将数量设置为最小购买数量
      if (count == 0) {
        this._updateCount(minCount, "specAdd");
      } else {
        const newCount = count + 1;
        this._updateCount(newCount, "specAdd");
      }
    },

    // 减少数量
    specMinus() {
      if (this.data.count <= 0) return;
      // 获取最小购买数量
      const { minCount } = this.data;
      // 如果当前数量等于最小购买数量，则直接置为0
      if (this.data.count <= minCount) {
        this._updateCount(0, "specMinus");
      } else {
        const newCount = this.data.count - 1;
        this._updateCount(newCount, "specMinus");
      }
    },

    // 更新数量
    _updateCount(newCount, eventName) {
      const { selectedSpecs, minCount } = this.data;
      // 计算价格和选项
      const { selectedOptions, price } =
        this._calculatePriceAndOptions(selectedSpecs);
      // 计算促销价格
      const { promoInfo } = this._calculatePromotionPrice(selectedSpecs);
      // 计算最终价格
      const { finalPrice, promotionCount, currentPrice } =
        this._calculateFinalPrice(newCount, price, promoInfo);
      // 构建规格食品项目
      const specFoodItem = this._buildSpecFoodItem(
        selectedSpecs,
        selectedOptions,
        newCount,
        price,
        promoInfo,
        promotionCount
      );
      // 最小购买数量处理
      // 确保当前数量不低于最小购买数量，但在减少数量时允许直接归零
      if (eventName === "specAdd" && newCount > 0 && newCount < minCount) {
        specFoodItem.count = minCount;
        newCount = minCount;
      }
      // 确保min_count属性始终存在
      specFoodItem.min_count = minCount;
      // 创建要更新的数据对象
      const updateData = {
        count: newCount,
        originalPrice: this._formatPrice(price),
        totalPrice: this._formatPrice(finalPrice),
        currentPrice: this._formatPrice(currentPrice),
      };

      // 只有当值不为undefined时才添加到更新数据中
      if (promoInfo.seckillInfo !== undefined) {
        updateData.seckillInfo = promoInfo.seckillInfo;
      }
      if (promoInfo.prefInfo !== undefined) {
        updateData.prefInfo = promoInfo.prefInfo;
      }
      if (promoInfo.marketInfo !== undefined) {
        updateData.marketInfo = promoInfo.marketInfo;
      }
      this.setData(updateData);

      this.triggerEvent(eventName, { specFoodItem });
    },

    // 计算价格和选项
    _calculatePriceAndOptions(selectedSpecs) {
      const { specData } = this.data;
      let price = 0;
      const selectedOptions = [];

      selectedSpecs.forEach((spec) => {
        const group = specData.find((g) => g.id === spec.groupId);
        const option = group?.spec_options.find(
          (opt) => opt.id === spec.optionId
        );
        if (option && option.state == 1) {
          price += option.price || 0;
          selectedOptions.push({
            name: option.name,
            price: option.price,
            spec_type_id: spec.groupId,
            spec_option_id: spec.optionId,
          });
        }
      });

      return { selectedOptions, price };
    },

    // 计算促销价格
    _calculatePromotionPrice(selectedSpecs) {
      const { foodItem } = this.data;
      const selectedOptionIds = selectedSpecs.map((spec) => spec.optionId);
      // 改为使用null而不是undefined
      let promoInfo = { seckillInfo: null, prefInfo: null, marketInfo: null };
      // 检查秒杀活动
      if (Array.isArray(foodItem.seckill)) {
        promoInfo.seckillInfo =
          foodItem.seckill.find(
            (item) =>
              item.seckill_active == 1 &&
              this._isOptionIdsMatch(selectedOptionIds, item.option_ids)
          ) || null; // 确保找不到时是null而不是undefined
      }
      // 检查优惠活动
      if (Array.isArray(foodItem.pref)) {
        promoInfo.prefInfo =
          foodItem.pref.find((item) =>
            this._isOptionIdsMatch(selectedOptionIds, item.option_ids)
          ) || null; // 确保找不到时是null而不是undefined
      }
      // 检查营销活动
      if (Array.isArray(foodItem.market)) {
        promoInfo.marketInfo =
          foodItem.market.find((item) =>
            this._isOptionIdsMatch(selectedOptionIds, item.option_ids)
          ) || null; // 确保找不到时是null而不是undefined
      }
      return { promoInfo };
    },

    // 计算最终价格
    _calculateFinalPrice(count, basePrice, promoInfo) {
      let promotionCount = 0;
      let finalPrice = basePrice;
      let currentPrice = basePrice;

      // 处理秒杀价格
      if (promoInfo.seckillInfo) {
        // 如果只有秒杀的话别的活动没有的话显示秒杀价格
        if (!promoInfo.prefInfo) {
          finalPrice = promoInfo.seckillInfo.price;
          // 如果超出秒杀数量，显示原价
          if (count > promoInfo.seckillInfo.user_max_order_count) {
            currentPrice = basePrice;
          } else {
            currentPrice = promoInfo.seckillInfo.price;
          }
        } else {
          const max = promoInfo.seckillInfo.user_max_order_count || 0;
          if (max === 0 || count <= max) {
            finalPrice = promoInfo.seckillInfo.price;
            currentPrice = promoInfo.seckillInfo.price;
          } else if (promoInfo.prefInfo) {
            // 超出秒杀数量，使用优惠价格
            const prefMax = promoInfo.prefInfo.max_order_count || 0;
            promotionCount = prefMax
              ? Math.min(count - max, prefMax)
              : count - max;
            // 如果优惠数量大于0，则显示优惠价格
            if (promotionCount > 0) {
              finalPrice = promoInfo.prefInfo.discount_price;
              currentPrice = promoInfo.prefInfo.discount_price;
            }
            // 如果超出优惠数量，显示原价
            if (count > max + prefMax) {
              currentPrice = basePrice;
            }
          }
        }
      } else if (promoInfo.prefInfo) {
        // 处理优惠价格
        const prefMax = promoInfo.prefInfo.max_order_count || 0;
        // 计算优惠数量：如果超过最大优惠数量，则只计算最大优惠数量
        promotionCount = Math.min(count, prefMax);

        finalPrice = promoInfo.prefInfo.discount_price;
        // 如果优惠数量大于等于优惠最大数量，则显示原价
        if (count > prefMax) {
          currentPrice = basePrice;
        } else {
          currentPrice = promoInfo.prefInfo.discount_price;
        }
      }
      return { finalPrice, promotionCount, currentPrice };
    },

    // 构建规格食品项目
    _buildSpecFoodItem(
      selectedSpecs,
      selectedOptions,
      count,
      originalPrice,
      promoInfo,
      promotionCount = 0
    ) {
      const { foodItem, minCount } = this.data;
      // 生成唯一ID
      const specUniqueId = `${foodItem.id}_${selectedSpecs
        .map((spec) => spec.optionId)
        .join("_")}`;
      // 基础项目信息
      const baseItem = {
        ...foodItem,
        spec_selected_options: selectedOptions.map((opt) => ({
          spec_type_id: opt.spec_type_id,
          spec_option_id: opt.spec_option_id,
          name: opt.name,
          price: opt.price,
        })),
        count,
        price: this._formatPrice(originalPrice),
        old_price: this._formatPrice(originalPrice),
        spec_unique_id: specUniqueId,
        min_count: minCount || foodItem.min_count || 1, // 确保min_count始终存在
      };
      // 添加秒杀信息
      if (promoInfo.seckillInfo) {
        baseItem.seckill_id = promoInfo.seckillInfo.seckill_id;
        baseItem.seckill_active = promoInfo.seckillInfo.seckill_active;
        baseItem.seckill_price = promoInfo.seckillInfo.price;
        baseItem.seckill_max_order_count =
          promoInfo.seckillInfo.user_max_order_count;
        baseItem.price = promoInfo.seckillInfo.price;
        // 更新组件状态
        this.setData({
          seckillMaxOrderCount: promoInfo.seckillInfo.user_max_order_count || 0,
          hasSeckill: true,
        });
      } else {
        delete baseItem.seckill_id;
        delete baseItem.seckill_active;
        delete baseItem.seckill_price;
        baseItem.seckill_max_order_count = 0;

        // 更新组件状态
        this.setData({
          seckillMaxOrderCount: 0,
          hasSeckill: false,
        });
      }
      // 添加优惠信息
      if (promoInfo.prefInfo) {
        baseItem.max_order_count = promoInfo.prefInfo.max_order_count;
        baseItem.prefrential_id = promoInfo.prefInfo.preferential_id;
        baseItem.prefrential_price = promoInfo.prefInfo.discount_price;
        baseItem.prefrential_count = promotionCount;
        // 更新组件状态
        const hasSeckill = !!promoInfo.seckillInfo;
        const hasPreferential =
          !!promoInfo.prefInfo.discount_price &&
          (!hasSeckill || promotionCount > 0);
        // 没有秒杀信息，但有优惠价格
        if (!hasSeckill) {
          baseItem.price = promoInfo.prefInfo.discount_price;
        }
        this.setData({
          maxOrderCount: promoInfo.prefInfo.max_order_count || 0,
          // 在以下情况显示优惠:
          // 1. 没有秒杀信息，但有优惠价格
          // 2. 有秒杀信息，但promotionCount > 0（表示数量超过了秒杀限制）
          hasPreferential,
        });
      } else {
        delete baseItem.prefrential_id;
        delete baseItem.prefrential_price;
        baseItem.prefrential_count = 0;
        baseItem.max_order_count = 0;

        // 更新组件状态
        this.setData({
          maxOrderCount: 0,
          hasPreferential: false,
        });
      }
      // 处理营销信息
      if (!promoInfo.marketInfo) {
        delete baseItem.reduction_foods_tags;
      }
      return baseItem;
    },

    // 查找匹配的购物车项目
    _findMatchedCartItem(foodId, uniqueId) {
      return app.globalData.appCartList?.list?.find(
        (item) => item.id == foodId && item.spec_unique_id == uniqueId
      );
    },

    // 通过规格查找匹配的购物车项目
    _findMatchedCartItemBySpecs(selectedSpecs) {
      const { foodItem } = this.data;
      return app.globalData.appCartList?.list?.find((item) => {
        if (item.id !== foodItem.id || !item.spec_selected_options)
          return false;
        const matchMap = new Map(
          item.spec_selected_options.map((opt) => [
            `${opt.spec_type_id}_${opt.spec_option_id}`,
            true,
          ])
        );
        return selectedSpecs.every((spec) =>
          matchMap.has(`${spec.groupId}_${spec.optionId}`)
        );
      });
    },

    // 检查选项ID是否匹配
    _isOptionIdsMatch(selectedOptionIds, targetOptionIds) {
      return (
        Array.isArray(targetOptionIds) &&
        Array.isArray(selectedOptionIds) &&
        targetOptionIds.every((id) => selectedOptionIds.includes(Number(id)))
      );
    },

    // 格式化选中的选项
    _formatSelectedOptions(options) {
      return options
        .map((opt) => opt.name)
        .join('<span style="color:#D4D7DC;padding:0 4px">|</span>');
    },

    // 格式化价格
    _formatPrice(val) {
      return Math.round((val || 0) * 100) / 100;
    },

    // 关闭模态框
    closeModal() {
      this.triggerEvent("close");
    },
  },
});
