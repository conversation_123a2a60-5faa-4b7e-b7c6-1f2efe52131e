/* components/specModal/spec.wxss */
.spec-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  opacity: 0.3;
  transition: all 0.3s;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  background: rgb(0 0 0 / 48%);
  backdrop-filter: blur(4px);
}
.spec-mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.specRtl {
  direction: rtl;
}

.spec-modal.show {
  opacity: 1;
}

.spec-container {
  width: calc(100% - 90rpx);
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.2);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  overflow: visible;
}
.spec-container view {
  overflow: visible;
}

.spec-modal.show .spec-container {
  transform: scale(1);
}

.spec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #eff1f6;
}

.spec-title {
  font-size: 34rpx;
  color: #333;
}

.spec-content {
  padding: 0 24rpx;
  max-height: 50vh;
  min-height: 30vh;
  box-sizing: border-box;
}

.spec-item {
  padding: 20rpx 0;
}

.spec-item-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-option {
  padding: 12rpx 15rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  border: 1px solid #f5f5f5;
  color: #8e8e8e;
}

.spec-option.active {
  background-color: #deffe2;
  color: var(--theme-main-color);
  border: 1px solid var(--theme-main-color);
}

.spec-option.disabled {
  background-color: #ededed;
  color: #999;
  cursor: not-allowed;
}

.price {
  color: var(--theme-main-color);
  display: flex;
  align-items: center;
}

.spec-footer {
  padding: 16rpx 24rpx 24rpx;
  border-top: 1rpx solid #eff1f6;
}
.spec-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.total-price-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16rpx;
}

.total-price {
  display: flex;
  align-items: center;
  font-size: 62rpx;
  font-family: "YouSheBiaoTiHei";
  color: var(--theme-main-color);
  line-height: 0.6;
}

.old-total-price {
  font-size: 30rpx;
  color: #8d8c8c;
  text-decoration: line-through;
  display: flex;
  align-items: center;
}

.confirm-btn {
  background-color: var(--theme-main-color);
  color: #fff;
  font-size: 30rpx;
  padding: 0px 44rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  line-height: 1;
}
.specLtr .confirm-btn {
  padding: 0px 18rpx;
}

.spec-option-divider {
  color: #e7e7e7;
  padding: 0 8rpx;
}

.price-unit {
  font-size: 20rpx;
}

.number-control {
  display: flex;
  align-items: center;
}
.item-control {
  width: 54rpx;
  height: 54rpx;
}
.item-control image {
  width: 100%;
  height: 100%;
}
.orderText {
  font-size: 36rpx;
  color: #ff4348;
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.spec-selected-options {
  display: flex;
  align-items: center;
  color: #000;
  font-size: 28rpx;
  padding-bottom: 22rpx;
  margin-top: 10rpx;
}
.spec-selected-options-text {
  color: #8d8c8c;
}
.specRtl .spec-selected-options-text {
  padding-left: 8rpx;
}
.specLtr .spec-selected-options-text {
  padding-right: 8rpx;
}
.spec-close {
  width: 68rpx;
  height: 68rpx;
  z-index: 3;
  position: absolute;
  left: 50%;
  bottom: -90rpx;
  transform: translate(-50%, 0%);
}
.spec-img {
  width: 100%;
  height: 100%;
}

/* 最大数量信息样式 */
.max-count-info {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #8d8c8c;
  gap: 5rpx;
}

.minCountBtn {
  background-color: var(--theme-main-color);
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
  border-radius: 50rpx;
  padding: 0 12rpx;
  gap: 8rpx;
}
.specLtr .minCountBtn {
  direction: rtl;
}
.min-count-color {
  font-weight: 600;
}
.minCountBg {
  width: 36rpx;
  height: 36rpx;
}
.total-price-unit {
  font-size: 36rpx;
}
.price-and-max-count {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
