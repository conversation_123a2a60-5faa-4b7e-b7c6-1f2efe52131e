Component({
  properties: {
    price: {
      type: String,
      value: '99.99',
    },
    fontSize: {
      type: String,
      value: '32rpx', 
    },
    smallFontOffset: {
      type: Number,
      value: 6, // 小字体与主字体差距 rpx
    },
    fontWeight: {
      type: String,
      value: 'normal'
    },
    color: {
      type: String,
      value: '#333'
    }
  },
  data: {
    preChars: [],
    postChars: [],
    smallFontSize: '26rpx',
  },
  observers: {
    'price, fontSize, smallFontOffset': function () {
      this.splitPrice();
    }
  },
  methods: {
    splitPrice() {
      const { price, fontSize, smallFontOffset } = this.data;
      const dotIndex = price.indexOf('.');
      let pre = '', post = '';

      if (dotIndex !== -1) {
        pre = price.slice(0, dotIndex + 1);   // 包含点
        post = price.slice(dotIndex + 1);
      } else {
        pre = price;
      }

      // 计算小字体（仅支持 rpx 为单位）
      const match = fontSize.match(/^(\d+)(rpx|rrpx)$/);
      let smallFontSize = fontSize;
      if (match && match[2] === 'rpx') {
        const base = parseInt(match[1]);
        smallFontSize = (base - smallFontOffset) + 'rpx';
      }

      this.setData({
        preChars: pre.split(''),
        postChars: post.split(''),
        smallFontSize,
      });
    }
  },
});
