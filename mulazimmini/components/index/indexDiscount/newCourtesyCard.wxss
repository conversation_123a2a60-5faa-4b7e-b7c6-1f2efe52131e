.newCoupon {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
}
.newCoupon view {
  overflow: visible;
}
.discount-tip-block {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  background: rgb(0 0 0 / 48%);
  backdrop-filter: blur(4px);
}

.tip-box {
  position: relative;
  width: 700rpx;
  height: 850rpx;
  z-index: 8;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  animation: scaleAndRotate 1.2s ease-out;
}

@keyframes scaleAndRotate {
  0% {
    transform: translate(-50%, -50%) scale(0.5) rotate(-10deg);
    opacity: 0;
  }
  40% {
    transform: translate(-50%, -50%) scale(1.1) rotate(5deg);
    opacity: 0.8;
  }
  70% {
    transform: translate(-50%, -50%) scale(0.95) rotate(-2deg);
    opacity: 0.9;
  }
  100% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    opacity: 1;
  }
}

.coupon-price {
  position: absolute;
  z-index: 11;
  bottom: 245rpx;
  left: 0;
  right: 0;
  font-size: 150rpx;
  color: #ffdba8;
  line-height: 0.3;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.img-close {
  width: 64rpx;
  height: 64rpx;
  position: absolute;
  left: 50%;
  bottom: -120rpx;
  transform: translate(-50%);
  z-index: 11;
}
.content-img {
  width: 100%;
  height: 100%;
}

.tag-box {
  position: fixed; /* 固定定位，元素相对于视口定位 */
  z-index: 2; /* 图层层级，确保在其他元素上方显示 */
  top: 50%;
  left: 50%; /* 元素顶部和左侧与视口中心对齐 */
  width: 100%;
  height: 100%; /* 元素尺寸占满整个视口 */
  transform: translate(-50%, -50%); /* 向上向左移动自身一半尺寸，实现真正居中 */
  /* 原有样式... */
  animation-name: fadeIn; /* 添加动画名称 */
  animation-fill-mode: both; /* 动画结束后保持最终状态 */
  opacity: 0;
  animation-delay: 0.8s;
  animation-duration: 0.8s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.1);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.tag-image {
  width: 180%;
  height: 180%;
  margin-top: -70%;
  margin-left: -40%;
  animation: rotate 12s linear infinite;
  z-index: 1;
}
