// components/index/indexDiscount/courtesyCard.js
var app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    couponData: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    langId: 1,
  },
  attached: function (options) {
    //获取语言
    this.setData({
      langId: app.globalData.langId,
    });
  },
  /**
   * 组件的方法列表
   */
  methods: {
    confirm() {
      let ids = this.data.couponData.couponList
        .map((item) => item.id)
        .join(",");
      this.triggerEvent("confirm", ids);
    },
    closeThis(e) {
      if (e.target.id != e.currentTarget.id) {
        return false;
      }
      this.triggerEvent("cancel");
    },
  },
});
