const app = getApp();
const { _formatPrice } = require("../../utils/util");
Page({
  /**
   * 页面的初始数据
   */
  data: {
    langId: 1, // 语言id
    lang: {}, // 语言
    comboList: [], // 套餐列表
    limit: 10, // 每页条数
    page: 1, // 当前页
    hasMore: true, // 是否有更多数据
    loading: false, // 是否显示loading
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getFoodsInfo();
    try {
      this.selectComponent("#loading").showLoading("center");
    } catch (error) {}
    this.setData({
      lang: app.getWord(),
      langId: app.globalData.langId,
    });
  },
  //获取餐厅美食
  getFoodsInfo() {
    const that = this;
    let buildingId =
      app.globalData.isArea?.id || wx.getStorageSync("isArea")?.id;
    let { limit, page } = that.data;
    let params = {
      building_id: buildingId || 1,
      limit,
      page,
    };
    let url = `/smart/v2/restaurant/combo-list`;
    app.GET({
      url: url,
      params,
      success: function (res) {
        if (res.data.status != 200) {
          that.selectComponent("#toast").showToast(res.data?.msg || "");
          return;
        }
        let newComboList = [];
        if (Array.isArray(res.data?.data)) {
          newComboList = res.data.data.map((item) => {
            return {
              ...item,
              price: _formatPrice(item.price),
              origin_price: _formatPrice(item.origin_price),
            };
          });
        }
        // 判断是否还有更多数据
        const hasMore = newComboList.length == limit;
        // 如果是加载更多，则将新数据追加到原有数据后面
        that.setData({
          comboList: [...that.data.comboList, ...newComboList],
          hasMore: hasMore,
        });
      },
      fail: function (res) {
        console.log(res);
      },
      complete: function () {
        that.setData({ loading: true });
        try {
          that.selectComponent("#loading").hideLoading();
        } catch (error) {}
      },
    });
  },
  // 上拉加载
  onReachBottom: function () {
    // 如果还有更多数据，则加载下一页
    if (this.data.hasMore) {
      // 页码增加后，调用获取数据方法，并标记为加载更多
      this.data.page++;
      this.getFoodsInfo();
    }
  },
  // 点击套餐item
  handleComboItemTap(e) {
    const { item } = e.currentTarget.dataset;
    app.globalData.appMerchantId = item.restaurant_id;
    app.globalData.comeAgainList = [{ id: item.id, count: 0 }];
    /* 跳转到takeFoodList */
    wx.navigateTo({
      url: "/pages/takeFoodList/takeFoodList",
    });
  },
  onShareAppMessage: function (e) {},
});
