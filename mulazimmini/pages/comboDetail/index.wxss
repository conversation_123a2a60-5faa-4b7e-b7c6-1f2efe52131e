page {
  background-color: #eff1f5;
  font-family: "UKIJEkran";
}
.combo-list view {
  overflow: visible;
}
.combo-list {
  padding: 20rpx 20rpx calc(var(--iphonex-fix-bottom) + 20rpx) 20rpx;
}
.combo-rtl {
  direction: rtl;
}
.combo-ltr {
  direction: ltr;
}
.combo-item {
  display: flex;
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 25rpx;
  justify-content: space-between;
  align-items: center;
  gap: 15rpx;
}

.combo-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title-and-price {
  display: flex;
  justify-content: space-between;
}
.price-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
}
.current-price {
  color: #ff4348;
  font-size: 36rpx;
  font-weight: bold;
}

.original-price {
  color: #999;
  font-size: 32rpx;
  text-decoration: line-through;
}

.title {
  overflow: hidden !important;
  white-space: normal;
  text-overflow: ellipsis;
  font-size: 32rpx;
  color: #000;
  padding: 4rpx 0;
  max-width: 270rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.description-and-calories-and-order-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}
.description-and-calories {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10rpx;
}
.description {
  color: #666;
  font-size: 28rpx;
  overflow: hidden !important;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 4rpx 0;
  max-width: 290rpx;
}

.calories {
  color: #999;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.order-btn {
  background-color: #07c160;
  color: white;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 40rpx;
  width: fit-content;
  margin: 0;
}
.combo-image {
  width: 160rpx;
  height: 160rpx;
  position: relative;
}

.combo-image image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.image-tag {
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: 0px 4px 11px 0px rgba(0, 255, 9, 0.16);
  background: linear-gradient(150.5deg, #9de797 12.776%, #00c040d9 61.203%);
  color: white;

  padding: 6rpx 22rpx;
}
.combo-rtl .image-tag {
  right: unset;
  left: 0;
  border-radius: 0px 20rpx 0px 20rpx;
  font-size: 20rpx;
}
.combo-ltr .image-tag {
  right: 0;
  left: unset;
  border-radius: 20rpx 0px 20rpx 0px;
  font-size: 22rpx;
}
.combo-not-message {
  margin-top: 22vh;
}