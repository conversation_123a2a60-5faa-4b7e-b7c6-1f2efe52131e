<!-- pages/comboDetail/index.wxml -->
<view class="combo-list {{langId == 1 ? 'combo-rtl' : 'combo-ltr'}}">
    <view class="combo-item" wx:for="{{comboList}}" wx:key="index" bind:tap="handleComboItemTap" data-item="{{item}}">
        <view class="combo-image">
            <image src="{{item.image}}" mode="aspectFill"></image>
            <view class="image-tag" wx:if="{{item.food_type == 2}}">{{lang.comboTitle}}</view>
        </view>
        <view class="combo-info">
            <view class="title-and-price">
                <view class="title">{{item.name}}</view>
                <view class="price-container">
                    <div class="original-price" wx:if="{{item.origin_price != item.price}}">
                        ¥{{item.origin_price}}
                    </div>
                    <div class="current-price">¥{{item.price}}</div>
                </view>
            </view>
            <view class="description-and-calories-and-order-btn">
                <view class="description-and-calories">
                    <view class="description">{{item.restaurant_name}}</view>
                    <view class="calories">
                        <text>{{lang.sales_count}}</text>
                        <text>:</text>
                        <text>{{item.month_order_count}}</text>
                    </view>
                </view>
                <view class="order-btn">{{lang.special_btn}}</view>
            </view>
        </view>
    </view>
    <view class="combo-not-message" wx:if="{{comboList.length==0 && loading}}">
        <not-message>
            <view>{{ lang.selfpickupordercontainer }}</view>
        </not-message>
    </view>
</view>
<loading id="loading"></loading>
<toast id="toast"></toast>