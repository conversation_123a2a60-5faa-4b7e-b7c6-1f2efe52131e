<!--pages/licensePage/licensePage.wxml-->
<view class="container-license">
  <!-- 验证码界面 -->
  <view class="verify-section" wx:if="{{!isVerified}}">
    <!-- 输入框和验证码同一行 -->
    <view class="input-captcha-row">
      <view class="captcha-wrapper">
        <view class="captcha-display" bindtap="refreshCaptcha">
          <image 
            class="captcha-image" 
            src="data:image/png;base64,{{captchaImage}}" 
            mode="aspectFill"
            wx:if="{{captchaImage}}"
          />
          <view class="loading-captcha" wx:else>
            <text>{{lang.loading}}</text>
          </view>
        </view>
      </view>
      <view class="input-wrapper">
        <input 
          class="code-input" 
          type="text" 
          maxlength="10" 
          value="{{inputCode}}"
          bindinput="onCodeInput"
          focus="{{true}}"
        />
      </view>
    </view>

    <!-- 验证按钮单独一行 -->
    <view class="btn-container">
      <button class="verify-btn" bindtap="onVerify">
        {{lang.confirm}}
      </button>
    </view>
  </view>

  <!-- 营业执照展示界面 -->
  <view class="license-section" wx:if="{{isVerified}}">    
    <scroll-view scroll-y="true" class="license-container">
      <view class="license-list" wx:if="{{licenseData.length > 0}}">
        <block wx:for="{{licenseData}}" wx:for-index="idx" wx:for-item="item" wx:key="idx">
          <view class="license-item" bindtap="previewImage" data-current="{{item.big_image}}" data-urls="{{allUrls}}">
            <image 
              class="license-image" 
              src="{{item.image}}" 
              mode="aspectFit"
              lazy-load="true"
            />
          </view>
        </block>
      </view>
      
      <view class="empty-state" wx:else>
        <text class="empty-text">{{lang.no_qualification}}</text>
      </view>
    </scroll-view>
  </view>
</view> 

<toast id="toast"></toast>
<loading id="loading"></loading>