/* pages/licensePage/licensePage.wxss */
page {
  font-family: "UKIJEkran";
}
.container-license {
  background-color: #fff;
  min-height: 100vh;
}

/* 验证码部分样式 */
.verify-section {
  padding: 400rpx 30rpx 40rpx;
}

.page-title {
  font-size: 44rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.page-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.header {
  margin-bottom: 40rpx;
}

.title-box {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.title-box text:first-child {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.title-box .tips {
  font-size: 24rpx;
  color: #999;
  display: block;
  line-height: 1.4;
}

.subtitle {
  font-size: 26rpx;
  color: #999;
  display: block;
  line-height: 1.4;
  padding: 0 30rpx;
}

/* 输入框和验证码同一行布局 */
.input-captcha-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.input-wrapper {
  flex: 1.8;
}

.captcha-wrapper {
  flex: 1.2;
}

.code-input {
  width: 100%;
  height: 100rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  padding: 0 30rpx;
  font-size: 36rpx;
  text-align: center;
  background-color: #fff;
  box-sizing: border-box;
  direction: ltr;
}

.code-input:focus {
  border-color: var(--theme-main-color, #ff6b35);
}

.captcha-display {
  background-color: #fff;
  border-radius: 10rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  height: 100rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-sizing: border-box;
}

.captcha-image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

.loading-captcha {
  color: #999;
  font-size: 24rpx;
}

.btn-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  width: 100%;
}

.verify-btn {
  background-color: var(--theme-main-color, #ff6b35);
  color: #fff;
  border: none;
  border-radius: 10rpx;
  height: 100rpx;
  font-size: 36rpx;
  width: 100% !important;
  box-sizing: border-box;
}

.verify-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 营业执照展示部分样式 */
.license-section .header {
  background-color: #fff;
  padding: 40rpx;
  border-bottom: 1rpx solid #e0e0e0;
  margin-bottom: 0;
}

.license-container {
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.license-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-bottom: 40rpx;
}

.license-item {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2px solid #f5f5f5;
  padding: 10rpx;
  box-sizing: border-box;
}

.license-image {
  width: 100%;
  min-height: 400rpx;
  max-height: 600rpx;
  background-color: #f9f9f9;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
}
