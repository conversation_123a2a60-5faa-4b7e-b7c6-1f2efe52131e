const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isVerified: false, // 是否已验证通过
    captchaImage: "", // 验证码图片base64
    inputCode: "", // 用户输入的验证码
    licenseData: [], // 营业执照数据
    allUrls: [], // 所有图片URL
    restaurantId: "", // 餐厅ID
    lang: {},
    langId: 1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      lang: app.getWord(),
      langId: app.globalData.langId,
    });

    // 获取餐厅ID
    if (options.restaurantId) {
      this.setData({
        restaurantId: options.restaurantId,
      });
    }
    this.selectComponent("#loading").showLoading("while");
    // 检查是否已经验证过
    this.checkVerificationStatus();
  },

  /**
   * 模拟检查验证状态的接口
   */
  checkVerificationStatus: function () {
    const that = this;
    const restaurantId = that.data.restaurantId;
    app.GET({
      url: `/smart/v2/restaurant/license-captcha?restaurant_id=${restaurantId}`,
      success: function (res) {
        // 如果未登录
        if (res.data.status == 401) {
          wx.navigateTo({
            url: "/pages/login/wechatLogin",
          });
          return;
        }
        if (res?.data?.data?.success) {
          const captchaImage = res.data.data.captcha;
          const license = res.data.data.license || [];
          const allUrls = license.map((item) => item.big_image);

          that.setData({
            captchaImage: captchaImage,
            inputCode: "",
            licenseData: license,
            allUrls: allUrls,
          });

          if (captchaImage === "" && res.data.data.success) {
            wx.setNavigationBarTitle({
              title: that.data.lang.qualification,
            });
            that.setData({
              isVerified: true,
            });
          }
        } else if (res?.statusCode == 404) {
          wx.navigateBack({
            delta: 1,
          });
        } else {
          that
            .selectComponent("#toast")
            .showToast(res?.data?.data?.error || res?.data?.msg);
          setTimeout(() => {
            wx.navigateBack({
              delta: 1,
            });
          }, 1000);
        }
      },
      fail: function (err) {
        console.log(err);
      },
      complete: function () {
        that.selectComponent("#loading").hideLoading();
      },
    });
  },

  /**
   * 监听输入框变化
   */
  onCodeInput: function (e) {
    this.setData({
      inputCode: e.detail.value,
    });
  },

  /**
   * 验证输入的验证码
   */
  onVerify: function () {
    const { inputCode } = this.data;

    if (!inputCode || inputCode.length < 1) {
      that.selectComponent("#toast").showToast("请输入验证码");

      return;
    }

    // 这里应该调用验证接口
    // 现在先模拟验证成功
    const that = this;
    that.selectComponent("#loading").showLoading("center");

    // 模拟验证接口调用
    // 实际项目中应该调用验证接口
    app.POST({
      url: "/smart/v2/restaurant/license-captcha-check",
      params: {
        restaurant_id: that.data.restaurantId,
        captcha: inputCode,
      },
      success: function (res) {
        // 如果未登录
        if (res.data.status == 401) {
          wx.navigateTo({
            url: "/pages/login/wechatLogin",
          });
          return;
        }
        if (res?.data?.data?.success) {
          const captchaImage = res.data.data.captcha;
          const license = res.data.data.license || [];
          const allUrls = license.map((item) => item.big_image);

          that.setData({
            captchaImage: captchaImage,
            inputCode: "",
            licenseData: license,
            allUrls: allUrls,
          });

          if (captchaImage === "" && res.data.data.success) {
            wx.setNavigationBarTitle({
              title: that.data.lang.qualification,
            });
            that.setData({
              isVerified: true,
            });
          }
        } else {
          // 验证失败
          console.log("验证失败");

          that.selectComponent("#toast").showToast(res.data.msg);
        }
      },
      fail: function (err) {
        console.log(err);
      },
      complete: function () {
        that.selectComponent("#loading").hideLoading();
      },
    });
  },

  /**
   * 验证成功处理
   */
  onVerifySuccess: function () {
    this.setData({
      isVerified: true,
    });
    wx.setNavigationBarTitle({
      title: this.data.lang.qualification,
    });

    this.selectComponent("#toast").showToast(this.data.lang.success);
  },

  /**
   * 刷新验证码
   */
  refreshCaptcha: function () {
    this.selectComponent("#loading").showLoading("center");
    this.checkVerificationStatus();
  },

  /**
   * 预览图片
   */
  previewImage: function (e) {
    const current = e.currentTarget.dataset.current;
    const urls = e.currentTarget.dataset.urls;

    wx.previewImage({
      current: current,
      urls: urls,
    });
  },
  //登录成功后回调函数
  successLoginCallback() {
    this.selectComponent("#loading").showLoading("center");
    this.checkVerificationStatus();
  },
});
