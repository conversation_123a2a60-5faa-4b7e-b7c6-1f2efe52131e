---
description: 微信小程序到Flutter的迁移规则
globs: 
alwaysApply: true
---
# 微信小程序到Flutter迁移规则

# 回复
- 请你中文回复
- 不得不询问删除文件
- 尽量不改变原来的逻辑
- 复用项目已有的逻辑
- 响应式布局为主

## 项目目录
- 微信小程序目录: `/Users/<USER>/app/weixin_mini_to_flutter/mulazimmini`
- Flutter项目目录: `/Users/<USER>/app/weixin_mini_to_flutter/mulazim-user-app`

## 目标
 - 给予给定的微信小程序代码，给出相关的 flutter 代码

## 项目结构映射

### 核心层（Core Layer）
- `core` -> `lib/core/`
  - `network/`: 网络请求处理
  - `config/`: 配置文件
  - `storage/`: 本地存储处理
  - `providers/`: 全局状态管理
  - `navigation/`: 路由导航
  - `utils/`: 工具类
  - `init/`: 初始化配置
  - `widgets/`: 全局组件

### 数据层（Data Layer）
- `data/` -> `lib/data/`
  - `models/`: 数据模型
  - `repositories/`: 数据仓库/API 请求，使用 [api_result.dart](mdc:mulazim-user-app/lib/data/models/result/api_result.dart)返回数据
  - `datasources/`: 数据源

### 功能层（Feature Layer）
- `pages/` -> `lib/features/`
  - `services/`: 业务层（Business），调用repositories，允许复用
    - 数据访问逻辑
  - `pages/`: 表现层（UI）
    - `widgets/`: 页面组件, 页面组件按组件化类放到widges目录，有效利用ConsumetWidget 最小颗粒度实现精准重建build，减少build重建。
    - `*_state.dart`: 页面状态数据
    - `*_page.dart`: 页面/视图层
    - `*_controller.dart`: 控制层（使用@riverpod注解），不允许page以外复用

### 路由配置
- `routes/` -> `lib/routes/`
  - `app_router.dart`: 应用路由配置
  - `paths.dart`: AppPaths 类，应用路由路径

### 国际化层（I18n Layer）
- `utils/word.js` -> `lib/l10n/`
  - `intl_zh.arb`: 中文翻译文件
  - `intl_en.arb`: 维吾尔语翻译文件

### 状态类和模型类
- 要明确定义
- 禁止使用freezed 和 json_serializable

## 迁移指南

### 1. 状态管理
- 将微信小程序的globalData替换为Riverpod状态管理
- 使用@riverpod注解进行状态控制器管理
- 实现适当的依赖注入

### 2. 网络层
- 将wx.request转换为Flutter的http/dio
- 实现适当的错误处理
- 使用拦截器处理通用请求头
- 正确处理身份验证

### 3. 存储
- 将wx.setStorageSync替换为Flutter的shared_preferences
- 实现适当的数据序列化
- 处理敏感数据的安全存储

### 4. UI组件
- 将WXML转换为Flutter widgets
- 将WXSS替换为Flutter样式
- 实现响应式设计
- 处理平台特定的UI差异
- 尺寸转换规则：
  - 微信小程序和Flutter的尺寸比例为2:1
  - 所有尺寸（宽度、高度、内边距、外边距等）需要除以2
  - 字体大小也需要除以2
  - 示例：
    ```dart
    // 微信小程序
    width: 100rpx;
    height: 80rpx;
    font-size: 32rpx;
    margin: 20rpx;
    
    // Flutter
    width: 50.w,
    height: 40.h,
    fontSize: 16.sp,
    margin: EdgeInsets.all(10.r),
    ``` 

### 5. 导航
- 将wx.navigateTo替换为Flutter导航
- 实现适当的路由管理
- 处理深度链接

### 6. 国际化
- 将word.js中的翻译键值对迁移到.arb文件中
- 使用Flutter的intl包进行国际化
- 配置pubspec.yaml启用国际化生成
- 使用S.current访问翻译内容

### 7. 平台特性
- 将微信API替换为Flutter等效实现
- 处理平台特定功能
- 实现适当的权限处理

## 代码组织

### 功能模块结构
```
lib/features/feature_name/
  ├── services/
  │   └── feature_service.dart
  └── pages/
      ├── widgets/
      ├── feature_state.dart
      ├── feature_page.dart
      └── feature_controller.dart
```

### 核心模块结构
```
lib/core/
  ├── network/
  ├── storage/
  ├── providers/
  ├── navigation/
  ├── utils/
  ├── init/
  └── widgets/
```

### 国际化模块结构
```
lib/l10n/
  ├── intl_zh.arb    # 中文翻译
  ├── intl_en.arb    # ug翻译
```

## 迁移流程

1. **分析阶段**
   - 映射现有功能
   - 识别依赖关系
   - 规划架构

2. **核心迁移**
   - 搭建项目结构
   - 实现核心服务
   - 设置Riverpod状态管理

3. **功能迁移**
   - 逐个迁移功能
   - 实现UI组件
   - 处理状态管理

4. **测试与优化**
   - 单元测试
   - 集成测试
   - UI测试
   - 性能优化

## 最佳实践

1. **代码风格**
   - 遵循Flutter风格指南
   - 使用适当的命名规范
   - 实现适当的错误处理
   - 类名称和文件名称一致，符合命名规范

2. **性能**
   - 优化widget重建
   - 处理内存管理
   - 实现适当的缓存

3. **安全性**
   - 安全存储实现
   - API安全性
   - 数据加密

4. **维护性**
   - 适当的文档
   - 代码注释
   - 版本控制实践

## 常见模式

### API调用
```dart
// 微信小程序
wx.request({
  url: url,
  method: 'GET',
  success: (res) => {},
  fail: (err) => {}
})

// Flutter 在repository riverpod 定义依赖注入
Future<LocationListModel?> getListByLocation(Map<String, dynamic> param) async {
  final response = await _apiClient.get(Api.address.listByLocation, params: param);
  return LocationListModel.fromJson(response);
}
```

### 存储
```dart
// 微信小程序
wx.setStorageSync('key', value)

// Flutter
LocalStorageRepository riverpod 依赖注入
_localStorageRepository.getToken()
```

### 导航
```dart
// 微信小程序
wx.navigateTo({
  url: '/pages/index/index'
})

// Flutter Grouter
context.push(
  AppPaths.restaurantDetailPage,
   extra: {
    'restaurantId': shopList![index].restaurantId,
    },
);
```

### 状态管理
```dart
// 微信小程序
App({
  globalData: {
    user: null
  }
})

// Flutter
@riverpod
class UserState extends _$UserState {
  @override
  User? build() => null;
  
  void setUser(User user) => state = user;
}
```

### 国际化
 - 不得执行flutter gen-l10n 命令，我自会处理
```dart
// 微信小程序
- [word.js](mdc:mulazimmini/pages/lang/word.js)
var zh = {
  local_area: "本地客服",
  general: "总部热线",
  about_suggestions: "在线客服",
  integral_is: "剩余积分",
  integral_info: "积分说明",
  integral_title: "积分记录",
  red_packet_page_title: "红包"
}
var ug = {
  local_area: "مۇلازىمەت تېلېفونى",
  general: "باش شىركەت",
  about_suggestions: "تەكلىپ - پىكىر",
  integral_is: "ھازىرقى جۇغلانما نومۇرىڭىز",
  integral_info: "جۇغلانما نومۇر چۈشەندۈرۈلۈشى",
  integral_title: "جۇغلانما نومۇر خاتىرىسى",
  red_packet_page_title: "قىزىل بولاق"
}
// 页面中使用
lang.local_area

// Flutter 
// lib/l10n/intl_zh.arb
- [intl_zh.arb](mdc:mulazim-user-app/lib/l10n/intl_zh.arb)
{
  "local_area": "本地客服",
  "general": "总部热线",
  "about_suggestions": "在线客服",
  "integral_is": "剩余积分",
  "integral_info": "积分说明",
  "integral_title": "积分记录",
  "red_packet_page_title": "红包"
}
// lib/l10n/intl_en.arb
- [intl_en.arb](mdc:mulazim-user-app/lib/l10n/intl_en.arb)
{
  "local_area": "مۇلازىمەت تېلېفونى",
  "general": "باش شىركەت",
  "about_suggestions": "تەكلىپ - پىكىر",
  "integral_is": "ھازىرقى جۇغلانما نومۇرىڭىز",
  "integral_info": "جۇغلانما نومۇر چۈشەندۈرۈلۈشى",
  "integral_title": "جۇغلانما نومۇر خاتىرىسى",
  "red_packet_page_title": "قىزىل بولاق"
}
// 页面中使用
S.current.local_area
```

### 国际化配置
```yaml
# pubspec.yaml
flutter:
  generate: true  # 启用代码生成

flutter_intl:
  enabled: true   # 启用国际化支持
  main_locale: "zh"  # 主要语言为中文
  arb_dir: "lib/l10n"  # arb文件目录
  output_dir: "lib/generated"  # 生成的代码目录
  template_arb_file: "intl_zh.arb"  # 模板文件
  output_class: "S"  # 生成的类名
```

### 国际化文件结构
```
lib/l10n/
  ├── intl_zh.arb    # 中文翻译
  └── intl_en.arb    # ug翻译
```

### 国际化使用规范
1. **翻译键值规范**
   - 使用小写字母和下划线
   - 键名要有意义，表达清晰
   - 相同含义的文本使用相同的键

2. **翻译文件管理**
   - 中文翻译在 `intl_zh.arb`
   - 维语翻译在 `intl_en.arb`
   - 不要创建其他arb文件
   - 新增翻译时两个文件同时添加

3. **代码中使用**
   - 使用 `S.current.key` 访问翻译
   - 不要硬编码文本
   - 使用const字符串存储键名

4. **翻译同步**
   - 从word.js迁移时保持维语翻译一致
   - 优先使用word.js中的维语翻译
   - 确保中文和维语的意思对应

   
## 资源文件迁移规则

### 目录结构映射
```
# 微信小程序
/resources/
  ├── img/
  │   ├── tel.png
  │   ├── share.png
  │   └── ...
  └── icons/
      ├── home.png
      └── ...

# Flutter
/assets/
  ├── images/
  │   ├── mine/           # 个人中心模块
  │   │   ├── tel.png
  │   │   └── share.png
  │   ├── home/           # 首页模块
  │   │   └── banner.png
  │   └── order/          # 订单模块
  │       └── status.png
  ├── icons/              # 通用图标
  │   └── home.png
  └── fonts/              # 字体文件
      └── custom.ttf
```

### 资源引用转换
```dart
// 微信小程序
<image src='/resources/img/tel.png' />

// Flutter
// 1. 先按业务模块分类复制文件
// 例如：tel.png用于个人中心模块，复制到 assets/images/mine/tel.png

// 2. 在pubspec.yaml中声明资源
flutter:
  assets:
    - assets/images/
    - assets/images/mine/
    - assets/images/home/
    - assets/images/order/
    - assets/icons/

// 3. 在Flutter中引用
Image.asset(
  'assets/images/mine/tel.png',
  width: 24.w,
  height: 24.h,
)

// 使用CachedNetworkImage加载网络图片
CachedNetworkImage(
  imageUrl: 'https://example.com/image.png',
  width: 24.w,
  height: 24.h,
)
```

## 状态管理规范

### 页面状态管理结构
```
lib/features/feature_name/pages/page_name/
  ├── page_name_page.dart      # UI页面
  ├── page_name_state.dart     # 状态定义
  └── page_name_controller.dart # 状态控制器
```

### 状态类定义规范
```dart
// page_name_state.dart
class PageNameState {
  /// 构造函数
  const PageNameState({
    this.isLoading = false,
    this.error,
    // ... 其他状态字段
  });

  /// 是否加载中
  final bool isLoading;

  /// 错误信息
  final String? error;

  /// 拷贝方法
  PageNameState copyWith({
    bool? isLoading,
    String? error,
  }) {
    return PageNameState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
```

### 控制器定义规范
```dart
// page_name_controller.dart
@riverpod
class PageNameController extends _$PageNameController {
  @override
  PageNameState build() {
    return const PageNameState();
  }

  /// 更新状态的方法
  void updateState() {
    state = state.copyWith(
      isLoading: true,
    );
  }

  /// 处理业务逻辑的方法
  Future<void> handleBusiness() async {
    state = state.copyWith(isLoading: true);
    try {
      // 调用依赖注入的service或repository
      final repository = ref.read(repositoryProvider);
      await repository.doSomething();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
```

### 页面使用规范
```dart
// page_name_page.dart
class PageNamePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听状态
    final state = ref.watch(pageNameControllerProvider);
    // 获取控制器
    final controller = ref.read(pageNameControllerProvider.notifier);

    return Scaffold(
      body: state.isLoading
          ? const LoadingWidget()
          : // ... 其他UI逻辑
    );
  }
}
```

### 依赖注入定义规范
```dart
// repository.dart
@riverpod
Repository repository(RepositoryRef ref) {
  return Repository(
    apiClient: ref.watch(apiClientProvider),
  );
}

// service.dart
@riverpod
Service service(ServiceRef ref) {
  return Service(
    repository: ref.watch(repositoryProvider),
  );
}
```

### 状态管理最佳实践
1. **状态定义**
   - 状态类使用不可变（immutable）对象
   - 提供copyWith方法用于状态更新
   - 状态字段使用final修饰
   - 提供默认构造函数

2. **控制器职责**
   - 负责状态更新逻辑
   - 处理业务逻辑
   - 调用依赖注入的服务
   - 错误处理和状态恢复

3. **页面职责**
   - 仅负责UI展示
   - 通过ref.watch监听状态
   - 通过ref.read获取控制器
   - 将用户操作委托给控制器

4. **依赖注入原则**
   - Service和Repository使用依赖注入
   - 控制器通过ref.read获取依赖
   - 避免在页面中直接使用Service或Repository